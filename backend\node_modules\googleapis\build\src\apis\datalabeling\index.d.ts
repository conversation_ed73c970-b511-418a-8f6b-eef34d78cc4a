/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { datalabeling_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof datalabeling_v1beta1.Datalabeling;
};
export declare function datalabeling(version: 'v1beta1'): datalabeling_v1beta1.Datalabeling;
export declare function datalabeling(options: datalabeling_v1beta1.Options): datalabeling_v1beta1.Datalabeling;
declare const auth: AuthPlus;
export { auth };
export { datalabeling_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
