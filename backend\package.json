{"name": "health-connect-backend", "version": "1.0.0", "description": "Backend API for HealthConnect application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "python-shell": "^5.0.0", "socket.io": "^4.7.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "health", "api"], "author": "HealthConnect Team", "license": "MIT"}