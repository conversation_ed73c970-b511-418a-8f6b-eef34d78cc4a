import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useHealthData } from '../contexts/HealthDataContext';

const QuickActions = () => {
  const navigate = useNavigate();
  const { generatePrediction, addHealthData } = useHealthData();

  const handleGeneratePrediction = async () => {
    const result = await generatePrediction('comprehensive');
    if (result.success) {
      navigate('/predictions');
    }
  };

  const handleAddTodayData = () => {
    navigate('/health-data');
  };

  const handleSetGoal = () => {
    navigate('/goals');
  };

  const handleChatWithBot = () => {
    navigate('/chatbot');
  };

  const quickActions = [
    {
      id: 'add-data',
      title: 'Log Health Data',
      description: 'Add today\'s health metrics',
      icon: '📊',
      color: '#667eea',
      action: handleAddTodayData
    },
    {
      id: 'generate-prediction',
      title: 'Health Analysis',
      description: 'Get AI health insights',
      icon: '🔮',
      color: '#9b59b6',
      action: handleGeneratePrediction
    },
    {
      id: 'set-goal',
      title: 'Set New Goal',
      description: 'Create a fitness goal',
      icon: '🎯',
      color: '#27ae60',
      action: handleSetGoal
    },
    {
      id: 'chat-bot',
      title: 'Ask Health Assistant',
      description: 'Get health advice',
      icon: '🤖',
      color: '#f39c12',
      action: handleChatWithBot
    }
  ];

  return (
    <div className="quick-actions">
      <div className="actions-grid">
        {quickActions.map((action) => (
          <button
            key={action.id}
            className="action-button"
            onClick={action.action}
            style={{ '--action-color': action.color }}
          >
            <div className="action-icon">{action.icon}</div>
            <div className="action-content">
              <h4 className="action-title">{action.title}</h4>
              <p className="action-description">{action.description}</p>
            </div>
            <div className="action-arrow">→</div>
          </button>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="quick-stats">
        <h4>Quick Stats</h4>
        <div className="stats-row">
          <div className="stat-item">
            <span className="stat-icon">🏃</span>
            <div className="stat-info">
              <span className="stat-label">This Week</span>
              <span className="stat-value">5 workouts</span>
            </div>
          </div>
          
          <div className="stat-item">
            <span className="stat-icon">🎯</span>
            <div className="stat-info">
              <span className="stat-label">Goals</span>
              <span className="stat-value">3 active</span>
            </div>
          </div>
          
          <div className="stat-item">
            <span className="stat-icon">📈</span>
            <div className="stat-info">
              <span className="stat-label">Streak</span>
              <span className="stat-value">7 days</span>
            </div>
          </div>
        </div>
      </div>

      {/* Health Reminders */}
      <div className="health-reminders">
        <h4>Reminders</h4>
        <div className="reminder-list">
          <div className="reminder-item">
            <span className="reminder-icon">💧</span>
            <span className="reminder-text">Drink water (2L today)</span>
            <button className="reminder-check">✓</button>
          </div>
          
          <div className="reminder-item">
            <span className="reminder-icon">🚶</span>
            <span className="reminder-text">Take a 10-min walk</span>
            <button className="reminder-check">✓</button>
          </div>
          
          <div className="reminder-item">
            <span className="reminder-icon">😴</span>
            <span className="reminder-text">Bedtime in 2 hours</span>
            <button className="reminder-snooze">⏰</button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .quick-actions {
          display: flex;
          flex-direction: column;
          gap: 25px;
        }

        .actions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
        }

        .action-button {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.9);
          border: 2px solid rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: left;
          width: 100%;
        }

        .action-button:hover {
          background: rgba(255, 255, 255, 1);
          border-color: var(--action-color);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .action-icon {
          font-size: 2rem;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .action-content {
          flex: 1;
        }

        .action-title {
          margin: 0 0 4px 0;
          font-size: 1rem;
          font-weight: 600;
          color: #2c3e50;
        }

        .action-description {
          margin: 0;
          font-size: 0.85rem;
          color: #7f8c8d;
          line-height: 1.3;
        }

        .action-arrow {
          font-size: 1.2rem;
          color: var(--action-color);
          font-weight: bold;
          margin-left: 10px;
          transition: transform 0.3s ease;
        }

        .action-button:hover .action-arrow {
          transform: translateX(3px);
        }

        .quick-stats h4,
        .health-reminders h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 1.1rem;
        }

        .stats-row {
          display: flex;
          gap: 15px;
          flex-wrap: wrap;
        }

        .stat-item {
          display: flex;
          align-items: center;
          background: rgba(102, 126, 234, 0.1);
          border: 1px solid rgba(102, 126, 234, 0.2);
          border-radius: 8px;
          padding: 12px;
          flex: 1;
          min-width: 120px;
        }

        .stat-icon {
          font-size: 1.3rem;
          margin-right: 10px;
        }

        .stat-info {
          display: flex;
          flex-direction: column;
        }

        .stat-label {
          font-size: 0.8rem;
          color: #7f8c8d;
          margin-bottom: 2px;
        }

        .stat-value {
          font-size: 0.9rem;
          font-weight: 600;
          color: #2c3e50;
        }

        .reminder-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .reminder-item {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.8);
          border: 1px solid rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          padding: 12px;
          transition: all 0.3s ease;
        }

        .reminder-item:hover {
          background: rgba(255, 255, 255, 0.95);
        }

        .reminder-icon {
          font-size: 1.2rem;
          margin-right: 12px;
        }

        .reminder-text {
          flex: 1;
          font-size: 0.9rem;
          color: #2c3e50;
        }

        .reminder-check,
        .reminder-snooze {
          background: none;
          border: 2px solid #27ae60;
          border-radius: 50%;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.8rem;
        }

        .reminder-check {
          color: #27ae60;
        }

        .reminder-check:hover {
          background: #27ae60;
          color: white;
        }

        .reminder-snooze {
          border-color: #f39c12;
          color: #f39c12;
        }

        .reminder-snooze:hover {
          background: #f39c12;
          color: white;
        }

        @media (max-width: 768px) {
          .actions-grid {
            grid-template-columns: 1fr;
          }

          .action-button {
            padding: 12px;
          }

          .action-title {
            font-size: 0.95rem;
          }

          .action-description {
            font-size: 0.8rem;
          }

          .stats-row {
            flex-direction: column;
          }

          .stat-item {
            min-width: auto;
          }
        }
      `}</style>
    </div>
  );
};

export default QuickActions;
