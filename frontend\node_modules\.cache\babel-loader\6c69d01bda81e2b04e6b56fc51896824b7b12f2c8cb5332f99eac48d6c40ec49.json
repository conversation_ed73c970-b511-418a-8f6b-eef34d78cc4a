{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Predictions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Predictions = () => {\n  _s();\n  const {\n    predictions,\n    fetchPredictions,\n    generatePrediction,\n    loading\n  } = useHealthData();\n  const [generating, setGenerating] = useState(false);\n  useEffect(() => {\n    fetchPredictions();\n  }, []);\n  const handleGeneratePrediction = async () => {\n    setGenerating(true);\n    await generatePrediction('comprehensive');\n    setGenerating(false);\n  };\n  const getRiskColor = riskLevel => {\n    switch (riskLevel) {\n      case 'very_low':\n      case 'low':\n        return '#27ae60';\n      case 'moderate':\n        return '#f39c12';\n      case 'high':\n      case 'very_high':\n        return '#e74c3c';\n      default:\n        return '#667eea';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"predictions-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Health Predictions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"AI-powered health insights based on your fitness data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleGeneratePrediction,\n        disabled: generating,\n        children: generating ? 'Generating...' : 'Generate New Analysis'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 9\n    }, this) : predictions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-predictions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-predictions-icon\",\n        children: \"\\uD83D\\uDD2E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No Health Predictions Yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Generate your first AI health analysis to get personalized insights!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: handleGeneratePrediction,\n        children: \"Generate Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"predictions-list\",\n      children: predictions.map(prediction => {\n        var _prediction$overallRi;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prediction-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prediction-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Health Analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"prediction-date\",\n                children: new Date(prediction.createdAt).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"overall-risk\",\n              style: {\n                backgroundColor: getRiskColor(prediction.overallRiskLevel)\n              },\n              children: [((_prediction$overallRi = prediction.overallRiskLevel) === null || _prediction$overallRi === void 0 ? void 0 : _prediction$overallRi.replace('_', ' ').toUpperCase()) || 'UNKNOWN', \" RISK\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), prediction.predictions && prediction.predictions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"predictions-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Risk Assessment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"predictions-grid\",\n              children: prediction.predictions.map((pred, index) => {\n                var _pred$riskLevel;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"prediction-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"prediction-condition\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: pred.condition\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 80,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"risk-badge\",\n                      style: {\n                        backgroundColor: getRiskColor(pred.riskLevel)\n                      },\n                      children: (_pred$riskLevel = pred.riskLevel) === null || _pred$riskLevel === void 0 ? void 0 : _pred$riskLevel.replace('_', ' ').toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 81,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"prediction-description\",\n                    children: pred.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"prediction-confidence\",\n                    children: [\"Confidence: \", pred.confidence, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 25\n                  }, this), pred.recommendations && pred.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"prediction-recommendations\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Recommendations:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 94,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      children: pred.recommendations.slice(0, 3).map((rec, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: rec\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 97,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 95,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 17\n          }, this), prediction.recommendations && prediction.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recommendations-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Personalized Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"recommendations-grid\",\n              children: prediction.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"recommendation-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"recommendation-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: rec.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `priority-badge priority-${rec.priority}`,\n                    children: rec.priority\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"recommendation-description\",\n                  children: rec.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 25\n                }, this), rec.actionItems && rec.actionItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-items\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Action Items:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: rec.actionItems.map((action, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: action\n                    }, i, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"recommendation-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"\\uD83D\\uDCC8 \", rec.expectedBenefit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"\\u23F0 \", rec.timeframe]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this), prediction.modelInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"model-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Analysis Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"model-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Model: \", prediction.modelInfo.modelName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Version: \", prediction.modelInfo.modelVersion]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Algorithm: \", prediction.modelInfo.algorithm]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 17\n          }, this)]\n        }, prediction._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .predictions-page { padding: 0; }\n        .page-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 30px; }\n        .page-header h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .page-header p { margin: 0; color: #7f8c8d; }\n        .no-predictions { text-align: center; padding: 60px 20px; color: #7f8c8d; }\n        .no-predictions-icon { font-size: 4rem; margin-bottom: 20px; }\n        .predictions-list { display: flex; flex-direction: column; gap: 30px; }\n        .prediction-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .prediction-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 25px; }\n        .prediction-header h3 { margin: 0; color: #2c3e50; }\n        .prediction-date { margin: 5px 0 0 0; color: #7f8c8d; font-size: 0.9rem; }\n        .overall-risk { padding: 8px 16px; border-radius: 20px; color: white; font-size: 0.8rem; font-weight: 700; letter-spacing: 0.5px; }\n        .predictions-section, .recommendations-section, .model-info { margin-bottom: 25px; }\n        .predictions-section h4, .recommendations-section h4, .model-info h4 { margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1rem; }\n        .predictions-grid, .recommendations-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n        .prediction-item, .recommendation-item { background: rgba(102, 126, 234, 0.05); border: 1px solid rgba(102, 126, 234, 0.1); border-radius: 12px; padding: 20px; }\n        .prediction-condition, .recommendation-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px; }\n        .prediction-condition h5, .recommendation-header h5 { margin: 0; color: #2c3e50; }\n        .risk-badge, .priority-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }\n        .priority-high { background: #e74c3c; }\n        .priority-medium { background: #f39c12; }\n        .priority-low { background: #27ae60; }\n        .prediction-description, .recommendation-description { color: #2c3e50; margin-bottom: 10px; font-size: 0.9rem; line-height: 1.4; }\n        .prediction-confidence { font-size: 0.85rem; color: #667eea; font-weight: 600; margin-bottom: 10px; }\n        .prediction-recommendations, .action-items { margin-top: 10px; }\n        .prediction-recommendations ul, .action-items ul { margin: 5px 0 0 20px; padding: 0; }\n        .prediction-recommendations li, .action-items li { margin-bottom: 5px; color: #2c3e50; font-size: 0.85rem; }\n        .recommendation-meta { display: flex; gap: 15px; margin-top: 10px; font-size: 0.8rem; color: #7f8c8d; }\n        .model-details { display: flex; gap: 20px; flex-wrap: wrap; font-size: 0.85rem; color: #7f8c8d; }\n        @media (max-width: 768px) {\n          .page-header { flex-direction: column; gap: 15px; align-items: stretch; }\n          .predictions-grid, .recommendations-grid { grid-template-columns: 1fr; }\n          .prediction-condition, .recommendation-header { flex-direction: column; gap: 10px; }\n          .model-details { flex-direction: column; gap: 5px; }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Predictions, \"KuSvRS78WDCgu1MbZhfHHlhyb3I=\", false, function () {\n  return [useHealthData];\n});\n_c = Predictions;\nexport default Predictions;\nvar _c;\n$RefreshReg$(_c, \"Predictions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHealthData", "jsxDEV", "_jsxDEV", "Predictions", "_s", "predictions", "fetchPredictions", "generatePrediction", "loading", "generating", "setGenerating", "handleGeneratePrediction", "getRiskColor", "riskLevel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "length", "map", "prediction", "_prediction$overallRi", "Date", "createdAt", "toLocaleDateString", "style", "backgroundColor", "overallRiskLevel", "replace", "toUpperCase", "pred", "index", "_pred$riskLevel", "condition", "description", "confidence", "recommendations", "slice", "rec", "i", "title", "priority", "actionItems", "action", "expectedBenefit", "timeframe", "modelInfo", "modelName", "modelVersion", "algorithm", "_id", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Predictions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst Predictions = () => {\n  const { predictions, fetchPredictions, generatePrediction, loading } = useHealthData();\n  const [generating, setGenerating] = useState(false);\n\n  useEffect(() => {\n    fetchPredictions();\n  }, []);\n\n  const handleGeneratePrediction = async () => {\n    setGenerating(true);\n    await generatePrediction('comprehensive');\n    setGenerating(false);\n  };\n\n  const getRiskColor = (riskLevel) => {\n    switch (riskLevel) {\n      case 'very_low': case 'low': return '#27ae60';\n      case 'moderate': return '#f39c12';\n      case 'high': case 'very_high': return '#e74c3c';\n      default: return '#667eea';\n    }\n  };\n\n  return (\n    <div className=\"predictions-page\">\n      <div className=\"page-header\">\n        <div>\n          <h1>Health Predictions</h1>\n          <p>AI-powered health insights based on your fitness data</p>\n        </div>\n        <button \n          className=\"btn btn-primary\"\n          onClick={handleGeneratePrediction}\n          disabled={generating}\n        >\n          {generating ? 'Generating...' : 'Generate New Analysis'}\n        </button>\n      </div>\n\n      {loading ? (\n        <div className=\"loading-spinner\"></div>\n      ) : predictions.length === 0 ? (\n        <div className=\"no-predictions\">\n          <div className=\"no-predictions-icon\">🔮</div>\n          <h3>No Health Predictions Yet</h3>\n          <p>Generate your first AI health analysis to get personalized insights!</p>\n          <button className=\"btn btn-primary\" onClick={handleGeneratePrediction}>\n            Generate Analysis\n          </button>\n        </div>\n      ) : (\n        <div className=\"predictions-list\">\n          {predictions.map((prediction) => (\n            <div key={prediction._id} className=\"prediction-card\">\n              <div className=\"prediction-header\">\n                <div>\n                  <h3>Health Analysis</h3>\n                  <p className=\"prediction-date\">\n                    {new Date(prediction.createdAt).toLocaleDateString()}\n                  </p>\n                </div>\n                <span \n                  className=\"overall-risk\"\n                  style={{ backgroundColor: getRiskColor(prediction.overallRiskLevel) }}\n                >\n                  {prediction.overallRiskLevel?.replace('_', ' ').toUpperCase() || 'UNKNOWN'} RISK\n                </span>\n              </div>\n\n              {prediction.predictions && prediction.predictions.length > 0 && (\n                <div className=\"predictions-section\">\n                  <h4>Risk Assessment</h4>\n                  <div className=\"predictions-grid\">\n                    {prediction.predictions.map((pred, index) => (\n                      <div key={index} className=\"prediction-item\">\n                        <div className=\"prediction-condition\">\n                          <h5>{pred.condition}</h5>\n                          <span \n                            className=\"risk-badge\"\n                            style={{ backgroundColor: getRiskColor(pred.riskLevel) }}\n                          >\n                            {pred.riskLevel?.replace('_', ' ').toUpperCase()}\n                          </span>\n                        </div>\n                        <p className=\"prediction-description\">{pred.description}</p>\n                        <div className=\"prediction-confidence\">\n                          Confidence: {pred.confidence}%\n                        </div>\n                        {pred.recommendations && pred.recommendations.length > 0 && (\n                          <div className=\"prediction-recommendations\">\n                            <strong>Recommendations:</strong>\n                            <ul>\n                              {pred.recommendations.slice(0, 3).map((rec, i) => (\n                                <li key={i}>{rec}</li>\n                              ))}\n                            </ul>\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {prediction.recommendations && prediction.recommendations.length > 0 && (\n                <div className=\"recommendations-section\">\n                  <h4>Personalized Recommendations</h4>\n                  <div className=\"recommendations-grid\">\n                    {prediction.recommendations.map((rec, index) => (\n                      <div key={index} className=\"recommendation-item\">\n                        <div className=\"recommendation-header\">\n                          <h5>{rec.title}</h5>\n                          <span className={`priority-badge priority-${rec.priority}`}>\n                            {rec.priority}\n                          </span>\n                        </div>\n                        <p className=\"recommendation-description\">{rec.description}</p>\n                        {rec.actionItems && rec.actionItems.length > 0 && (\n                          <div className=\"action-items\">\n                            <strong>Action Items:</strong>\n                            <ul>\n                              {rec.actionItems.map((action, i) => (\n                                <li key={i}>{action}</li>\n                              ))}\n                            </ul>\n                          </div>\n                        )}\n                        <div className=\"recommendation-meta\">\n                          <span>📈 {rec.expectedBenefit}</span>\n                          <span>⏰ {rec.timeframe}</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {prediction.modelInfo && (\n                <div className=\"model-info\">\n                  <h4>Analysis Details</h4>\n                  <div className=\"model-details\">\n                    <span>Model: {prediction.modelInfo.modelName}</span>\n                    <span>Version: {prediction.modelInfo.modelVersion}</span>\n                    <span>Algorithm: {prediction.modelInfo.algorithm}</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      <style jsx>{`\n        .predictions-page { padding: 0; }\n        .page-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 30px; }\n        .page-header h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .page-header p { margin: 0; color: #7f8c8d; }\n        .no-predictions { text-align: center; padding: 60px 20px; color: #7f8c8d; }\n        .no-predictions-icon { font-size: 4rem; margin-bottom: 20px; }\n        .predictions-list { display: flex; flex-direction: column; gap: 30px; }\n        .prediction-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .prediction-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 25px; }\n        .prediction-header h3 { margin: 0; color: #2c3e50; }\n        .prediction-date { margin: 5px 0 0 0; color: #7f8c8d; font-size: 0.9rem; }\n        .overall-risk { padding: 8px 16px; border-radius: 20px; color: white; font-size: 0.8rem; font-weight: 700; letter-spacing: 0.5px; }\n        .predictions-section, .recommendations-section, .model-info { margin-bottom: 25px; }\n        .predictions-section h4, .recommendations-section h4, .model-info h4 { margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1rem; }\n        .predictions-grid, .recommendations-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n        .prediction-item, .recommendation-item { background: rgba(102, 126, 234, 0.05); border: 1px solid rgba(102, 126, 234, 0.1); border-radius: 12px; padding: 20px; }\n        .prediction-condition, .recommendation-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px; }\n        .prediction-condition h5, .recommendation-header h5 { margin: 0; color: #2c3e50; }\n        .risk-badge, .priority-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }\n        .priority-high { background: #e74c3c; }\n        .priority-medium { background: #f39c12; }\n        .priority-low { background: #27ae60; }\n        .prediction-description, .recommendation-description { color: #2c3e50; margin-bottom: 10px; font-size: 0.9rem; line-height: 1.4; }\n        .prediction-confidence { font-size: 0.85rem; color: #667eea; font-weight: 600; margin-bottom: 10px; }\n        .prediction-recommendations, .action-items { margin-top: 10px; }\n        .prediction-recommendations ul, .action-items ul { margin: 5px 0 0 20px; padding: 0; }\n        .prediction-recommendations li, .action-items li { margin-bottom: 5px; color: #2c3e50; font-size: 0.85rem; }\n        .recommendation-meta { display: flex; gap: 15px; margin-top: 10px; font-size: 0.8rem; color: #7f8c8d; }\n        .model-details { display: flex; gap: 20px; flex-wrap: wrap; font-size: 0.85rem; color: #7f8c8d; }\n        @media (max-width: 768px) {\n          .page-header { flex-direction: column; gap: 15px; align-items: stretch; }\n          .predictions-grid, .recommendations-grid { grid-template-columns: 1fr; }\n          .prediction-condition, .recommendation-header { flex-direction: column; gap: 10px; }\n          .model-details { flex-direction: column; gap: 5px; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Predictions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,WAAW;IAAEC,gBAAgB;IAAEC,kBAAkB;IAAEC;EAAQ,CAAC,GAAGR,aAAa,CAAC,CAAC;EACtF,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdO,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3CD,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMH,kBAAkB,CAAC,eAAe,CAAC;IACzCG,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAME,YAAY,GAAIC,SAAS,IAAK;IAClC,QAAQA,SAAS;MACf,KAAK,UAAU;MAAE,KAAK,KAAK;QAAE,OAAO,SAAS;MAC7C,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,MAAM;MAAE,KAAK,WAAW;QAAE,OAAO,SAAS;MAC/C;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/Bb,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1Bb,OAAA;QAAAa,QAAA,gBACEb,OAAA;UAAAa,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BjB,OAAA;UAAAa,QAAA,EAAG;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNjB,OAAA;QACEY,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAET,wBAAyB;QAClCU,QAAQ,EAAEZ,UAAW;QAAAM,QAAA,EAEpBN,UAAU,GAAG,eAAe,GAAG;MAAuB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELX,OAAO,gBACNN,OAAA;MAAKY,SAAS,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GACrCd,WAAW,CAACiB,MAAM,KAAK,CAAC,gBAC1BpB,OAAA;MAAKY,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bb,OAAA;QAAKY,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7CjB,OAAA;QAAAa,QAAA,EAAI;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClCjB,OAAA;QAAAa,QAAA,EAAG;MAAoE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC3EjB,OAAA;QAAQY,SAAS,EAAC,iBAAiB;QAACM,OAAO,EAAET,wBAAyB;QAAAI,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENjB,OAAA;MAAKY,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BV,WAAW,CAACkB,GAAG,CAAEC,UAAU;QAAA,IAAAC,qBAAA;QAAA,oBAC1BvB,OAAA;UAA0BY,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACnDb,OAAA;YAAKY,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCb,OAAA;cAAAa,QAAA,gBACEb,OAAA;gBAAAa,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBjB,OAAA;gBAAGY,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC3B,IAAIW,IAAI,CAACF,UAAU,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjB,OAAA;cACEY,SAAS,EAAC,cAAc;cACxBe,KAAK,EAAE;gBAAEC,eAAe,EAAElB,YAAY,CAACY,UAAU,CAACO,gBAAgB;cAAE,CAAE;cAAAhB,QAAA,GAErE,EAAAU,qBAAA,GAAAD,UAAU,CAACO,gBAAgB,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,SAAS,EAAC,OAC7E;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELK,UAAU,CAACnB,WAAW,IAAImB,UAAU,CAACnB,WAAW,CAACiB,MAAM,GAAG,CAAC,iBAC1DpB,OAAA;YAAKY,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCb,OAAA;cAAAa,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBjB,OAAA;cAAKY,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC9BS,UAAU,CAACnB,WAAW,CAACkB,GAAG,CAAC,CAACW,IAAI,EAAEC,KAAK;gBAAA,IAAAC,eAAA;gBAAA,oBACtClC,OAAA;kBAAiBY,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC1Cb,OAAA;oBAAKY,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCb,OAAA;sBAAAa,QAAA,EAAKmB,IAAI,CAACG;oBAAS;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzBjB,OAAA;sBACEY,SAAS,EAAC,YAAY;sBACtBe,KAAK,EAAE;wBAAEC,eAAe,EAAElB,YAAY,CAACsB,IAAI,CAACrB,SAAS;sBAAE,CAAE;sBAAAE,QAAA,GAAAqB,eAAA,GAExDF,IAAI,CAACrB,SAAS,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBJ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjB,OAAA;oBAAGY,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAEmB,IAAI,CAACI;kBAAW;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DjB,OAAA;oBAAKY,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,cACzB,EAACmB,IAAI,CAACK,UAAU,EAAC,GAC/B;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACLe,IAAI,CAACM,eAAe,IAAIN,IAAI,CAACM,eAAe,CAAClB,MAAM,GAAG,CAAC,iBACtDpB,OAAA;oBAAKY,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzCb,OAAA;sBAAAa,QAAA,EAAQ;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjCjB,OAAA;sBAAAa,QAAA,EACGmB,IAAI,CAACM,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACmB,GAAG,EAAEC,CAAC,kBAC3CzC,OAAA;wBAAAa,QAAA,EAAa2B;sBAAG,GAAPC,CAAC;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CACtB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACN;gBAAA,GAvBOgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBV,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAK,UAAU,CAACgB,eAAe,IAAIhB,UAAU,CAACgB,eAAe,CAAClB,MAAM,GAAG,CAAC,iBAClEpB,OAAA;YAAKY,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCb,OAAA;cAAAa,QAAA,EAAI;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCjB,OAAA;cAAKY,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCS,UAAU,CAACgB,eAAe,CAACjB,GAAG,CAAC,CAACmB,GAAG,EAAEP,KAAK,kBACzCjC,OAAA;gBAAiBY,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAC9Cb,OAAA;kBAAKY,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpCb,OAAA;oBAAAa,QAAA,EAAK2B,GAAG,CAACE;kBAAK;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpBjB,OAAA;oBAAMY,SAAS,EAAE,2BAA2B4B,GAAG,CAACG,QAAQ,EAAG;oBAAA9B,QAAA,EACxD2B,GAAG,CAACG;kBAAQ;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNjB,OAAA;kBAAGY,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE2B,GAAG,CAACJ;gBAAW;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9DuB,GAAG,CAACI,WAAW,IAAIJ,GAAG,CAACI,WAAW,CAACxB,MAAM,GAAG,CAAC,iBAC5CpB,OAAA;kBAAKY,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3Bb,OAAA;oBAAAa,QAAA,EAAQ;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BjB,OAAA;oBAAAa,QAAA,EACG2B,GAAG,CAACI,WAAW,CAACvB,GAAG,CAAC,CAACwB,MAAM,EAAEJ,CAAC,kBAC7BzC,OAAA;sBAAAa,QAAA,EAAagC;oBAAM,GAAVJ,CAAC;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAc,CACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,eACDjB,OAAA;kBAAKY,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCb,OAAA;oBAAAa,QAAA,GAAM,eAAG,EAAC2B,GAAG,CAACM,eAAe;kBAAA;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrCjB,OAAA;oBAAAa,QAAA,GAAM,SAAE,EAAC2B,GAAG,CAACO,SAAS;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA,GArBEgB,KAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAK,UAAU,CAAC0B,SAAS,iBACnBhD,OAAA;YAAKY,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBb,OAAA;cAAAa,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBjB,OAAA;cAAKY,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5Bb,OAAA;gBAAAa,QAAA,GAAM,SAAO,EAACS,UAAU,CAAC0B,SAAS,CAACC,SAAS;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDjB,OAAA;gBAAAa,QAAA,GAAM,WAAS,EAACS,UAAU,CAAC0B,SAAS,CAACE,YAAY;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDjB,OAAA;gBAAAa,QAAA,GAAM,aAAW,EAACS,UAAU,CAAC0B,SAAS,CAACG,SAAS;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GA7FOK,UAAU,CAAC8B,GAAG;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8FnB,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAEDjB,OAAA;MAAOqD,GAAG;MAAAxC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACf,EAAA,CA/LID,WAAW;EAAA,QACwDH,aAAa;AAAA;AAAAwD,EAAA,GADhFrD,WAAW;AAiMjB,eAAeA,WAAW;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}