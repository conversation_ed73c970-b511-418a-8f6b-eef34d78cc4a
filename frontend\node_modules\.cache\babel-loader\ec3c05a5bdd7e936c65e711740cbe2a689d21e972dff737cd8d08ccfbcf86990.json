{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\ApiTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApiTest = () => {\n  _s();\n  var _testResults$health, _testResults$login, _testResults$dashboar, _testResults$health2, _testResults$login2, _testResults$dashboar2;\n  const [testResults, setTestResults] = useState({\n    health: null,\n    login: null,\n    dashboard: null\n  });\n  const [loading, setLoading] = useState(false);\n  const runTests = async () => {\n    setLoading(true);\n    const results = {\n      health: null,\n      login: null,\n      dashboard: null\n    };\n    try {\n      // Test 1: Health Check\n      console.log('Testing health endpoint...');\n      const healthResponse = await axios.get('http://localhost:5000/api/health');\n      results.health = {\n        success: true,\n        data: healthResponse.data\n      };\n      console.log('Health test passed:', healthResponse.data);\n\n      // Test 2: Login\n      console.log('Testing login endpoint...');\n      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {\n        email: '<EMAIL>',\n        password: 'demo123'\n      });\n      results.login = {\n        success: true,\n        data: loginResponse.data\n      };\n      console.log('Login test passed:', loginResponse.data);\n\n      // Test 3: Dashboard (if login succeeded)\n      if (results.login.success) {\n        console.log('Testing dashboard endpoint...');\n        const dashboardResponse = await axios.get('http://localhost:5000/api/users/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${results.login.data.data.token}`\n          }\n        });\n        results.dashboard = {\n          success: true,\n          data: dashboardResponse.data\n        };\n        console.log('Dashboard test passed:', dashboardResponse.data);\n      }\n    } catch (error) {\n      console.error('API test error:', error);\n      if (!results.health) {\n        results.health = {\n          success: false,\n          error: error.message\n        };\n      } else if (!results.login) {\n        results.login = {\n          success: false,\n          error: error.message\n        };\n      } else {\n        results.dashboard = {\n          success: false,\n          error: error.message\n        };\n      }\n    }\n    setTestResults(results);\n    setLoading(false);\n  };\n  useEffect(() => {\n    runTests();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '800px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\uD83D\\uDD27 API Connection Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: runTests,\n      disabled: loading,\n      style: {\n        padding: '10px 20px',\n        backgroundColor: '#667eea',\n        color: 'white',\n        border: 'none',\n        borderRadius: '5px',\n        cursor: loading ? 'not-allowed' : 'pointer',\n        marginBottom: '20px'\n      },\n      children: loading ? 'Testing...' : 'Run Tests Again'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gap: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: (_testResults$health = testResults.health) !== null && _testResults$health !== void 0 && _testResults$health.success ? '#d4edda' : testResults.health ? '#f8d7da' : '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"1. Health Check\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), testResults.health ? testResults.health.success ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Success!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 22\n            }, this), \" Backend is reachable\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Status: \", testResults.health.data.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Message: \", testResults.health.data.message]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u274C \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Failed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Error: \", testResults.health.error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u23F3 Testing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: (_testResults$login = testResults.login) !== null && _testResults$login !== void 0 && _testResults$login.success ? '#d4edda' : testResults.login ? '#f8d7da' : '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"2. Login Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), testResults.login ? testResults.login.success ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Success!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 22\n            }, this), \" Authentication working\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"User: \", testResults.login.data.data.user.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Email: \", testResults.login.data.data.user.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Token: \", testResults.login.data.data.token.substring(0, 20), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u274C \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Failed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Error: \", testResults.login.error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u23F3 Waiting for health check...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: (_testResults$dashboar = testResults.dashboard) !== null && _testResults$dashboar !== void 0 && _testResults$dashboar.success ? '#d4edda' : testResults.dashboard ? '#f8d7da' : '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"3. Dashboard Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), testResults.dashboard ? testResults.dashboard.success ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Success!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 22\n            }, this), \" Protected routes working\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Today's Steps: \", testResults.dashboard.data.data.today.steps]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Today's Calories: \", testResults.dashboard.data.data.today.calories]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Active Goals: \", testResults.dashboard.data.data.goals.active.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u274C \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Failed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Error: \", testResults.dashboard.error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u23F3 Waiting for login...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), ((_testResults$health2 = testResults.health) === null || _testResults$health2 === void 0 ? void 0 : _testResults$health2.success) && ((_testResults$login2 = testResults.login) === null || _testResults$login2 === void 0 ? void 0 : _testResults$login2.success) && ((_testResults$dashboar2 = testResults.dashboard) === null || _testResults$dashboar2 === void 0 ? void 0 : _testResults$dashboar2.success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px',\n        padding: '15px',\n        backgroundColor: '#d1ecf1',\n        border: '1px solid #bee5eb',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDF89 All Tests Passed!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The backend API is working correctly. If login still doesn't work in the main app, the issue is likely in the React Router navigation or authentication context.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Next step:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 14\n        }, this), \" Try logging in with the main login form and check the browser console for any errors.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(ApiTest, \"A6Tzeg2pU9wwjK4/axevxvB0Uy8=\");\n_c = ApiTest;\nexport default ApiTest;\nvar _c;\n$RefreshReg$(_c, \"ApiTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "ApiTest", "_s", "_testResults$health", "_testResults$login", "_testResults$dashboar", "_testResults$health2", "_testResults$login2", "_testResults$dashboar2", "testResults", "setTestResults", "health", "login", "dashboard", "loading", "setLoading", "runTests", "results", "console", "log", "healthResponse", "get", "success", "data", "loginResponse", "post", "email", "password", "dashboardResponse", "headers", "token", "error", "message", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "backgroundColor", "color", "border", "borderRadius", "cursor", "marginBottom", "display", "gap", "status", "user", "name", "substring", "today", "steps", "calories", "goals", "active", "length", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/ApiTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst ApiTest = () => {\n  const [testResults, setTestResults] = useState({\n    health: null,\n    login: null,\n    dashboard: null\n  });\n  const [loading, setLoading] = useState(false);\n\n  const runTests = async () => {\n    setLoading(true);\n    const results = { health: null, login: null, dashboard: null };\n\n    try {\n      // Test 1: Health Check\n      console.log('Testing health endpoint...');\n      const healthResponse = await axios.get('http://localhost:5000/api/health');\n      results.health = { success: true, data: healthResponse.data };\n      console.log('Health test passed:', healthResponse.data);\n\n      // Test 2: Login\n      console.log('Testing login endpoint...');\n      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {\n        email: '<EMAIL>',\n        password: 'demo123'\n      });\n      results.login = { success: true, data: loginResponse.data };\n      console.log('Login test passed:', loginResponse.data);\n\n      // Test 3: Dashboard (if login succeeded)\n      if (results.login.success) {\n        console.log('Testing dashboard endpoint...');\n        const dashboardResponse = await axios.get('http://localhost:5000/api/users/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${results.login.data.data.token}`\n          }\n        });\n        results.dashboard = { success: true, data: dashboardResponse.data };\n        console.log('Dashboard test passed:', dashboardResponse.data);\n      }\n\n    } catch (error) {\n      console.error('API test error:', error);\n      if (!results.health) {\n        results.health = { success: false, error: error.message };\n      } else if (!results.login) {\n        results.login = { success: false, error: error.message };\n      } else {\n        results.dashboard = { success: false, error: error.message };\n      }\n    }\n\n    setTestResults(results);\n    setLoading(false);\n  };\n\n  useEffect(() => {\n    runTests();\n  }, []);\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <h2>🔧 API Connection Test</h2>\n      \n      <button \n        onClick={runTests} \n        disabled={loading}\n        style={{\n          padding: '10px 20px',\n          backgroundColor: '#667eea',\n          color: 'white',\n          border: 'none',\n          borderRadius: '5px',\n          cursor: loading ? 'not-allowed' : 'pointer',\n          marginBottom: '20px'\n        }}\n      >\n        {loading ? 'Testing...' : 'Run Tests Again'}\n      </button>\n\n      <div style={{ display: 'grid', gap: '15px' }}>\n        {/* Health Test */}\n        <div style={{\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: testResults.health?.success ? '#d4edda' : testResults.health ? '#f8d7da' : '#f8f9fa'\n        }}>\n          <h3>1. Health Check</h3>\n          {testResults.health ? (\n            testResults.health.success ? (\n              <div>\n                <p>✅ <strong>Success!</strong> Backend is reachable</p>\n                <p>Status: {testResults.health.data.status}</p>\n                <p>Message: {testResults.health.data.message}</p>\n              </div>\n            ) : (\n              <div>\n                <p>❌ <strong>Failed!</strong></p>\n                <p>Error: {testResults.health.error}</p>\n              </div>\n            )\n          ) : (\n            <p>⏳ Testing...</p>\n          )}\n        </div>\n\n        {/* Login Test */}\n        <div style={{\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: testResults.login?.success ? '#d4edda' : testResults.login ? '#f8d7da' : '#f8f9fa'\n        }}>\n          <h3>2. Login Test</h3>\n          {testResults.login ? (\n            testResults.login.success ? (\n              <div>\n                <p>✅ <strong>Success!</strong> Authentication working</p>\n                <p>User: {testResults.login.data.data.user.name}</p>\n                <p>Email: {testResults.login.data.data.user.email}</p>\n                <p>Token: {testResults.login.data.data.token.substring(0, 20)}...</p>\n              </div>\n            ) : (\n              <div>\n                <p>❌ <strong>Failed!</strong></p>\n                <p>Error: {testResults.login.error}</p>\n              </div>\n            )\n          ) : (\n            <p>⏳ Waiting for health check...</p>\n          )}\n        </div>\n\n        {/* Dashboard Test */}\n        <div style={{\n          padding: '15px',\n          border: '1px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: testResults.dashboard?.success ? '#d4edda' : testResults.dashboard ? '#f8d7da' : '#f8f9fa'\n        }}>\n          <h3>3. Dashboard Test</h3>\n          {testResults.dashboard ? (\n            testResults.dashboard.success ? (\n              <div>\n                <p>✅ <strong>Success!</strong> Protected routes working</p>\n                <p>Today's Steps: {testResults.dashboard.data.data.today.steps}</p>\n                <p>Today's Calories: {testResults.dashboard.data.data.today.calories}</p>\n                <p>Active Goals: {testResults.dashboard.data.data.goals.active.length}</p>\n              </div>\n            ) : (\n              <div>\n                <p>❌ <strong>Failed!</strong></p>\n                <p>Error: {testResults.dashboard.error}</p>\n              </div>\n            )\n          ) : (\n            <p>⏳ Waiting for login...</p>\n          )}\n        </div>\n      </div>\n\n      {testResults.health?.success && testResults.login?.success && testResults.dashboard?.success && (\n        <div style={{\n          marginTop: '20px',\n          padding: '15px',\n          backgroundColor: '#d1ecf1',\n          border: '1px solid #bee5eb',\n          borderRadius: '8px'\n        }}>\n          <h3>🎉 All Tests Passed!</h3>\n          <p>The backend API is working correctly. If login still doesn't work in the main app, the issue is likely in the React Router navigation or authentication context.</p>\n          <p><strong>Next step:</strong> Try logging in with the main login form and check the browser console for any errors.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ApiTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC;IAC7Ce,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMoB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BD,UAAU,CAAC,IAAI,CAAC;IAChB,MAAME,OAAO,GAAG;MAAEN,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;IAE9D,IAAI;MACF;MACAK,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMC,cAAc,GAAG,MAAMtB,KAAK,CAACuB,GAAG,CAAC,kCAAkC,CAAC;MAC1EJ,OAAO,CAACN,MAAM,GAAG;QAAEW,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEH,cAAc,CAACG;MAAK,CAAC;MAC7DL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,cAAc,CAACG,IAAI,CAAC;;MAEvD;MACAL,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAMK,aAAa,GAAG,MAAM1B,KAAK,CAAC2B,IAAI,CAAC,sCAAsC,EAAE;QAC7EC,KAAK,EAAE,wBAAwB;QAC/BC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFV,OAAO,CAACL,KAAK,GAAG;QAAEU,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEC,aAAa,CAACD;MAAK,CAAC;MAC3DL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,aAAa,CAACD,IAAI,CAAC;;MAErD;MACA,IAAIN,OAAO,CAACL,KAAK,CAACU,OAAO,EAAE;QACzBJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMS,iBAAiB,GAAG,MAAM9B,KAAK,CAACuB,GAAG,CAAC,2CAA2C,EAAE;UACrFQ,OAAO,EAAE;YACP,eAAe,EAAE,UAAUZ,OAAO,CAACL,KAAK,CAACW,IAAI,CAACA,IAAI,CAACO,KAAK;UAC1D;QACF,CAAC,CAAC;QACFb,OAAO,CAACJ,SAAS,GAAG;UAAES,OAAO,EAAE,IAAI;UAAEC,IAAI,EAAEK,iBAAiB,CAACL;QAAK,CAAC;QACnEL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAES,iBAAiB,CAACL,IAAI,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACd,OAAO,CAACN,MAAM,EAAE;QACnBM,OAAO,CAACN,MAAM,GAAG;UAAEW,OAAO,EAAE,KAAK;UAAES,KAAK,EAAEA,KAAK,CAACC;QAAQ,CAAC;MAC3D,CAAC,MAAM,IAAI,CAACf,OAAO,CAACL,KAAK,EAAE;QACzBK,OAAO,CAACL,KAAK,GAAG;UAAEU,OAAO,EAAE,KAAK;UAAES,KAAK,EAAEA,KAAK,CAACC;QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLf,OAAO,CAACJ,SAAS,GAAG;UAAES,OAAO,EAAE,KAAK;UAAES,KAAK,EAAEA,KAAK,CAACC;QAAQ,CAAC;MAC9D;IACF;IAEAtB,cAAc,CAACO,OAAO,CAAC;IACvBF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACdmB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhB,OAAA;IAAKiC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnErC,OAAA;MAAAqC,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE/BzC,OAAA;MACE0C,OAAO,EAAE1B,QAAS;MAClB2B,QAAQ,EAAE7B,OAAQ;MAClBmB,KAAK,EAAE;QACLC,OAAO,EAAE,WAAW;QACpBU,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAElC,OAAO,GAAG,aAAa,GAAG,SAAS;QAC3CmC,YAAY,EAAE;MAChB,CAAE;MAAAZ,QAAA,EAEDvB,OAAO,GAAG,YAAY,GAAG;IAAiB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAETzC,OAAA;MAAKiC,KAAK,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAd,QAAA,gBAE3CrC,OAAA;QAAKiC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfY,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBH,eAAe,EAAE,CAAAzC,mBAAA,GAAAM,WAAW,CAACE,MAAM,cAAAR,mBAAA,eAAlBA,mBAAA,CAAoBmB,OAAO,GAAG,SAAS,GAAGb,WAAW,CAACE,MAAM,GAAG,SAAS,GAAG;QAC9F,CAAE;QAAA0B,QAAA,gBACArC,OAAA;UAAAqC,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACvBhC,WAAW,CAACE,MAAM,GACjBF,WAAW,CAACE,MAAM,CAACW,OAAO,gBACxBtB,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yBAAqB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvDzC,OAAA;YAAAqC,QAAA,GAAG,UAAQ,EAAC5B,WAAW,CAACE,MAAM,CAACY,IAAI,CAAC6B,MAAM;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CzC,OAAA;YAAAqC,QAAA,GAAG,WAAS,EAAC5B,WAAW,CAACE,MAAM,CAACY,IAAI,CAACS,OAAO;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,gBAENzC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCzC,OAAA;YAAAqC,QAAA,GAAG,SAAO,EAAC5B,WAAW,CAACE,MAAM,CAACoB,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,gBAEDzC,OAAA;UAAAqC,QAAA,EAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzC,OAAA;QAAKiC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfY,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBH,eAAe,EAAE,CAAAxC,kBAAA,GAAAK,WAAW,CAACG,KAAK,cAAAR,kBAAA,eAAjBA,kBAAA,CAAmBkB,OAAO,GAAG,SAAS,GAAGb,WAAW,CAACG,KAAK,GAAG,SAAS,GAAG;QAC5F,CAAE;QAAAyB,QAAA,gBACArC,OAAA;UAAAqC,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrBhC,WAAW,CAACG,KAAK,GAChBH,WAAW,CAACG,KAAK,CAACU,OAAO,gBACvBtB,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2BAAuB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDzC,OAAA;YAAAqC,QAAA,GAAG,QAAM,EAAC5B,WAAW,CAACG,KAAK,CAACW,IAAI,CAACA,IAAI,CAAC8B,IAAI,CAACC,IAAI;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDzC,OAAA;YAAAqC,QAAA,GAAG,SAAO,EAAC5B,WAAW,CAACG,KAAK,CAACW,IAAI,CAACA,IAAI,CAAC8B,IAAI,CAAC3B,KAAK;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDzC,OAAA;YAAAqC,QAAA,GAAG,SAAO,EAAC5B,WAAW,CAACG,KAAK,CAACW,IAAI,CAACA,IAAI,CAACO,KAAK,CAACyB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,gBAENzC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCzC,OAAA;YAAAqC,QAAA,GAAG,SAAO,EAAC5B,WAAW,CAACG,KAAK,CAACmB,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACN,gBAEDzC,OAAA;UAAAqC,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzC,OAAA;QAAKiC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfY,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBH,eAAe,EAAE,CAAAvC,qBAAA,GAAAI,WAAW,CAACI,SAAS,cAAAR,qBAAA,eAArBA,qBAAA,CAAuBiB,OAAO,GAAG,SAAS,GAAGb,WAAW,CAACI,SAAS,GAAG,SAAS,GAAG;QACpG,CAAE;QAAAwB,QAAA,gBACArC,OAAA;UAAAqC,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzBhC,WAAW,CAACI,SAAS,GACpBJ,WAAW,CAACI,SAAS,CAACS,OAAO,gBAC3BtB,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6BAAyB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DzC,OAAA;YAAAqC,QAAA,GAAG,iBAAe,EAAC5B,WAAW,CAACI,SAAS,CAACU,IAAI,CAACA,IAAI,CAACiC,KAAK,CAACC,KAAK;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEzC,OAAA;YAAAqC,QAAA,GAAG,oBAAkB,EAAC5B,WAAW,CAACI,SAAS,CAACU,IAAI,CAACA,IAAI,CAACiC,KAAK,CAACE,QAAQ;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEzC,OAAA;YAAAqC,QAAA,GAAG,gBAAc,EAAC5B,WAAW,CAACI,SAAS,CAACU,IAAI,CAACA,IAAI,CAACoC,KAAK,CAACC,MAAM,CAACC,MAAM;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,gBAENzC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAAqC,QAAA,GAAG,SAAE,eAAArC,OAAA;cAAAqC,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCzC,OAAA;YAAAqC,QAAA,GAAG,SAAO,EAAC5B,WAAW,CAACI,SAAS,CAACkB,KAAK;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACN,gBAEDzC,OAAA;UAAAqC,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,EAAAnC,oBAAA,GAAAG,WAAW,CAACE,MAAM,cAAAL,oBAAA,uBAAlBA,oBAAA,CAAoBgB,OAAO,OAAAf,mBAAA,GAAIE,WAAW,CAACG,KAAK,cAAAL,mBAAA,uBAAjBA,mBAAA,CAAmBe,OAAO,OAAAd,sBAAA,GAAIC,WAAW,CAACI,SAAS,cAAAL,sBAAA,uBAArBA,sBAAA,CAAuBc,OAAO,kBAC1FtB,OAAA;MAAKiC,KAAK,EAAE;QACV6B,SAAS,EAAE,MAAM;QACjB5B,OAAO,EAAE,MAAM;QACfU,eAAe,EAAE,SAAS;QAC1BE,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE;MAChB,CAAE;MAAAV,QAAA,gBACArC,OAAA;QAAAqC,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BzC,OAAA;QAAAqC,QAAA,EAAG;MAAgK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvKzC,OAAA;QAAAqC,QAAA,gBAAGrC,OAAA;UAAAqC,QAAA,EAAQ;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,0FAAsF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvC,EAAA,CAhLID,OAAO;AAAA8D,EAAA,GAAP9D,OAAO;AAkLb,eAAeA,OAAO;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}