const express = require('express');
const mongoose = require('mongoose');
const { protect, checkOwnership } = require('../middleware/auth');
const HealthData = require('../models/HealthData');
const router = express.Router();

// @desc    Get user's health data
// @route   GET /api/health-data
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const { startDate, endDate, limit = 30, page = 1 } = req.query;
    const userId = req.user.id;

    let query = { userId };

    // Date filtering
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    const skip = (page - 1) * limit;
    
    const healthData = await HealthData.find(query)
      .sort({ date: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await HealthData.countDocuments(query);

    res.json({
      success: true,
      data: {
        healthData,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          hasNext: skip + healthData.length < total,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get health data error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Add health data
// @route   POST /api/health-data
// @access  Private
router.post('/', protect, async (req, res) => {
  try {
    const healthDataInput = {
      ...req.body,
      userId: req.user.id,
      date: req.body.date || new Date()
    };

    // Check if data for this date already exists
    const existingData = await HealthData.findOne({
      userId: req.user.id,
      date: {
        $gte: new Date(healthDataInput.date).setHours(0, 0, 0, 0),
        $lt: new Date(healthDataInput.date).setHours(23, 59, 59, 999)
      }
    });

    let healthData;
    if (existingData) {
      // Update existing data
      Object.assign(existingData, healthDataInput);
      existingData.lastModified = new Date();
      healthData = await existingData.save();
    } else {
      // Create new data
      healthData = await HealthData.create(healthDataInput);
    }

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`user-${req.user.id}`).emit('healthDataUpdate', {
      type: existingData ? 'updated' : 'created',
      data: healthData
    });

    res.status(201).json({
      success: true,
      message: existingData ? 'Health data updated successfully' : 'Health data added successfully',
      data: { healthData }
    });
  } catch (error) {
    console.error('Add health data error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get health data statistics
// @route   GET /api/health-data/stats
// @access  Private
router.get('/stats', protect, async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const userId = req.user.id;

    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    const stats = await HealthData.aggregate([
      {
        $match: {
          userId: mongoose.Types.ObjectId(userId),
          date: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalSteps: { $sum: '$steps' },
          avgSteps: { $avg: '$steps' },
          totalDistance: { $sum: '$distance' },
          avgDistance: { $avg: '$distance' },
          totalCalories: { $sum: '$caloriesBurned' },
          avgCalories: { $avg: '$caloriesBurned' },
          avgHeartRate: { $avg: '$heartRate.average' },
          avgSleep: { $avg: '$sleep.totalSleep' },
          avgMood: { $avg: '$mood' },
          avgEnergyLevel: { $avg: '$energyLevel' },
          dataPoints: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        period,
        stats: stats[0] || {},
        startDate,
        endDate: new Date()
      }
    });
  } catch (error) {
    console.error('Get health stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get weekly summary
// @route   GET /api/health-data/weekly-summary
// @access  Private
router.get('/weekly-summary', protect, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const userId = req.user.id;

    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    const summary = await HealthData.getWeeklySummary(userId, start, end);

    res.json({
      success: true,
      data: {
        summary: summary[0] || {},
        period: { startDate: start, endDate: end }
      }
    });
  } catch (error) {
    console.error('Get weekly summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
