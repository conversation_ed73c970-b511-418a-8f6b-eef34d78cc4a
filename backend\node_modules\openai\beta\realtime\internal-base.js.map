{"version": 3, "file": "internal-base.js", "sourceRoot": "", "sources": ["../../src/beta/realtime/internal-base.ts"], "names": [], "mappings": ";;;AACA,4DAAsD;AACtD,0CAA0C;AAC1C,0CAAkD;AAElD,MAAa,mBAAoB,SAAQ,mBAAW;IAWlD,YAAY,OAAe,EAAE,KAAwB;QACnD,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,CAAC;IAClC,CAAC;CACF;AAjBD,kDAiBC;AAeD,MAAsB,qBAAsB,SAAQ,2BAA4B;IAapE,QAAQ,CAAC,KAAwB,EAAE,OAA4B,EAAE,KAAW;QACpF,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,CAAC;gBACZ,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,SAAS,KAAK,CAAC,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACxI,CAAC,CAAC,OAAO,IAAI,eAAe,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;YAC/B,MAAM,KAAK,GAAG,IAAI,mBAAmB,CACnC,OAAO;gBACL,iIAAiI,EACnI,KAAK,CACN,CAAC;YACF,aAAa;YACb,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;YACpB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACtB,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,aAAa;QACb,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;CACF;AArCD,sDAqCC;AAED,SAAgB,OAAO,CAAC,MAA0C;IAChE,OAAO,MAAM,YAAY,mBAAW,CAAC;AACvC,CAAC;AAFD,0BAEC;AAED,SAAgB,gBAAgB,CAAC,MAA0C,EAAE,KAAa;IACxF,MAAM,IAAI,GAAG,WAAW,CAAC;IACzB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAC/B,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9E,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;QACnB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACvD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KAC3C;SAAM;QACL,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACtC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAZD,4CAYC"}