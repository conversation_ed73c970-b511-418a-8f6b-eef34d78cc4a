import React from 'react';
import { useHealthData } from '../contexts/HealthDataContext';

const GoalProgress = ({ goals = [] }) => {
  const { updateGoalProgress } = useHealthData();

  const handleProgressUpdate = async (goalId, newValue) => {
    await updateGoalProgress(goalId, newValue);
  };

  const getRiskLevelColor = (riskLevel) => {
    switch (riskLevel) {
      case 'low':
      case 'very_low':
        return '#27ae60';
      case 'moderate':
        return '#f39c12';
      case 'high':
      case 'very_high':
        return '#e74c3c';
      default:
        return '#667eea';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#e74c3c';
      case 'medium':
        return '#f39c12';
      case 'low':
        return '#27ae60';
      default:
        return '#667eea';
    }
  };

  if (goals.length === 0) {
    return (
      <div className="no-goals">
        <div className="no-goals-icon">🎯</div>
        <h3>No Active Goals</h3>
        <p>Set some health goals to track your progress!</p>
        <button className="btn btn-primary btn-small">
          Create Your First Goal
        </button>
      </div>
    );
  }

  return (
    <div className="goal-progress">
      {goals.slice(0, 5).map((goal) => {
        const progressPercentage = Math.min(
          (goal.current?.value / goal.target?.value) * 100 || 0,
          100
        );
        const isCompleted = progressPercentage >= 100;
        const daysRemaining = goal.daysRemaining || 0;

        return (
          <div key={goal._id} className="goal-item">
            <div className="goal-header">
              <div className="goal-info">
                <h4 className="goal-title">{goal.title}</h4>
                <p className="goal-meta">
                  <span className="goal-category">{goal.category}</span>
                  <span className="goal-type">{goal.type}</span>
                  {daysRemaining > 0 && (
                    <span className="goal-deadline">
                      {daysRemaining} days left
                    </span>
                  )}
                </p>
              </div>
              <div className="goal-priority">
                <span 
                  className="priority-badge"
                  style={{ 
                    backgroundColor: getPriorityColor(goal.priority),
                    color: 'white'
                  }}
                >
                  {goal.priority}
                </span>
              </div>
            </div>

            <div className="goal-progress-section">
              <div className="progress-info">
                <span className="progress-current">
                  {goal.current?.value?.toLocaleString() || 0} {goal.target?.unit}
                </span>
                <span className="progress-target">
                  / {goal.target?.value?.toLocaleString()} {goal.target?.unit}
                </span>
                <span className={`progress-percentage ${isCompleted ? 'completed' : ''}`}>
                  {Math.round(progressPercentage)}%
                </span>
              </div>

              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ 
                    width: `${progressPercentage}%`,
                    backgroundColor: isCompleted ? '#27ae60' : '#667eea'
                  }}
                />
              </div>
            </div>

            {goal.milestones && goal.milestones.length > 0 && (
              <div className="goal-milestones">
                {goal.milestones.map((milestone, index) => (
                  <div 
                    key={index}
                    className={`milestone ${milestone.achieved ? 'achieved' : ''}`}
                    title={milestone.reward}
                  >
                    <span className="milestone-percentage">
                      {milestone.percentage}%
                    </span>
                    {milestone.achieved && (
                      <span className="milestone-check">✓</span>
                    )}
                  </div>
                ))}
              </div>
            )}

            {isCompleted && (
              <div className="goal-completed">
                <span className="completion-icon">🎉</span>
                <span className="completion-text">Goal Completed!</span>
              </div>
            )}
          </div>
        );
      })}

      <style jsx>{`
        .goal-progress {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .no-goals {
          text-align: center;
          padding: 40px 20px;
          color: #7f8c8d;
        }

        .no-goals-icon {
          font-size: 3rem;
          margin-bottom: 15px;
        }

        .no-goals h3 {
          margin: 0 0 10px 0;
          color: #2c3e50;
        }

        .no-goals p {
          margin: 0 0 20px 0;
        }

        .goal-item {
          background: rgba(102, 126, 234, 0.05);
          border: 1px solid rgba(102, 126, 234, 0.1);
          border-radius: 12px;
          padding: 20px;
          transition: all 0.3s ease;
        }

        .goal-item:hover {
          background: rgba(102, 126, 234, 0.08);
          border-color: rgba(102, 126, 234, 0.2);
        }

        .goal-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;
        }

        .goal-info {
          flex: 1;
        }

        .goal-title {
          margin: 0 0 8px 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: #2c3e50;
        }

        .goal-meta {
          margin: 0;
          font-size: 0.85rem;
          color: #7f8c8d;
          display: flex;
          gap: 10px;
          flex-wrap: wrap;
        }

        .goal-category,
        .goal-type,
        .goal-deadline {
          background: rgba(255, 255, 255, 0.8);
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .goal-deadline {
          background: rgba(243, 156, 18, 0.1);
          color: #f39c12;
        }

        .priority-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
        }

        .goal-progress-section {
          margin-bottom: 15px;
        }

        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .progress-current {
          font-weight: 600;
          color: #2c3e50;
        }

        .progress-target {
          color: #7f8c8d;
          font-size: 0.9rem;
        }

        .progress-percentage {
          font-weight: 600;
          color: #667eea;
        }

        .progress-percentage.completed {
          color: #27ae60;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          border-radius: 4px;
          transition: width 0.6s ease;
        }

        .goal-milestones {
          display: flex;
          gap: 8px;
          margin-bottom: 10px;
        }

        .milestone {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: rgba(0, 0, 0, 0.1);
          font-size: 0.7rem;
          font-weight: 600;
          color: #7f8c8d;
          position: relative;
          transition: all 0.3s ease;
        }

        .milestone.achieved {
          background: #27ae60;
          color: white;
        }

        .milestone-check {
          position: absolute;
          top: -2px;
          right: -2px;
          background: #27ae60;
          color: white;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.6rem;
        }

        .goal-completed {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 10px;
          background: rgba(39, 174, 96, 0.1);
          border: 1px solid rgba(39, 174, 96, 0.2);
          border-radius: 8px;
          color: #27ae60;
          font-weight: 600;
        }

        .completion-icon {
          font-size: 1.2rem;
        }

        @media (max-width: 768px) {
          .goal-header {
            flex-direction: column;
            gap: 10px;
          }

          .goal-meta {
            flex-direction: column;
            gap: 5px;
          }

          .progress-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
          }
        }
      `}</style>
    </div>
  );
};

export default GoalProgress;
