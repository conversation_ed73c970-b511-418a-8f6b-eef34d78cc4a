const mongoose = require('mongoose');

const healthDataSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  date: {
    type: Date,
    required: true,
    index: true
  },
  // Activity Data
  steps: {
    type: Number,
    default: 0,
    min: 0,
    max: 100000
  },
  distance: {
    type: Number, // in kilometers
    default: 0,
    min: 0
  },
  caloriesBurned: {
    type: Number,
    default: 0,
    min: 0
  },
  activeMinutes: {
    type: Number,
    default: 0,
    min: 0,
    max: 1440 // max minutes in a day
  },
  
  // Heart Rate Data
  heartRate: {
    resting: {
      type: Number,
      min: 30,
      max: 200
    },
    average: {
      type: Number,
      min: 30,
      max: 220
    },
    maximum: {
      type: Number,
      min: 50,
      max: 220
    },
    zones: {
      fatBurn: { type: Number, default: 0 }, // minutes
      cardio: { type: Number, default: 0 },
      peak: { type: Number, default: 0 }
    }
  },
  
  // Sleep Data
  sleep: {
    totalSleep: {
      type: Number, // in minutes
      min: 0,
      max: 1440
    },
    deepSleep: {
      type: Number, // in minutes
      min: 0
    },
    lightSleep: {
      type: Number, // in minutes
      min: 0
    },
    remSleep: {
      type: Number, // in minutes
      min: 0
    },
    awakeTime: {
      type: Number, // in minutes
      min: 0
    },
    sleepEfficiency: {
      type: Number, // percentage
      min: 0,
      max: 100
    },
    bedTime: Date,
    wakeTime: Date
  },
  
  // Exercise Sessions
  exercises: [{
    type: {
      type: String,
      enum: ['running', 'walking', 'cycling', 'swimming', 'weightlifting', 'yoga', 'other']
    },
    duration: Number, // in minutes
    caloriesBurned: Number,
    averageHeartRate: Number,
    maxHeartRate: Number,
    startTime: Date,
    endTime: Date,
    notes: String
  }],
  
  // Vital Signs
  vitals: {
    bloodPressure: {
      systolic: {
        type: Number,
        min: 70,
        max: 250
      },
      diastolic: {
        type: Number,
        min: 40,
        max: 150
      }
    },
    bloodOxygen: {
      type: Number, // SpO2 percentage
      min: 70,
      max: 100
    },
    bodyTemperature: {
      type: Number, // in Celsius
      min: 30,
      max: 45
    },
    weight: {
      type: Number, // in kg
      min: 20,
      max: 500
    }
  },
  
  // Stress and Recovery
  stress: {
    level: {
      type: Number, // 1-10 scale
      min: 1,
      max: 10
    },
    duration: Number, // minutes of high stress
    triggers: [String]
  },
  
  // Hydration
  waterIntake: {
    type: Number, // in liters
    default: 0,
    min: 0,
    max: 10
  },
  
  // Mood and Energy
  mood: {
    type: Number, // 1-10 scale
    min: 1,
    max: 10
  },
  energyLevel: {
    type: Number, // 1-10 scale
    min: 1,
    max: 10
  },
  
  // Data Source
  dataSource: {
    type: String,
    enum: ['fitbit', 'apple_watch', 'garmin', 'manual', 'other'],
    default: 'manual'
  },
  
  // Data Quality
  dataQuality: {
    completeness: {
      type: Number, // percentage
      min: 0,
      max: 100,
      default: 100
    },
    accuracy: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'high'
    }
  },
  
  // Sync Information
  syncedAt: {
    type: Date,
    default: Date.now
  },
  lastModified: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better query performance
healthDataSchema.index({ userId: 1, date: -1 });
healthDataSchema.index({ userId: 1, createdAt: -1 });
healthDataSchema.index({ date: -1 });

// Virtual for daily activity score
healthDataSchema.virtual('activityScore').get(function() {
  let score = 0;
  
  // Steps contribution (max 30 points)
  if (this.steps) {
    score += Math.min((this.steps / 10000) * 30, 30);
  }
  
  // Active minutes contribution (max 25 points)
  if (this.activeMinutes) {
    score += Math.min((this.activeMinutes / 60) * 25, 25);
  }
  
  // Sleep contribution (max 25 points)
  if (this.sleep && this.sleep.totalSleep) {
    const sleepHours = this.sleep.totalSleep / 60;
    if (sleepHours >= 7 && sleepHours <= 9) {
      score += 25;
    } else {
      score += Math.max(0, 25 - Math.abs(8 - sleepHours) * 5);
    }
  }
  
  // Heart rate zones contribution (max 20 points)
  if (this.heartRate && this.heartRate.zones) {
    const totalZoneMinutes = this.heartRate.zones.fatBurn + this.heartRate.zones.cardio + this.heartRate.zones.peak;
    score += Math.min((totalZoneMinutes / 30) * 20, 20);
  }
  
  return Math.round(score);
});

// Method to get weekly summary
healthDataSchema.statics.getWeeklySummary = async function(userId, startDate, endDate) {
  return await this.aggregate([
    {
      $match: {
        userId: mongoose.Types.ObjectId(userId),
        date: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        totalSteps: { $sum: '$steps' },
        totalDistance: { $sum: '$distance' },
        totalCalories: { $sum: '$caloriesBurned' },
        totalActiveMinutes: { $sum: '$activeMinutes' },
        avgHeartRate: { $avg: '$heartRate.average' },
        avgSleep: { $avg: '$sleep.totalSleep' },
        avgMood: { $avg: '$mood' },
        avgEnergyLevel: { $avg: '$energyLevel' },
        daysWithData: { $sum: 1 }
      }
    }
  ]);
};

module.exports = mongoose.model('HealthData', healthDataSchema);
