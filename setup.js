#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏥 HealthConnect Setup Script');
console.log('============================\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ Node.js version 16 or higher is required');
  console.error(`   Current version: ${nodeVersion}`);
  process.exit(1);
}

console.log(`✅ Node.js version: ${nodeVersion}`);

// Function to run command and handle errors
function runCommand(command, description, cwd = process.cwd()) {
  try {
    console.log(`📦 ${description}...`);
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Failed to ${description.toLowerCase()}`);
    console.error(`   Command: ${command}`);
    console.error(`   Error: ${error.message}\n`);
    process.exit(1);
  }
}

// Check if MongoDB is running
function checkMongoDB() {
  try {
    console.log('🔍 Checking MongoDB connection...');
    execSync('mongosh --eval "db.runCommand({ping: 1})" --quiet', { stdio: 'pipe' });
    console.log('✅ MongoDB is running\n');
  } catch (error) {
    console.log('⚠️  MongoDB connection failed');
    console.log('   Please ensure MongoDB is installed and running');
    console.log('   Installation guide: https://docs.mongodb.com/manual/installation/');
    console.log('   Start MongoDB: mongod\n');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    return new Promise((resolve) => {
      rl.question('Continue setup anyway? (y/N): ', (answer) => {
        rl.close();
        if (answer.toLowerCase() !== 'y') {
          console.log('Setup cancelled. Please start MongoDB and run setup again.');
          process.exit(1);
        }
        resolve();
      });
    });
  }
}

async function main() {
  try {
    // Check MongoDB
    await checkMongoDB();

    // Install root dependencies
    if (fs.existsSync('package.json')) {
      runCommand('npm install', 'Installing root dependencies');
    }

    // Install backend dependencies
    if (fs.existsSync('backend/package.json')) {
      runCommand('npm install', 'Installing backend dependencies', 'backend');
    }

    // Install frontend dependencies
    if (fs.existsSync('frontend/package.json')) {
      runCommand('npm install', 'Installing frontend dependencies', 'frontend');
    }

    // Create environment file if it doesn't exist
    const envPath = path.join('backend', '.env');
    if (!fs.existsSync(envPath)) {
      console.log('📝 Creating environment configuration...');
      const envContent = `NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/healthconnect
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRE=30d

# OpenAI API for chatbot (optional - replace with your key)
OPENAI_API_KEY=your_openai_api_key_here

# Email configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Python ML model path
ML_MODEL_PATH=../ml-model/health_predictor.py

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100`;

      fs.writeFileSync(envPath, envContent);
      console.log('✅ Environment file created\n');
    }

    // Seed database
    console.log('🌱 Seeding database with sample data...');
    try {
      runCommand('node database/seedDatabase.js', 'Seeding database');
    } catch (error) {
      console.log('⚠️  Database seeding failed, but setup will continue');
      console.log('   You can manually seed the database later with: node database/seedDatabase.js\n');
    }

    // Setup complete
    console.log('🎉 Setup completed successfully!');
    console.log('================================\n');
    
    console.log('🚀 To start the application:');
    console.log('   1. Start the backend:  cd backend && npm run dev');
    console.log('   2. Start the frontend: cd frontend && npm start');
    console.log('   3. Or start both:      npm run dev\n');
    
    console.log('🔑 Demo Login Credentials:');
    console.log('   Email:    <EMAIL>');
    console.log('   Password: demo123\n');
    
    console.log('📚 Available Scripts:');
    console.log('   npm run dev        - Start both frontend and backend');
    console.log('   npm run server     - Start backend only');
    console.log('   npm run client     - Start frontend only');
    console.log('   npm run build      - Build frontend for production\n');
    
    console.log('🌐 Application URLs:');
    console.log('   Frontend: http://localhost:3000');
    console.log('   Backend:  http://localhost:5000');
    console.log('   API:      http://localhost:5000/api\n');
    
    console.log('📖 For more information, check the README.md file');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup
main();
