import React from 'react';

const StatsCard = ({ title, value, target, icon, color, progress = 0 }) => {
  const progressPercentage = Math.min(progress, 100);
  const isComplete = progress >= 100;

  return (
    <div className="stats-card">
      <div className="stats-header">
        <span className="stats-icon" style={{ color }}>
          {icon}
        </span>
        <div className="stats-info">
          <h3 className="stats-value">{value}</h3>
          <p className="stats-title">{title}</p>
        </div>
      </div>
      
      <div className="stats-progress">
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ 
              width: `${progressPercentage}%`,
              backgroundColor: color
            }}
          />
        </div>
        <div className="progress-info">
          <span className={`progress-text ${isComplete ? 'complete' : ''}`}>
            {Math.round(progressPercentage)}%
          </span>
          {target && (
            <span className="progress-target">
              Target: {target}
            </span>
          )}
        </div>
      </div>

      <style jsx>{`
        .stats-card {
          background: rgba(255, 255, 255, 0.9);
          border-radius: 12px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
        }

        .stats-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
        }

        .stats-icon {
          font-size: 2rem;
          margin-right: 12px;
        }

        .stats-info {
          flex: 1;
        }

        .stats-value {
          font-size: 1.8rem;
          font-weight: 700;
          margin: 0 0 4px 0;
          color: #2c3e50;
        }

        .stats-title {
          font-size: 0.9rem;
          color: #7f8c8d;
          margin: 0;
          font-weight: 500;
        }

        .stats-progress {
          margin-top: 15px;
        }

        .progress-bar {
          width: 100%;
          height: 6px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 8px;
        }

        .progress-fill {
          height: 100%;
          border-radius: 3px;
          transition: width 0.6s ease;
          background: linear-gradient(90deg, currentColor 0%, currentColor 100%);
        }

        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .progress-text {
          font-size: 0.85rem;
          font-weight: 600;
          color: #2c3e50;
        }

        .progress-text.complete {
          color: #27ae60;
        }

        .progress-target {
          font-size: 0.8rem;
          color: #7f8c8d;
        }

        @media (max-width: 480px) {
          .stats-card {
            padding: 15px;
          }

          .stats-value {
            font-size: 1.5rem;
          }

          .stats-icon {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
};

export default StatsCard;
