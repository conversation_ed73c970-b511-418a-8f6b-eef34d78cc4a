{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\ActivityChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Line } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler } from 'chart.js';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);\nconst ActivityChart = ({\n  period = '7d'\n}) => {\n  _s();\n  const {\n    getActivitySummary\n  } = useHealthData();\n  const [chartData, setChartData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [selectedMetric, setSelectedMetric] = useState('steps');\n  useEffect(() => {\n    fetchChartData();\n  }, [period]);\n  const fetchChartData = async () => {\n    setLoading(true);\n    const result = await getActivitySummary(period);\n    if (result.success) {\n      const data = result.data;\n      prepareChartData(data.dailyData || []);\n    }\n    setLoading(false);\n  };\n  const prepareChartData = dailyData => {\n    const labels = dailyData.map(item => {\n      const date = new Date(item.date);\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n    });\n    const datasets = [];\n    if (selectedMetric === 'steps') {\n      datasets.push({\n        label: 'Steps',\n        data: dailyData.map(item => item.steps || 0),\n        borderColor: '#667eea',\n        backgroundColor: 'rgba(102, 126, 234, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#667eea',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'calories') {\n      datasets.push({\n        label: 'Calories',\n        data: dailyData.map(item => item.calories || 0),\n        borderColor: '#e74c3c',\n        backgroundColor: 'rgba(231, 76, 60, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#e74c3c',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'activeMinutes') {\n      datasets.push({\n        label: 'Active Minutes',\n        data: dailyData.map(item => item.activeMinutes || 0),\n        borderColor: '#f39c12',\n        backgroundColor: 'rgba(243, 156, 18, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#f39c12',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'all') {\n      datasets.push({\n        label: 'Steps',\n        data: dailyData.map(item => item.steps || 0),\n        borderColor: '#667eea',\n        backgroundColor: 'rgba(102, 126, 234, 0.1)',\n        yAxisID: 'y',\n        tension: 0.4\n      }, {\n        label: 'Calories',\n        data: dailyData.map(item => item.calories || 0),\n        borderColor: '#e74c3c',\n        backgroundColor: 'rgba(231, 76, 60, 0.1)',\n        yAxisID: 'y1',\n        tension: 0.4\n      });\n    }\n    setChartData({\n      labels,\n      datasets\n    });\n  };\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12,\n            weight: '500'\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.95)',\n        titleColor: '#2c3e50',\n        bodyColor: '#2c3e50',\n        borderColor: 'rgba(102, 126, 234, 0.2)',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: true,\n        callbacks: {\n          label: function (context) {\n            let label = context.dataset.label || '';\n            if (label) {\n              label += ': ';\n            }\n            label += context.parsed.y.toLocaleString();\n            return label;\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          color: '#7f8c8d',\n          font: {\n            size: 11\n          }\n        }\n      },\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)'\n        },\n        ticks: {\n          color: '#7f8c8d',\n          font: {\n            size: 11\n          },\n          callback: function (value) {\n            return value.toLocaleString();\n          }\n        }\n      },\n      ...(selectedMetric === 'all' && {\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          beginAtZero: true,\n          grid: {\n            drawOnChartArea: false\n          },\n          ticks: {\n            color: '#7f8c8d',\n            font: {\n              size: 11\n            }\n          }\n        }\n      })\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index'\n    },\n    elements: {\n      point: {\n        hoverRadius: 8\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-loading\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"activity-chart\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-controls\",\n      children: /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedMetric,\n        onChange: e => setSelectedMetric(e.target.value),\n        className: \"form-select\",\n        style: {\n          width: 'auto',\n          fontSize: '0.9rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"steps\",\n          children: \"Steps\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"calories\",\n          children: \"Calories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"activeMinutes\",\n          children: \"Active Minutes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"all\",\n          children: \"All Metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: chartData ? /*#__PURE__*/_jsxDEV(Line, {\n        data: chartData,\n        options: options\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No activity data available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .activity-chart {\n          height: 100%;\n        }\n\n        .chart-controls {\n          margin-bottom: 20px;\n          display: flex;\n          justify-content: flex-end;\n        }\n\n        .chart-container {\n          height: 300px;\n          position: relative;\n        }\n\n        .chart-loading {\n          height: 300px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .no-data {\n          height: 300px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #7f8c8d;\n          font-style: italic;\n        }\n\n        @media (max-width: 768px) {\n          .chart-container {\n            height: 250px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivityChart, \"pO+gHvKr1MqR1LqyKnwLqjsu0LM=\", false, function () {\n  return [useHealthData];\n});\n_c = ActivityChart;\nexport default ActivityChart;\nvar _c;\n$RefreshReg$(_c, \"ActivityChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Line", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "useHealthData", "jsxDEV", "_jsxDEV", "register", "ActivityChart", "period", "_s", "getActivitySummary", "chartData", "setChartData", "loading", "setLoading", "selectedMetric", "setSelectedMetric", "fetchChartData", "result", "success", "data", "prepareChartData", "dailyData", "labels", "map", "item", "date", "Date", "toLocaleDateString", "month", "day", "datasets", "push", "label", "steps", "borderColor", "backgroundColor", "fill", "tension", "pointBackgroundColor", "pointBorderColor", "pointBorderWidth", "pointRadius", "pointHoverRadius", "calories", "activeMinutes", "yAxisID", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "usePointStyle", "padding", "font", "size", "weight", "tooltip", "titleColor", "bodyColor", "borderWidth", "cornerRadius", "displayColors", "callbacks", "context", "dataset", "parsed", "y", "toLocaleString", "scales", "x", "grid", "display", "ticks", "color", "beginAtZero", "callback", "value", "y1", "type", "drawOnChartArea", "interaction", "intersect", "mode", "elements", "point", "hoverRadius", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "style", "width", "fontSize", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/ActivityChart.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Line } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n} from 'chart.js';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n);\n\nconst ActivityChart = ({ period = '7d' }) => {\n  const { getActivitySummary } = useHealthData();\n  const [chartData, setChartData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [selectedMetric, setSelectedMetric] = useState('steps');\n\n  useEffect(() => {\n    fetchChartData();\n  }, [period]);\n\n  const fetchChartData = async () => {\n    setLoading(true);\n    const result = await getActivitySummary(period);\n    \n    if (result.success) {\n      const data = result.data;\n      prepareChartData(data.dailyData || []);\n    }\n    setLoading(false);\n  };\n\n  const prepareChartData = (dailyData) => {\n    const labels = dailyData.map(item => {\n      const date = new Date(item.date);\n      return date.toLocaleDateString('en-US', { \n        month: 'short', \n        day: 'numeric' \n      });\n    });\n\n    const datasets = [];\n\n    if (selectedMetric === 'steps') {\n      datasets.push({\n        label: 'Steps',\n        data: dailyData.map(item => item.steps || 0),\n        borderColor: '#667eea',\n        backgroundColor: 'rgba(102, 126, 234, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#667eea',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'calories') {\n      datasets.push({\n        label: 'Calories',\n        data: dailyData.map(item => item.calories || 0),\n        borderColor: '#e74c3c',\n        backgroundColor: 'rgba(231, 76, 60, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#e74c3c',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'activeMinutes') {\n      datasets.push({\n        label: 'Active Minutes',\n        data: dailyData.map(item => item.activeMinutes || 0),\n        borderColor: '#f39c12',\n        backgroundColor: 'rgba(243, 156, 18, 0.1)',\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: '#f39c12',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 4,\n        pointHoverRadius: 6\n      });\n    } else if (selectedMetric === 'all') {\n      datasets.push(\n        {\n          label: 'Steps',\n          data: dailyData.map(item => item.steps || 0),\n          borderColor: '#667eea',\n          backgroundColor: 'rgba(102, 126, 234, 0.1)',\n          yAxisID: 'y',\n          tension: 0.4\n        },\n        {\n          label: 'Calories',\n          data: dailyData.map(item => item.calories || 0),\n          borderColor: '#e74c3c',\n          backgroundColor: 'rgba(231, 76, 60, 0.1)',\n          yAxisID: 'y1',\n          tension: 0.4\n        }\n      );\n    }\n\n    setChartData({\n      labels,\n      datasets\n    });\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12,\n            weight: '500'\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.95)',\n        titleColor: '#2c3e50',\n        bodyColor: '#2c3e50',\n        borderColor: 'rgba(102, 126, 234, 0.2)',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: true,\n        callbacks: {\n          label: function(context) {\n            let label = context.dataset.label || '';\n            if (label) {\n              label += ': ';\n            }\n            label += context.parsed.y.toLocaleString();\n            return label;\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false\n        },\n        ticks: {\n          color: '#7f8c8d',\n          font: {\n            size: 11\n          }\n        }\n      },\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)'\n        },\n        ticks: {\n          color: '#7f8c8d',\n          font: {\n            size: 11\n          },\n          callback: function(value) {\n            return value.toLocaleString();\n          }\n        }\n      },\n      ...(selectedMetric === 'all' && {\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          beginAtZero: true,\n          grid: {\n            drawOnChartArea: false,\n          },\n          ticks: {\n            color: '#7f8c8d',\n            font: {\n              size: 11\n            }\n          }\n        }\n      })\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index'\n    },\n    elements: {\n      point: {\n        hoverRadius: 8\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"chart-loading\">\n        <div className=\"loading-spinner\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"activity-chart\">\n      <div className=\"chart-controls\">\n        <select\n          value={selectedMetric}\n          onChange={(e) => setSelectedMetric(e.target.value)}\n          className=\"form-select\"\n          style={{ width: 'auto', fontSize: '0.9rem' }}\n        >\n          <option value=\"steps\">Steps</option>\n          <option value=\"calories\">Calories</option>\n          <option value=\"activeMinutes\">Active Minutes</option>\n          <option value=\"all\">All Metrics</option>\n        </select>\n      </div>\n\n      <div className=\"chart-container\">\n        {chartData ? (\n          <Line data={chartData} options={options} />\n        ) : (\n          <div className=\"no-data\">\n            <p>No activity data available</p>\n          </div>\n        )}\n      </div>\n\n      <style jsx>{`\n        .activity-chart {\n          height: 100%;\n        }\n\n        .chart-controls {\n          margin-bottom: 20px;\n          display: flex;\n          justify-content: flex-end;\n        }\n\n        .chart-container {\n          height: 300px;\n          position: relative;\n        }\n\n        .chart-loading {\n          height: 300px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .no-data {\n          height: 300px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #7f8c8d;\n          font-style: italic;\n        }\n\n        @media (max-width: 768px) {\n          .chart-container {\n            height: 250px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default ActivityChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,UAAU;AACjB,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9DX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,MACF,CAAC;AAED,MAAMK,aAAa,GAAGA,CAAC;EAAEC,MAAM,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAmB,CAAC,GAAGP,aAAa,CAAC,CAAC;EAC9C,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,OAAO,CAAC;EAE7DD,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;EAEZ,MAAMS,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCH,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMI,MAAM,GAAG,MAAMR,kBAAkB,CAACF,MAAM,CAAC;IAE/C,IAAIU,MAAM,CAACC,OAAO,EAAE;MAClB,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI;MACxBC,gBAAgB,CAACD,IAAI,CAACE,SAAS,IAAI,EAAE,CAAC;IACxC;IACAR,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMO,gBAAgB,GAAIC,SAAS,IAAK;IACtC,MAAMC,MAAM,GAAGD,SAAS,CAACE,GAAG,CAACC,IAAI,IAAI;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACC,IAAI,CAAC;MAChC,OAAOA,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMC,QAAQ,GAAG,EAAE;IAEnB,IAAIhB,cAAc,KAAK,OAAO,EAAE;MAC9BgB,QAAQ,CAACC,IAAI,CAAC;QACZC,KAAK,EAAE,OAAO;QACdb,IAAI,EAAEE,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,KAAK,IAAI,CAAC,CAAC;QAC5CC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,0BAA0B;QAC3CC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,GAAG;QACZC,oBAAoB,EAAE,SAAS;QAC/BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,CAAC;QACdC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI5B,cAAc,KAAK,UAAU,EAAE;MACxCgB,QAAQ,CAACC,IAAI,CAAC;QACZC,KAAK,EAAE,UAAU;QACjBb,IAAI,EAAEE,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACmB,QAAQ,IAAI,CAAC,CAAC;QAC/CT,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,wBAAwB;QACzCC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,GAAG;QACZC,oBAAoB,EAAE,SAAS;QAC/BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,CAAC;QACdC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI5B,cAAc,KAAK,eAAe,EAAE;MAC7CgB,QAAQ,CAACC,IAAI,CAAC;QACZC,KAAK,EAAE,gBAAgB;QACvBb,IAAI,EAAEE,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACoB,aAAa,IAAI,CAAC,CAAC;QACpDV,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,yBAAyB;QAC1CC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,GAAG;QACZC,oBAAoB,EAAE,SAAS;QAC/BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,CAAC;QACdC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI5B,cAAc,KAAK,KAAK,EAAE;MACnCgB,QAAQ,CAACC,IAAI,CACX;QACEC,KAAK,EAAE,OAAO;QACdb,IAAI,EAAEE,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,KAAK,IAAI,CAAC,CAAC;QAC5CC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,0BAA0B;QAC3CU,OAAO,EAAE,GAAG;QACZR,OAAO,EAAE;MACX,CAAC,EACD;QACEL,KAAK,EAAE,UAAU;QACjBb,IAAI,EAAEE,SAAS,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACmB,QAAQ,IAAI,CAAC,CAAC;QAC/CT,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,wBAAwB;QACzCU,OAAO,EAAE,IAAI;QACbR,OAAO,EAAE;MACX,CACF,CAAC;IACH;IAEA1B,YAAY,CAAC;MACXW,MAAM;MACNQ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,OAAO,GAAG;IACdC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,KAAK;QACf7B,MAAM,EAAE;UACN8B,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE;YACJC,IAAI,EAAE,EAAE;YACRC,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACDC,OAAO,EAAE;QACPtB,eAAe,EAAE,2BAA2B;QAC5CuB,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpBzB,WAAW,EAAE,0BAA0B;QACvC0B,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,IAAI;QACnBC,SAAS,EAAE;UACT/B,KAAK,EAAE,SAAAA,CAASgC,OAAO,EAAE;YACvB,IAAIhC,KAAK,GAAGgC,OAAO,CAACC,OAAO,CAACjC,KAAK,IAAI,EAAE;YACvC,IAAIA,KAAK,EAAE;cACTA,KAAK,IAAI,IAAI;YACf;YACAA,KAAK,IAAIgC,OAAO,CAACE,MAAM,CAACC,CAAC,CAACC,cAAc,CAAC,CAAC;YAC1C,OAAOpC,KAAK;UACd;QACF;MACF;IACF,CAAC;IACDqC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX,CAAC;QACDC,KAAK,EAAE;UACLC,KAAK,EAAE,SAAS;UAChBpB,IAAI,EAAE;YACJC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACDY,CAAC,EAAE;QACDQ,WAAW,EAAE,IAAI;QACjBJ,IAAI,EAAE;UACJG,KAAK,EAAE;QACT,CAAC;QACDD,KAAK,EAAE;UACLC,KAAK,EAAE,SAAS;UAChBpB,IAAI,EAAE;YACJC,IAAI,EAAE;UACR,CAAC;UACDqB,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;YACxB,OAAOA,KAAK,CAACT,cAAc,CAAC,CAAC;UAC/B;QACF;MACF,CAAC;MACD,IAAItD,cAAc,KAAK,KAAK,IAAI;QAC9BgE,EAAE,EAAE;UACFC,IAAI,EAAE,QAAQ;UACdP,OAAO,EAAE,IAAI;UACbrB,QAAQ,EAAE,OAAO;UACjBwB,WAAW,EAAE,IAAI;UACjBJ,IAAI,EAAE;YACJS,eAAe,EAAE;UACnB,CAAC;UACDP,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBpB,IAAI,EAAE;cACJC,IAAI,EAAE;YACR;UACF;QACF;MACF,CAAC;IACH,CAAC;IACD0B,WAAW,EAAE;MACXC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE;QACLC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EAED,IAAI1E,OAAO,EAAE;IACX,oBACER,OAAA;MAAKmF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BpF,OAAA;QAAKmF,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,oBACExF,OAAA;IAAKmF,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BpF,OAAA;MAAKmF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BpF,OAAA;QACEyE,KAAK,EAAE/D,cAAe;QACtB+E,QAAQ,EAAGC,CAAC,IAAK/E,iBAAiB,CAAC+E,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QACnDU,SAAS,EAAC,aAAa;QACvBS,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAS,CAAE;QAAAV,QAAA,gBAE7CpF,OAAA;UAAQyE,KAAK,EAAC,OAAO;UAAAW,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCxF,OAAA;UAAQyE,KAAK,EAAC,UAAU;UAAAW,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1CxF,OAAA;UAAQyE,KAAK,EAAC,eAAe;UAAAW,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACrDxF,OAAA;UAAQyE,KAAK,EAAC,KAAK;UAAAW,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENxF,OAAA;MAAKmF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7B9E,SAAS,gBACRN,OAAA,CAACb,IAAI;QAAC4B,IAAI,EAAET,SAAU;QAACoC,OAAO,EAAEA;MAAQ;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE3CxF,OAAA;QAAKmF,SAAS,EAAC,SAAS;QAAAC,QAAA,eACtBpF,OAAA;UAAAoF,QAAA,EAAG;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENxF,OAAA;MAAO+F,GAAG;MAAAX,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpF,EAAA,CA1QIF,aAAa;EAAA,QACcJ,aAAa;AAAA;AAAAkG,EAAA,GADxC9F,aAAa;AA4QnB,eAAeA,aAAa;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}