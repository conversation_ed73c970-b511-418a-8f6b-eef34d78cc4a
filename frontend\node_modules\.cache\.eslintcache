[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js": "20"}, {"size": 535, "mtime": 1751714883125, "results": "21", "hashOfConfig": "22"}, {"size": 2290, "mtime": 1751715809703, "results": "23", "hashOfConfig": "22"}, {"size": 362, "mtime": 1751714883417, "results": "24", "hashOfConfig": "22"}, {"size": 7468, "mtime": 1751716064240, "results": "25", "hashOfConfig": "22"}, {"size": 8153, "mtime": 1751716117517, "results": "26", "hashOfConfig": "22"}, {"size": 9097, "mtime": 1751716596842, "results": "27", "hashOfConfig": "22"}, {"size": 3584, "mtime": 1751719386980, "results": "28", "hashOfConfig": "22"}, {"size": 8307, "mtime": 1751716498372, "results": "29", "hashOfConfig": "22"}, {"size": 11390, "mtime": 1751716454334, "results": "30", "hashOfConfig": "22"}, {"size": 9670, "mtime": 1751716545536, "results": "31", "hashOfConfig": "22"}, {"size": 11971, "mtime": 1751716396489, "results": "32", "hashOfConfig": "22"}, {"size": 3746, "mtime": 1751719362286, "results": "33", "hashOfConfig": "22"}, {"size": 7708, "mtime": 1751715965545, "results": "34", "hashOfConfig": "22"}, {"size": 494, "mtime": 1751715973460, "results": "35", "hashOfConfig": "22"}, {"size": 3640, "mtime": 1751715990753, "results": "36", "hashOfConfig": "22"}, {"size": 3306, "mtime": 1751716137448, "results": "37", "hashOfConfig": "22"}, {"size": 6901, "mtime": 1751716181966, "results": "38", "hashOfConfig": "22"}, {"size": 9181, "mtime": 1751716230729, "results": "39", "hashOfConfig": "22"}, {"size": 8653, "mtime": 1751716322774, "results": "40", "hashOfConfig": "22"}, {"size": 9254, "mtime": 1751716273966, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4t21u5", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js", ["103", "104"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js", ["105"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js", ["106"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js", ["107"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js", ["108"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js", ["109", "110"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js", ["111"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js", [], [], {"ruleId": "112", "severity": 1, "message": "113", "line": 17, "column": 6, "nodeType": "114", "endLine": 17, "endColumn": 8, "suggestions": "115"}, {"ruleId": "116", "severity": 1, "message": "117", "line": 5, "column": 39, "nodeType": "118", "messageId": "119", "endLine": 5, "endColumn": 57}, {"ruleId": "112", "severity": 1, "message": "120", "line": 20, "column": 6, "nodeType": "114", "endLine": 20, "endColumn": 8, "suggestions": "121"}, {"ruleId": "112", "severity": 1, "message": "122", "line": 22, "column": 6, "nodeType": "114", "endLine": 22, "endColumn": 8, "suggestions": "123"}, {"ruleId": "112", "severity": 1, "message": "124", "line": 10, "column": 6, "nodeType": "114", "endLine": 10, "endColumn": 8, "suggestions": "125"}, {"ruleId": "112", "severity": 1, "message": "126", "line": 226, "column": 6, "nodeType": "114", "endLine": 226, "endColumn": 29, "suggestions": "127"}, {"ruleId": "112", "severity": 1, "message": "128", "line": 35, "column": 6, "nodeType": "114", "endLine": 35, "endColumn": 14, "suggestions": "129"}, {"ruleId": "116", "severity": 1, "message": "130", "line": 7, "column": 9, "nodeType": "118", "messageId": "119", "endLine": 7, "endColumn": 29}, {"ruleId": "116", "severity": 1, "message": "131", "line": 11, "column": 9, "nodeType": "118", "messageId": "119", "endLine": 11, "endColumn": 26}, {"ruleId": "116", "severity": 1, "message": "132", "line": 7, "column": 31, "nodeType": "118", "messageId": "119", "endLine": 7, "endColumn": 44}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["133"], "no-unused-vars", "'updateGoalProgress' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchGoals'. Either include it or remove the dependency array.", ["134"], "React Hook useEffect has a missing dependency: 'fetchHealthData'. Either include it or remove the dependency array.", ["135"], "React Hook useEffect has a missing dependency: 'fetchPredictions'. Either include it or remove the dependency array.", ["136"], "React Hook useEffect has missing dependencies: 'fetchDashboardData', 'fetchGoals', 'fetchHealthData', and 'fetchPredictions'. Either include them or remove the dependency array.", ["137"], "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", ["138"], "'handleProgressUpdate' is assigned a value but never used.", "'getRiskLevelColor' is assigned a value but never used.", "'addHealthData' is assigned a value but never used.", {"desc": "139", "fix": "140"}, {"desc": "141", "fix": "142"}, {"desc": "143", "fix": "144"}, {"desc": "145", "fix": "146"}, {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "151", "text": "152"}, "Update the dependencies array to be: [fetchGoals]", {"range": "153", "text": "154"}, "Update the dependencies array to be: [fetchHealthData]", {"range": "155", "text": "156"}, "Update the dependencies array to be: [fetchPredictions]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", {"range": "159", "text": "160"}, "Update the dependencies array to be: [fetchChartData, period]", {"range": "161", "text": "162"}, [686, 688], "[fetchDashboardData]", [583, 585], "[fetchGoals]", [678, 680], "[fetchHealthData]", [339, 341], "[fetchPredictions]", [7057, 7080], "[fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", [740, 748], "[fetchChartData, period]"]