[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\AuthDebug.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoogleFitIntegration.js": "23"}, {"size": 535, "mtime": 1751714883125, "results": "24", "hashOfConfig": "25"}, {"size": 2473, "mtime": 1751720861904, "results": "26", "hashOfConfig": "25"}, {"size": 362, "mtime": 1751714883417, "results": "27", "hashOfConfig": "25"}, {"size": 7468, "mtime": 1751716064240, "results": "28", "hashOfConfig": "25"}, {"size": 8153, "mtime": 1751716117517, "results": "29", "hashOfConfig": "25"}, {"size": 12450, "mtime": 1751781947505, "results": "30", "hashOfConfig": "25"}, {"size": 4347, "mtime": 1751720792940, "results": "31", "hashOfConfig": "25"}, {"size": 8307, "mtime": 1751716498372, "results": "32", "hashOfConfig": "25"}, {"size": 11390, "mtime": 1751716454334, "results": "33", "hashOfConfig": "25"}, {"size": 9670, "mtime": 1751716545536, "results": "34", "hashOfConfig": "25"}, {"size": 11971, "mtime": 1751716396489, "results": "35", "hashOfConfig": "25"}, {"size": 5529, "mtime": 1751720775894, "results": "36", "hashOfConfig": "25"}, {"size": 7708, "mtime": 1751715965545, "results": "37", "hashOfConfig": "25"}, {"size": 983, "mtime": 1751720382347, "results": "38", "hashOfConfig": "25"}, {"size": 3640, "mtime": 1751715990753, "results": "39", "hashOfConfig": "25"}, {"size": 3306, "mtime": 1751716137448, "results": "40", "hashOfConfig": "25"}, {"size": 6901, "mtime": 1751716181966, "results": "41", "hashOfConfig": "25"}, {"size": 9181, "mtime": 1751716230729, "results": "42", "hashOfConfig": "25"}, {"size": 8653, "mtime": 1751716322774, "results": "43", "hashOfConfig": "25"}, {"size": 9254, "mtime": 1751716273966, "results": "44", "hashOfConfig": "25"}, {"size": 6306, "mtime": 1751720049811, "results": "45", "hashOfConfig": "25"}, {"size": 1040, "mtime": 1751720812364, "results": "46", "hashOfConfig": "25"}, {"size": 9811, "mtime": 1751781773222, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4t21u5", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js", ["117"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js", ["118", "119"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js", ["120"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js", ["121"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js", ["122"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js", ["123"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js", ["124"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js", ["125", "126"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js", ["127"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\AuthDebug.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoogleFitIntegration.js", [], [], {"ruleId": "128", "severity": 1, "message": "129", "line": 17, "column": 6, "nodeType": "130", "endLine": 17, "endColumn": 8, "suggestions": "131"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 5, "column": 39, "nodeType": "134", "messageId": "135", "endLine": 5, "endColumn": 57}, {"ruleId": "128", "severity": 1, "message": "136", "line": 20, "column": 6, "nodeType": "130", "endLine": 20, "endColumn": 8, "suggestions": "137"}, {"ruleId": "128", "severity": 1, "message": "138", "line": 22, "column": 6, "nodeType": "130", "endLine": 22, "endColumn": 8, "suggestions": "139"}, {"ruleId": "128", "severity": 1, "message": "140", "line": 10, "column": 6, "nodeType": "130", "endLine": 10, "endColumn": 8, "suggestions": "141"}, {"ruleId": "128", "severity": 1, "message": "142", "line": 60, "column": 6, "nodeType": "130", "endLine": 60, "endColumn": 15, "suggestions": "143"}, {"ruleId": "128", "severity": 1, "message": "144", "line": 226, "column": 6, "nodeType": "130", "endLine": 226, "endColumn": 29, "suggestions": "145"}, {"ruleId": "128", "severity": 1, "message": "146", "line": 35, "column": 6, "nodeType": "130", "endLine": 35, "endColumn": 14, "suggestions": "147"}, {"ruleId": "132", "severity": 1, "message": "148", "line": 7, "column": 9, "nodeType": "134", "messageId": "135", "endLine": 7, "endColumn": 29}, {"ruleId": "132", "severity": 1, "message": "149", "line": 11, "column": 9, "nodeType": "134", "messageId": "135", "endLine": 11, "endColumn": 26}, {"ruleId": "132", "severity": 1, "message": "150", "line": 7, "column": 31, "nodeType": "134", "messageId": "135", "endLine": 7, "endColumn": 44}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["151"], "no-unused-vars", "'updateGoalProgress' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchGoals'. Either include it or remove the dependency array.", ["152"], "React Hook useEffect has a missing dependency: 'fetchHealthData'. Either include it or remove the dependency array.", ["153"], "React Hook useEffect has a missing dependency: 'fetchPredictions'. Either include it or remove the dependency array.", ["154"], "React Hook useEffect has a missing dependency: 'token'. Either include it or remove the dependency array.", ["155"], "React Hook useEffect has missing dependencies: 'fetchDashboardData', 'fetchGoals', 'fetchHealthData', and 'fetchPredictions'. Either include them or remove the dependency array.", ["156"], "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", ["157"], "'handleProgressUpdate' is assigned a value but never used.", "'getRiskLevelColor' is assigned a value but never used.", "'addHealthData' is assigned a value but never used.", {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "172", "text": "173"}, "Update the dependencies array to be: [fetchGoals]", {"range": "174", "text": "175"}, "Update the dependencies array to be: [fetchHealthData]", {"range": "176", "text": "177"}, "Update the dependencies array to be: [fetchPredictions]", {"range": "178", "text": "179"}, "Update the dependencies array to be: [loading, token]", {"range": "180", "text": "181"}, "Update the dependencies array to be: [fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [fetchChartData, period]", {"range": "184", "text": "185"}, [686, 688], "[fetchDashboardData]", [583, 585], "[fetchGoals]", [678, 680], "[fetchHealthData]", [339, 341], "[fetchPredictions]", [2043, 2052], "[loading, token]", [7057, 7080], "[fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", [740, 748], "[fetchChartData, period]"]