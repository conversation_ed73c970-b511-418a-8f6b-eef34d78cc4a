[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\AuthDebug.js": "22"}, {"size": 535, "mtime": 1751714883125, "results": "23", "hashOfConfig": "24"}, {"size": 2473, "mtime": 1751720861904, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1751714883417, "results": "26", "hashOfConfig": "24"}, {"size": 7468, "mtime": 1751716064240, "results": "27", "hashOfConfig": "24"}, {"size": 8153, "mtime": 1751716117517, "results": "28", "hashOfConfig": "24"}, {"size": 9097, "mtime": 1751716596842, "results": "29", "hashOfConfig": "24"}, {"size": 4347, "mtime": 1751720792940, "results": "30", "hashOfConfig": "24"}, {"size": 8307, "mtime": 1751716498372, "results": "31", "hashOfConfig": "24"}, {"size": 11390, "mtime": 1751716454334, "results": "32", "hashOfConfig": "24"}, {"size": 9670, "mtime": 1751716545536, "results": "33", "hashOfConfig": "24"}, {"size": 11971, "mtime": 1751716396489, "results": "34", "hashOfConfig": "24"}, {"size": 5529, "mtime": 1751720775894, "results": "35", "hashOfConfig": "24"}, {"size": 7708, "mtime": 1751715965545, "results": "36", "hashOfConfig": "24"}, {"size": 983, "mtime": 1751720382347, "results": "37", "hashOfConfig": "24"}, {"size": 3640, "mtime": 1751715990753, "results": "38", "hashOfConfig": "24"}, {"size": 3306, "mtime": 1751716137448, "results": "39", "hashOfConfig": "24"}, {"size": 6901, "mtime": 1751716181966, "results": "40", "hashOfConfig": "24"}, {"size": 9181, "mtime": 1751716230729, "results": "41", "hashOfConfig": "24"}, {"size": 8653, "mtime": 1751716322774, "results": "42", "hashOfConfig": "24"}, {"size": 9254, "mtime": 1751716273966, "results": "43", "hashOfConfig": "24"}, {"size": 6306, "mtime": 1751720049811, "results": "44", "hashOfConfig": "24"}, {"size": 1040, "mtime": 1751720812364, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4t21u5", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js", ["112"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js", ["113", "114"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js", ["115"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js", ["116"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js", ["117"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js", ["118"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js", ["119"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js", ["120", "121"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js", ["122"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\AuthDebug.js", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 17, "column": 6, "nodeType": "125", "endLine": 17, "endColumn": 8, "suggestions": "126"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 5, "column": 39, "nodeType": "129", "messageId": "130", "endLine": 5, "endColumn": 57}, {"ruleId": "123", "severity": 1, "message": "131", "line": 20, "column": 6, "nodeType": "125", "endLine": 20, "endColumn": 8, "suggestions": "132"}, {"ruleId": "123", "severity": 1, "message": "133", "line": 22, "column": 6, "nodeType": "125", "endLine": 22, "endColumn": 8, "suggestions": "134"}, {"ruleId": "123", "severity": 1, "message": "135", "line": 10, "column": 6, "nodeType": "125", "endLine": 10, "endColumn": 8, "suggestions": "136"}, {"ruleId": "123", "severity": 1, "message": "137", "line": 60, "column": 6, "nodeType": "125", "endLine": 60, "endColumn": 15, "suggestions": "138"}, {"ruleId": "123", "severity": 1, "message": "139", "line": 226, "column": 6, "nodeType": "125", "endLine": 226, "endColumn": 29, "suggestions": "140"}, {"ruleId": "123", "severity": 1, "message": "141", "line": 35, "column": 6, "nodeType": "125", "endLine": 35, "endColumn": 14, "suggestions": "142"}, {"ruleId": "127", "severity": 1, "message": "143", "line": 7, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 7, "endColumn": 29}, {"ruleId": "127", "severity": 1, "message": "144", "line": 11, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 11, "endColumn": 26}, {"ruleId": "127", "severity": 1, "message": "145", "line": 7, "column": 31, "nodeType": "129", "messageId": "130", "endLine": 7, "endColumn": 44}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["146"], "no-unused-vars", "'updateGoalProgress' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchGoals'. Either include it or remove the dependency array.", ["147"], "React Hook useEffect has a missing dependency: 'fetchHealthData'. Either include it or remove the dependency array.", ["148"], "React Hook useEffect has a missing dependency: 'fetchPredictions'. Either include it or remove the dependency array.", ["149"], "React Hook useEffect has a missing dependency: 'token'. Either include it or remove the dependency array.", ["150"], "React Hook useEffect has missing dependencies: 'fetchDashboardData', 'fetchGoals', 'fetchHealthData', and 'fetchPredictions'. Either include them or remove the dependency array.", ["151"], "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", ["152"], "'handleProgressUpdate' is assigned a value but never used.", "'getRiskLevelColor' is assigned a value but never used.", "'addHealthData' is assigned a value but never used.", {"desc": "153", "fix": "154"}, {"desc": "155", "fix": "156"}, {"desc": "157", "fix": "158"}, {"desc": "159", "fix": "160"}, {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "167", "text": "168"}, "Update the dependencies array to be: [fetchGoals]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [fetchHealthData]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [fetchPredictions]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [loading, token]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [fetchChartData, period]", {"range": "179", "text": "180"}, [686, 688], "[fetchDashboardData]", [583, 585], "[fetchGoals]", [678, 680], "[fetchHealthData]", [339, 341], "[fetchPredictions]", [2043, 2052], "[loading, token]", [7057, 7080], "[fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", [740, 748], "[fetchChartData, period]"]