[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js": "21"}, {"size": 535, "mtime": 1751714883125, "results": "22", "hashOfConfig": "23"}, {"size": 2399, "mtime": 1751720093045, "results": "24", "hashOfConfig": "23"}, {"size": 362, "mtime": 1751714883417, "results": "25", "hashOfConfig": "23"}, {"size": 7468, "mtime": 1751716064240, "results": "26", "hashOfConfig": "23"}, {"size": 8153, "mtime": 1751716117517, "results": "27", "hashOfConfig": "23"}, {"size": 9097, "mtime": 1751716596842, "results": "28", "hashOfConfig": "23"}, {"size": 3584, "mtime": 1751719386980, "results": "29", "hashOfConfig": "23"}, {"size": 8307, "mtime": 1751716498372, "results": "30", "hashOfConfig": "23"}, {"size": 11390, "mtime": 1751716454334, "results": "31", "hashOfConfig": "23"}, {"size": 9670, "mtime": 1751716545536, "results": "32", "hashOfConfig": "23"}, {"size": 11971, "mtime": 1751716396489, "results": "33", "hashOfConfig": "23"}, {"size": 3746, "mtime": 1751719362286, "results": "34", "hashOfConfig": "23"}, {"size": 7708, "mtime": 1751715965545, "results": "35", "hashOfConfig": "23"}, {"size": 494, "mtime": 1751715973460, "results": "36", "hashOfConfig": "23"}, {"size": 3640, "mtime": 1751715990753, "results": "37", "hashOfConfig": "23"}, {"size": 3306, "mtime": 1751716137448, "results": "38", "hashOfConfig": "23"}, {"size": 6901, "mtime": 1751716181966, "results": "39", "hashOfConfig": "23"}, {"size": 9181, "mtime": 1751716230729, "results": "40", "hashOfConfig": "23"}, {"size": 8653, "mtime": 1751716322774, "results": "41", "hashOfConfig": "23"}, {"size": 9254, "mtime": 1751716273966, "results": "42", "hashOfConfig": "23"}, {"size": 6306, "mtime": 1751720049811, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4t21u5", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Dashboard.js", ["107"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Goals.js", ["108", "109"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\HealthData.js", ["110"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Predictions.js", ["111"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\pages\\Chatbot.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\contexts\\HealthDataContext.js", ["112"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ActivityChart.js", ["113"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\GoalProgress.js", ["114", "115"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\QuickActions.js", ["116"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\HealthInsights.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\mini_project\\frontend\\src\\components\\ApiTest.js", [], [], {"ruleId": "117", "severity": 1, "message": "118", "line": 17, "column": 6, "nodeType": "119", "endLine": 17, "endColumn": 8, "suggestions": "120"}, {"ruleId": "121", "severity": 1, "message": "122", "line": 5, "column": 39, "nodeType": "123", "messageId": "124", "endLine": 5, "endColumn": 57}, {"ruleId": "117", "severity": 1, "message": "125", "line": 20, "column": 6, "nodeType": "119", "endLine": 20, "endColumn": 8, "suggestions": "126"}, {"ruleId": "117", "severity": 1, "message": "127", "line": 22, "column": 6, "nodeType": "119", "endLine": 22, "endColumn": 8, "suggestions": "128"}, {"ruleId": "117", "severity": 1, "message": "129", "line": 10, "column": 6, "nodeType": "119", "endLine": 10, "endColumn": 8, "suggestions": "130"}, {"ruleId": "117", "severity": 1, "message": "131", "line": 226, "column": 6, "nodeType": "119", "endLine": 226, "endColumn": 29, "suggestions": "132"}, {"ruleId": "117", "severity": 1, "message": "133", "line": 35, "column": 6, "nodeType": "119", "endLine": 35, "endColumn": 14, "suggestions": "134"}, {"ruleId": "121", "severity": 1, "message": "135", "line": 7, "column": 9, "nodeType": "123", "messageId": "124", "endLine": 7, "endColumn": 29}, {"ruleId": "121", "severity": 1, "message": "136", "line": 11, "column": 9, "nodeType": "123", "messageId": "124", "endLine": 11, "endColumn": 26}, {"ruleId": "121", "severity": 1, "message": "137", "line": 7, "column": 31, "nodeType": "123", "messageId": "124", "endLine": 7, "endColumn": 44}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["138"], "no-unused-vars", "'updateGoalProgress' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchGoals'. Either include it or remove the dependency array.", ["139"], "React Hook useEffect has a missing dependency: 'fetchHealthData'. Either include it or remove the dependency array.", ["140"], "React Hook useEffect has a missing dependency: 'fetchPredictions'. Either include it or remove the dependency array.", ["141"], "React Hook useEffect has missing dependencies: 'fetchDashboardData', 'fetchGoals', 'fetchHealthData', and 'fetchPredictions'. Either include them or remove the dependency array.", ["142"], "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", ["143"], "'handleProgressUpdate' is assigned a value but never used.", "'getRiskLevelColor' is assigned a value but never used.", "'addHealthData' is assigned a value but never used.", {"desc": "144", "fix": "145"}, {"desc": "146", "fix": "147"}, {"desc": "148", "fix": "149"}, {"desc": "150", "fix": "151"}, {"desc": "152", "fix": "153"}, {"desc": "154", "fix": "155"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "156", "text": "157"}, "Update the dependencies array to be: [fetchGoals]", {"range": "158", "text": "159"}, "Update the dependencies array to be: [fetchHealthData]", {"range": "160", "text": "161"}, "Update the dependencies array to be: [fetchPredictions]", {"range": "162", "text": "163"}, "Update the dependencies array to be: [fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", {"range": "164", "text": "165"}, "Update the dependencies array to be: [fetchChartData, period]", {"range": "166", "text": "167"}, [686, 688], "[fetchDashboardData]", [583, 585], "[fetchGoals]", [678, 680], "[fetchHealthData]", [339, 341], "[fetchPredictions]", [7057, 7080], "[fetchDashboardData, fetchGoals, fetchHealthData, fetchPredictions, isAuthenticated, user]", [740, 748], "[fetchChartData, period]"]