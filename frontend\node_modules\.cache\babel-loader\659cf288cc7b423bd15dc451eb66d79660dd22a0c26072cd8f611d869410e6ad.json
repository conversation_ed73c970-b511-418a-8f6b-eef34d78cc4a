{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _user$name, _user$activityLevel;\n  const {\n    user,\n    updateProfile,\n    changePassword\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profileData, setProfileData] = useState({\n    name: (user === null || user === void 0 ? void 0 : user.name) || '',\n    height: (user === null || user === void 0 ? void 0 : user.height) || '',\n    weight: (user === null || user === void 0 ? void 0 : user.weight) || '',\n    activityLevel: (user === null || user === void 0 ? void 0 : user.activityLevel) || 'moderately_active'\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [message, setMessage] = useState('');\n  const handleProfileUpdate = async e => {\n    e.preventDefault();\n    const result = await updateProfile(profileData);\n    setMessage(result.success ? 'Profile updated successfully!' : result.message);\n  };\n  const handlePasswordChange = async e => {\n    e.preventDefault();\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setMessage('New passwords do not match');\n      return;\n    }\n    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);\n    setMessage(result.success ? 'Password changed successfully!' : result.message);\n    if (result.success) {\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()) || '👤'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: user === null || user === void 0 ? void 0 : user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: user === null || user === void 0 ? void 0 : user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Age: \", (user === null || user === void 0 ? void 0 : user.age) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"BMI: \", (user === null || user === void 0 ? void 0 : user.bmi) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Activity: \", (user === null || user === void 0 ? void 0 : (_user$activityLevel = user.activityLevel) === null || _user$activityLevel === void 0 ? void 0 : _user$activityLevel.replace('_', ' ')) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'profile' ? 'active' : ''}`,\n          onClick: () => setActiveTab('profile'),\n          children: \"Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'password' ? 'active' : ''}`,\n          onClick: () => setActiveTab('password'),\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'integrations' ? 'active' : ''}`,\n          onClick: () => setActiveTab('integrations'),\n          children: \"\\uD83C\\uDFC3\\u200D\\u2642\\uFE0F Device Integration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `alert ${message.includes('success') ? 'alert-success' : 'alert-danger'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this), activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleProfileUpdate,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                value: profileData.name,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  name: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                className: \"form-input\",\n                value: (user === null || user === void 0 ? void 0 : user.email) || '',\n                disabled: true,\n                style: {\n                  background: '#f8f9fa',\n                  color: '#6c757d'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Height (cm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-input\",\n                value: profileData.height,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  height: e.target.value\n                }),\n                min: \"50\",\n                max: \"300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Weight (kg)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-input\",\n                value: profileData.weight,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  weight: e.target.value\n                }),\n                min: \"20\",\n                max: \"500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Activity Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select\",\n              value: profileData.activityLevel,\n              onChange: e => setProfileData({\n                ...profileData,\n                activityLevel: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"sedentary\",\n                children: \"Sedentary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"lightly_active\",\n                children: \"Lightly Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"moderately_active\",\n                children: \"Moderately Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"very_active\",\n                children: \"Very Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"extremely_active\",\n                children: \"Extremely Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Update Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), activeTab === 'password' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handlePasswordChange,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Current Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.currentPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                currentPassword: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.newPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                newPassword: e.target.value\n              }),\n              required: true,\n              minLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Confirm New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.confirmPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                confirmPassword: e.target.value\n              }),\n              required: true,\n              minLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Change Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this), activeTab === 'integrations' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(GoogleFitIntegration, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"integration-info\",\n          style: {\n            marginTop: '30px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCF1 Supported Devices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83C\\uDFC3\\u200D\\u2642\\uFE0F Google Fit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Sync data from any device connected to Google Fit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Android Wear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Fitbit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Garmin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Samsung Health\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-card coming-soon\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u231A Apple Health\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Direct integration with Apple HealthKit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge\",\n                children: \"Coming Soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-card coming-soon\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDCCA Fitbit Direct\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Direct Fitbit API integration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge\",\n                children: \"Coming Soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .profile-page { padding: 0; }\n        .profile-container { max-width: 800px; margin: 0 auto; }\n        .profile-header { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; margin-bottom: 30px; display: flex; align-items: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .user-avatar { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700; margin-right: 25px; }\n        .user-info h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .user-info p { margin: 0 0 10px 0; color: #7f8c8d; }\n        .user-stats { display: flex; gap: 20px; font-size: 0.9rem; color: #667eea; }\n        .profile-tabs { display: flex; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 5px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-button { flex: 1; padding: 15px 20px; border: none; background: none; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; color: #7f8c8d; }\n        .tab-button.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n        .tab-content { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-content h2 { margin: 0 0 25px 0; color: #2c3e50; }\n        .device-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 20px;\n          margin-top: 20px;\n        }\n\n        .device-card {\n          background: white;\n          border: 1px solid #e0e0e0;\n          border-radius: 12px;\n          padding: 20px;\n          position: relative;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .device-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n        }\n\n        .device-card.coming-soon {\n          opacity: 0.6;\n          background: #f8f9fa;\n        }\n\n        .device-card h4 {\n          margin: 0 0 10px 0;\n          color: #333;\n        }\n\n        .device-card p {\n          color: #666;\n          margin: 0 0 15px 0;\n          font-size: 0.9rem;\n        }\n\n        .device-card ul {\n          list-style: none;\n          padding: 0;\n          margin: 0;\n        }\n\n        .device-card li {\n          padding: 4px 0;\n          color: #555;\n          font-size: 0.85rem;\n        }\n\n        .device-card li:before {\n          content: \"✓ \";\n          color: #28a745;\n          font-weight: bold;\n        }\n\n        .badge {\n          position: absolute;\n          top: 10px;\n          right: 10px;\n          background: #ffc107;\n          color: #333;\n          padding: 4px 8px;\n          border-radius: 12px;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .integration-info {\n          background: #f8f9fa;\n          border-radius: 12px;\n          padding: 25px;\n          border: 1px solid #e9ecef;\n        }\n\n        .integration-info h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n        }\n\n        @media (max-width: 768px) {\n          .profile-header { flex-direction: column; text-align: center; }\n          .user-avatar { margin-right: 0; margin-bottom: 20px; }\n          .user-stats { justify-content: center; flex-wrap: wrap; }\n          .profile-tabs { flex-direction: column; }\n          .device-grid { grid-template-columns: 1fr; }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"B/jj/9b5Ob7Bk7TnOpej7siblFE=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "GoogleFitIntegration", "jsxDEV", "_jsxDEV", "Profile", "_s", "_user$name", "_user$activityLevel", "user", "updateProfile", "changePassword", "activeTab", "setActiveTab", "profileData", "setProfileData", "name", "height", "weight", "activityLevel", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "message", "setMessage", "handleProfileUpdate", "e", "preventDefault", "result", "success", "handlePasswordChange", "className", "children", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "age", "bmi", "replace", "onClick", "includes", "onSubmit", "type", "value", "onChange", "target", "required", "disabled", "style", "background", "color", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "marginTop", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\n\nconst Profile = () => {\n  const { user, updateProfile, changePassword } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profileData, setProfileData] = useState({\n    name: user?.name || '',\n    height: user?.height || '',\n    weight: user?.weight || '',\n    activityLevel: user?.activityLevel || 'moderately_active'\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [message, setMessage] = useState('');\n\n  const handleProfileUpdate = async (e) => {\n    e.preventDefault();\n    const result = await updateProfile(profileData);\n    setMessage(result.success ? 'Profile updated successfully!' : result.message);\n  };\n\n  const handlePasswordChange = async (e) => {\n    e.preventDefault();\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setMessage('New passwords do not match');\n      return;\n    }\n    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);\n    setMessage(result.success ? 'Password changed successfully!' : result.message);\n    if (result.success) {\n      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n    }\n  };\n\n  return (\n    <div className=\"profile-page\">\n      <div className=\"profile-container\">\n        <div className=\"profile-header\">\n          <div className=\"user-avatar\">\n            {user?.name?.charAt(0).toUpperCase() || '👤'}\n          </div>\n          <div className=\"user-info\">\n            <h1>{user?.name}</h1>\n            <p>{user?.email}</p>\n            <div className=\"user-stats\">\n              <span>Age: {user?.age || 'N/A'}</span>\n              <span>BMI: {user?.bmi || 'N/A'}</span>\n              <span>Activity: {user?.activityLevel?.replace('_', ' ') || 'N/A'}</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"profile-tabs\">\n          <button \n            className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}\n            onClick={() => setActiveTab('profile')}\n          >\n            Profile Settings\n          </button>\n          <button\n            className={`tab-button ${activeTab === 'password' ? 'active' : ''}`}\n            onClick={() => setActiveTab('password')}\n          >\n            Change Password\n          </button>\n          <button\n            className={`tab-button ${activeTab === 'integrations' ? 'active' : ''}`}\n            onClick={() => setActiveTab('integrations')}\n          >\n            🏃‍♂️ Device Integration\n          </button>\n        </div>\n\n        {message && (\n          <div className={`alert ${message.includes('success') ? 'alert-success' : 'alert-danger'}`}>\n            {message}\n          </div>\n        )}\n\n        {activeTab === 'profile' && (\n          <div className=\"tab-content\">\n            <h2>Profile Information</h2>\n            <form onSubmit={handleProfileUpdate}>\n              <div className=\"grid grid-2\">\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"form-input\"\n                    value={profileData.name}\n                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}\n                    required\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    className=\"form-input\"\n                    value={user?.email || ''}\n                    disabled\n                    style={{ background: '#f8f9fa', color: '#6c757d' }}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Height (cm)</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-input\"\n                    value={profileData.height}\n                    onChange={(e) => setProfileData({...profileData, height: e.target.value})}\n                    min=\"50\"\n                    max=\"300\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Weight (kg)</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-input\"\n                    value={profileData.weight}\n                    onChange={(e) => setProfileData({...profileData, weight: e.target.value})}\n                    min=\"20\"\n                    max=\"500\"\n                  />\n                </div>\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Activity Level</label>\n                <select\n                  className=\"form-select\"\n                  value={profileData.activityLevel}\n                  onChange={(e) => setProfileData({...profileData, activityLevel: e.target.value})}\n                >\n                  <option value=\"sedentary\">Sedentary</option>\n                  <option value=\"lightly_active\">Lightly Active</option>\n                  <option value=\"moderately_active\">Moderately Active</option>\n                  <option value=\"very_active\">Very Active</option>\n                  <option value=\"extremely_active\">Extremely Active</option>\n                </select>\n              </div>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Update Profile\n              </button>\n            </form>\n          </div>\n        )}\n\n        {activeTab === 'password' && (\n          <div className=\"tab-content\">\n            <h2>Change Password</h2>\n            <form onSubmit={handlePasswordChange}>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Current Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.currentPassword}\n                  onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">New Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.newPassword}\n                  onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}\n                  required\n                  minLength=\"6\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Confirm New Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.confirmPassword}\n                  onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}\n                  required\n                  minLength=\"6\"\n                />\n              </div>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Change Password\n              </button>\n            </form>\n          </div>\n        )}\n\n        {activeTab === 'integrations' && (\n          <div className=\"tab-content\">\n            <GoogleFitIntegration />\n\n            <div className=\"integration-info\" style={{ marginTop: '30px' }}>\n              <h3>📱 Supported Devices</h3>\n              <div className=\"device-grid\">\n                <div className=\"device-card\">\n                  <h4>🏃‍♂️ Google Fit</h4>\n                  <p>Sync data from any device connected to Google Fit</p>\n                  <ul>\n                    <li>Android Wear</li>\n                    <li>Fitbit</li>\n                    <li>Garmin</li>\n                    <li>Samsung Health</li>\n                  </ul>\n                </div>\n                <div className=\"device-card coming-soon\">\n                  <h4>⌚ Apple Health</h4>\n                  <p>Direct integration with Apple HealthKit</p>\n                  <span className=\"badge\">Coming Soon</span>\n                </div>\n                <div className=\"device-card coming-soon\">\n                  <h4>📊 Fitbit Direct</h4>\n                  <p>Direct Fitbit API integration</p>\n                  <span className=\"badge\">Coming Soon</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      <style jsx>{`\n        .profile-page { padding: 0; }\n        .profile-container { max-width: 800px; margin: 0 auto; }\n        .profile-header { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; margin-bottom: 30px; display: flex; align-items: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .user-avatar { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700; margin-right: 25px; }\n        .user-info h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .user-info p { margin: 0 0 10px 0; color: #7f8c8d; }\n        .user-stats { display: flex; gap: 20px; font-size: 0.9rem; color: #667eea; }\n        .profile-tabs { display: flex; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 5px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-button { flex: 1; padding: 15px 20px; border: none; background: none; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; color: #7f8c8d; }\n        .tab-button.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n        .tab-content { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-content h2 { margin: 0 0 25px 0; color: #2c3e50; }\n        .device-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 20px;\n          margin-top: 20px;\n        }\n\n        .device-card {\n          background: white;\n          border: 1px solid #e0e0e0;\n          border-radius: 12px;\n          padding: 20px;\n          position: relative;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .device-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n        }\n\n        .device-card.coming-soon {\n          opacity: 0.6;\n          background: #f8f9fa;\n        }\n\n        .device-card h4 {\n          margin: 0 0 10px 0;\n          color: #333;\n        }\n\n        .device-card p {\n          color: #666;\n          margin: 0 0 15px 0;\n          font-size: 0.9rem;\n        }\n\n        .device-card ul {\n          list-style: none;\n          padding: 0;\n          margin: 0;\n        }\n\n        .device-card li {\n          padding: 4px 0;\n          color: #555;\n          font-size: 0.85rem;\n        }\n\n        .device-card li:before {\n          content: \"✓ \";\n          color: #28a745;\n          font-weight: bold;\n        }\n\n        .badge {\n          position: absolute;\n          top: 10px;\n          right: 10px;\n          background: #ffc107;\n          color: #333;\n          padding: 4px 8px;\n          border-radius: 12px;\n          font-size: 0.75rem;\n          font-weight: 600;\n        }\n\n        .integration-info {\n          background: #f8f9fa;\n          border-radius: 12px;\n          padding: 25px;\n          border: 1px solid #e9ecef;\n        }\n\n        .integration-info h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n        }\n\n        @media (max-width: 768px) {\n          .profile-header { flex-direction: column; text-align: center; }\n          .user-avatar { margin-right: 0; margin-bottom: 20px; }\n          .user-stats { justify-content: center; flex-wrap: wrap; }\n          .profile-tabs { flex-direction: column; }\n          .device-grid { grid-template-columns: 1fr; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,mBAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAGV,OAAO,CAAC,CAAC;EACzD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC7CgB,IAAI,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,KAAI,EAAE;IACtBC,MAAM,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,MAAM,KAAI,EAAE;IAC1BC,MAAM,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,KAAI,EAAE;IAC1BC,aAAa,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,aAAa,KAAI;EACxC,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC;IAC/CsB,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM2B,mBAAmB,GAAG,MAAOC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,MAAMpB,aAAa,CAACI,WAAW,CAAC;IAC/CY,UAAU,CAACI,MAAM,CAACC,OAAO,GAAG,+BAA+B,GAAGD,MAAM,CAACL,OAAO,CAAC;EAC/E,CAAC;EAED,MAAMO,oBAAoB,GAAG,MAAOJ,CAAC,IAAK;IACxCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIT,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC7DE,UAAU,CAAC,4BAA4B,CAAC;MACxC;IACF;IACA,MAAMI,MAAM,GAAG,MAAMnB,cAAc,CAACS,YAAY,CAACE,eAAe,EAAEF,YAAY,CAACG,WAAW,CAAC;IAC3FG,UAAU,CAACI,MAAM,CAACC,OAAO,GAAG,gCAAgC,GAAGD,MAAM,CAACL,OAAO,CAAC;IAC9E,IAAIK,MAAM,CAACC,OAAO,EAAE;MAClBV,eAAe,CAAC;QAAEC,eAAe,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,eAAe,EAAE;MAAG,CAAC,CAAC;IAChF;EACF,CAAC;EAED,oBACEpB,OAAA;IAAK6B,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B9B,OAAA;MAAK6B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9B,OAAA;QAAK6B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9B,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB,CAAAzB,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEO,IAAI,cAAAT,UAAA,uBAAVA,UAAA,CAAY4B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNpC,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9B,OAAA;YAAA8B,QAAA,EAAKzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;UAAI;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBpC,OAAA;YAAA8B,QAAA,EAAIzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAA8B,QAAA,GAAM,OAAK,EAAC,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,GAAG,KAAI,KAAK;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpC,OAAA;cAAA8B,QAAA,GAAM,OAAK,EAAC,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,GAAG,KAAI,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpC,OAAA;cAAA8B,QAAA,GAAM,YAAU,EAAC,CAAAzB,IAAI,aAAJA,IAAI,wBAAAD,mBAAA,GAAJC,IAAI,CAAEU,aAAa,cAAAX,mBAAA,uBAAnBA,mBAAA,CAAqBoC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI,KAAK;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAK6B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9B,OAAA;UACE6B,SAAS,EAAE,cAAcrB,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;UACnEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,SAAS,CAAE;UAAAqB,QAAA,EACxC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpC,OAAA;UACE6B,SAAS,EAAE,cAAcrB,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACpEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,UAAU,CAAE;UAAAqB,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpC,OAAA;UACE6B,SAAS,EAAE,cAAcrB,SAAS,KAAK,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;UACxEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,cAAc,CAAE;UAAAqB,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELf,OAAO,iBACNrB,OAAA;QAAK6B,SAAS,EAAE,SAASR,OAAO,CAACqB,QAAQ,CAAC,SAAS,CAAC,GAAG,eAAe,GAAG,cAAc,EAAG;QAAAZ,QAAA,EACvFT;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAEA5B,SAAS,KAAK,SAAS,iBACtBR,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAA8B,QAAA,EAAI;QAAmB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BpC,OAAA;UAAM2C,QAAQ,EAAEpB,mBAAoB;UAAAO,QAAA,gBAClC9B,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/CpC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACE,IAAK;gBACxBkC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEE,IAAI,EAAEY,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBACxEG,QAAQ;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CpC,OAAA;gBACE4C,IAAI,EAAC,OAAO;gBACZf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,KAAK,KAAI,EAAG;gBACzBY,QAAQ;gBACRC,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDpC,OAAA;gBACE4C,IAAI,EAAC,QAAQ;gBACbf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACG,MAAO;gBAC1BiC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEG,MAAM,EAAEW,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC1EQ,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDpC,OAAA;gBACE4C,IAAI,EAAC,QAAQ;gBACbf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACI,MAAO;gBAC1BgC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEI,MAAM,EAAEU,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC1EQ,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpDpC,OAAA;cACE6B,SAAS,EAAC,aAAa;cACvBgB,KAAK,EAAEnC,WAAW,CAACK,aAAc;cACjC+B,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,aAAa,EAAES,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cAAAf,QAAA,gBAEjF9B,OAAA;gBAAQ6C,KAAK,EAAC,WAAW;gBAAAf,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CpC,OAAA;gBAAQ6C,KAAK,EAAC,gBAAgB;gBAAAf,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDpC,OAAA;gBAAQ6C,KAAK,EAAC,mBAAmB;gBAAAf,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5DpC,OAAA;gBAAQ6C,KAAK,EAAC,aAAa;gBAAAf,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDpC,OAAA;gBAAQ6C,KAAK,EAAC,kBAAkB;gBAAAf,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpC,OAAA;YAAQ4C,IAAI,EAAC,QAAQ;YAACf,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA5B,SAAS,KAAK,UAAU,iBACvBR,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAA8B,QAAA,EAAI;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBpC,OAAA;UAAM2C,QAAQ,EAAEf,oBAAqB;UAAAE,QAAA,gBACnC9B,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACE,eAAgB;cACpC4B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEE,eAAe,EAAEM,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrFG,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACG,WAAY;cAChC2B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEG,WAAW,EAAEK,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACjFG,QAAQ;cACRO,SAAS,EAAC;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1DpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACI,eAAgB;cACpC0B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEI,eAAe,EAAEI,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrFG,QAAQ;cACRO,SAAS,EAAC;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAQ4C,IAAI,EAAC,QAAQ;YAACf,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA5B,SAAS,KAAK,cAAc,iBAC3BR,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA,CAACF,oBAAoB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAExBpC,OAAA;UAAK6B,SAAS,EAAC,kBAAkB;UAACqB,KAAK,EAAE;YAAEM,SAAS,EAAE;UAAO,CAAE;UAAA1B,QAAA,gBAC7D9B,OAAA;YAAA8B,QAAA,EAAI;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BpC,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA;cAAK6B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9B,OAAA;gBAAA8B,QAAA,EAAI;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBpC,OAAA;gBAAA8B,QAAA,EAAG;cAAiD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxDpC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAA8B,QAAA,EAAI;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBpC,OAAA;kBAAA8B,QAAA,EAAI;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfpC,OAAA;kBAAA8B,QAAA,EAAI;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfpC,OAAA;kBAAA8B,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAA8B,QAAA,EAAI;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBpC,OAAA;gBAAA8B,QAAA,EAAG;cAAuC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9CpC,OAAA;gBAAM6B,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAA8B,QAAA,EAAI;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBpC,OAAA;gBAAA8B,QAAA,EAAG;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpCpC,OAAA;gBAAM6B,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENpC,OAAA;MAAOyD,GAAG;MAAA3B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClC,EAAA,CAvUID,OAAO;EAAA,QACqCJ,OAAO;AAAA;AAAA6D,EAAA,GADnDzD,OAAO;AAyUb,eAAeA,OAAO;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}