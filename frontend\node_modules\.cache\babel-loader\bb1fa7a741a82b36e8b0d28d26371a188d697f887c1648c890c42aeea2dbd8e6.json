{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\contexts\\\\HealthDataContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HealthDataContext = /*#__PURE__*/createContext();\nexport const useHealthData = () => {\n  _s();\n  const context = useContext(HealthDataContext);\n  if (!context) {\n    throw new Error('useHealthData must be used within a HealthDataProvider');\n  }\n  return context;\n};\n_s(useHealthData, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const HealthDataProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const [healthData, setHealthData] = useState([]);\n  const [goals, setGoals] = useState([]);\n  const [predictions, setPredictions] = useState([]);\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // Fetch dashboard data\n  const fetchDashboardData = async () => {\n    if (!isAuthenticated) return;\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/users/dashboard');\n      setDashboardData(response.data.data);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch health data\n  const fetchHealthData = async (params = {}) => {\n    if (!isAuthenticated) return;\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/health-data', {\n        params\n      });\n      setHealthData(response.data.data.healthData);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch health data:', error);\n      return {\n        healthData: [],\n        pagination: {}\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add health data\n  const addHealthData = async data => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/health-data', data);\n\n      // Update local state\n      const newData = response.data.data.healthData;\n      setHealthData(prev => [newData, ...prev.filter(item => new Date(item.date).toDateString() !== new Date(newData.date).toDateString())]);\n\n      // Refresh dashboard\n      fetchDashboardData();\n      return {\n        success: true,\n        data: newData\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to add health data';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Update health data\n  const updateHealthData = async (id, data) => {\n    try {\n      const response = await axios.put(`http://localhost:5000/api/health-data/${id}`, data);\n\n      // Update local state\n      const updatedData = response.data.data.healthData;\n      setHealthData(prev => prev.map(item => item._id === id ? updatedData : item));\n      return {\n        success: true,\n        data: updatedData\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to update health data';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Delete health data\n  const deleteHealthData = async id => {\n    try {\n      await axios.delete(`http://localhost:5000/api/health-data/${id}`);\n\n      // Update local state\n      setHealthData(prev => prev.filter(item => item._id !== id));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to delete health data';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Fetch goals\n  const fetchGoals = async (params = {}) => {\n    if (!isAuthenticated) return;\n    try {\n      const response = await axios.get('http://localhost:5000/api/goals', {\n        params\n      });\n      setGoals(response.data.data.goals);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch goals:', error);\n      return {\n        goals: [],\n        pagination: {}\n      };\n    }\n  };\n\n  // Add goal\n  const addGoal = async goalData => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/goals', goalData);\n      const newGoal = response.data.data.goal;\n      setGoals(prev => [newGoal, ...prev]);\n      return {\n        success: true,\n        data: newGoal\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to add goal';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Update goal progress\n  const updateGoalProgress = async (id, value, note = '') => {\n    try {\n      const response = await axios.put(`http://localhost:5000/api/goals/${id}/progress`, {\n        value,\n        note\n      });\n      const updatedGoal = response.data.data.goal;\n      setGoals(prev => prev.map(goal => goal._id === id ? updatedGoal : goal));\n      return {\n        success: true,\n        data: updatedGoal\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to update goal progress';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Generate prediction\n  const generatePrediction = async (predictionType = 'comprehensive') => {\n    try {\n      setLoading(true);\n      const response = await axios.post('http://localhost:5000/api/predictions/generate', {\n        predictionType\n      });\n      const newPrediction = response.data.data.prediction;\n      setPredictions(prev => [newPrediction, ...prev]);\n      return {\n        success: true,\n        data: newPrediction\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      const message = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Failed to generate prediction';\n      return {\n        success: false,\n        message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch predictions\n  const fetchPredictions = async (params = {}) => {\n    if (!isAuthenticated) return;\n    try {\n      const response = await axios.get('http://localhost:5000/api/predictions', {\n        params\n      });\n      setPredictions(response.data.data.predictions);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch predictions:', error);\n      return {\n        predictions: [],\n        pagination: {}\n      };\n    }\n  };\n\n  // Get activity summary\n  const getActivitySummary = async (period = '7d') => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/users/activity-summary', {\n        params: {\n          period\n        }\n      });\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      const message = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to get activity summary';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Chat with bot\n  const chatWithBot = async message => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/chatbot/chat', {\n        message\n      });\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      const message = ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || 'Chat failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Load initial data when user logs in\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      fetchDashboardData();\n      fetchHealthData({\n        limit: 10\n      });\n      fetchGoals({\n        status: 'active',\n        limit: 5\n      });\n      fetchPredictions({\n        limit: 5\n      });\n    }\n  }, [isAuthenticated, user]);\n  const value = {\n    // Data\n    healthData,\n    goals,\n    predictions,\n    dashboardData,\n    loading,\n    // Health Data methods\n    fetchHealthData,\n    addHealthData,\n    updateHealthData,\n    deleteHealthData,\n    // Goals methods\n    fetchGoals,\n    addGoal,\n    updateGoalProgress,\n    // Predictions methods\n    fetchPredictions,\n    generatePrediction,\n    // Dashboard methods\n    fetchDashboardData,\n    getActivitySummary,\n    // Chatbot methods\n    chatWithBot\n  };\n  return /*#__PURE__*/_jsxDEV(HealthDataContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s2(HealthDataProvider, \"SlcBk2GDLkZ1OWyasjaFwHRozM4=\", false, function () {\n  return [useAuth];\n});\n_c = HealthDataProvider;\nvar _c;\n$RefreshReg$(_c, \"HealthDataProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "useAuth", "jsxDEV", "_jsxDEV", "HealthDataContext", "useHealthData", "_s", "context", "Error", "HealthDataProvider", "children", "_s2", "user", "isAuthenticated", "healthData", "setHealthData", "goals", "setGoals", "predictions", "setPredictions", "dashboardData", "setDashboardData", "loading", "setLoading", "fetchDashboardData", "response", "get", "data", "error", "console", "fetchHealthData", "params", "pagination", "addHealthData", "post", "newData", "prev", "filter", "item", "Date", "date", "toDateString", "success", "_error$response", "_error$response$data", "message", "updateHealthData", "id", "put", "updatedData", "map", "_id", "_error$response2", "_error$response2$data", "deleteHealthData", "delete", "_error$response3", "_error$response3$data", "fetchGoals", "addGoal", "goalData", "newGoal", "goal", "_error$response4", "_error$response4$data", "updateGoalProgress", "value", "note", "updatedGoal", "_error$response5", "_error$response5$data", "generatePrediction", "predictionType", "newPrediction", "prediction", "_error$response6", "_error$response6$data", "fetchPredictions", "getActivitySummary", "period", "_error$response7", "_error$response7$data", "chatWithBot", "_error$response8", "_error$response8$data", "limit", "status", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/contexts/HealthDataContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useAuth } from './AuthContext';\n\nconst HealthDataContext = createContext();\n\nexport const useHealthData = () => {\n  const context = useContext(HealthDataContext);\n  if (!context) {\n    throw new Error('useHealthData must be used within a HealthDataProvider');\n  }\n  return context;\n};\n\nexport const HealthDataProvider = ({ children }) => {\n  const { user, isAuthenticated } = useAuth();\n  const [healthData, setHealthData] = useState([]);\n  const [goals, setGoals] = useState([]);\n  const [predictions, setPredictions] = useState([]);\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // Fetch dashboard data\n  const fetchDashboardData = async () => {\n    if (!isAuthenticated) return;\n    \n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/users/dashboard');\n      setDashboardData(response.data.data);\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch health data\n  const fetchHealthData = async (params = {}) => {\n    if (!isAuthenticated) return;\n    \n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/health-data', { params });\n      setHealthData(response.data.data.healthData);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch health data:', error);\n      return { healthData: [], pagination: {} };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add health data\n  const addHealthData = async (data) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/health-data', data);\n      \n      // Update local state\n      const newData = response.data.data.healthData;\n      setHealthData(prev => [newData, ...prev.filter(item => \n        new Date(item.date).toDateString() !== new Date(newData.date).toDateString()\n      )]);\n      \n      // Refresh dashboard\n      fetchDashboardData();\n      \n      return { success: true, data: newData };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to add health data';\n      return { success: false, message };\n    }\n  };\n\n  // Update health data\n  const updateHealthData = async (id, data) => {\n    try {\n      const response = await axios.put(`http://localhost:5000/api/health-data/${id}`, data);\n      \n      // Update local state\n      const updatedData = response.data.data.healthData;\n      setHealthData(prev => prev.map(item => \n        item._id === id ? updatedData : item\n      ));\n      \n      return { success: true, data: updatedData };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to update health data';\n      return { success: false, message };\n    }\n  };\n\n  // Delete health data\n  const deleteHealthData = async (id) => {\n    try {\n      await axios.delete(`http://localhost:5000/api/health-data/${id}`);\n      \n      // Update local state\n      setHealthData(prev => prev.filter(item => item._id !== id));\n      \n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to delete health data';\n      return { success: false, message };\n    }\n  };\n\n  // Fetch goals\n  const fetchGoals = async (params = {}) => {\n    if (!isAuthenticated) return;\n    \n    try {\n      const response = await axios.get('http://localhost:5000/api/goals', { params });\n      setGoals(response.data.data.goals);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch goals:', error);\n      return { goals: [], pagination: {} };\n    }\n  };\n\n  // Add goal\n  const addGoal = async (goalData) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/goals', goalData);\n      \n      const newGoal = response.data.data.goal;\n      setGoals(prev => [newGoal, ...prev]);\n      \n      return { success: true, data: newGoal };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to add goal';\n      return { success: false, message };\n    }\n  };\n\n  // Update goal progress\n  const updateGoalProgress = async (id, value, note = '') => {\n    try {\n      const response = await axios.put(`http://localhost:5000/api/goals/${id}/progress`, {\n        value,\n        note\n      });\n      \n      const updatedGoal = response.data.data.goal;\n      setGoals(prev => prev.map(goal => \n        goal._id === id ? updatedGoal : goal\n      ));\n      \n      return { success: true, data: updatedGoal };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to update goal progress';\n      return { success: false, message };\n    }\n  };\n\n  // Generate prediction\n  const generatePrediction = async (predictionType = 'comprehensive') => {\n    try {\n      setLoading(true);\n      const response = await axios.post('http://localhost:5000/api/predictions/generate', {\n        predictionType\n      });\n      \n      const newPrediction = response.data.data.prediction;\n      setPredictions(prev => [newPrediction, ...prev]);\n      \n      return { success: true, data: newPrediction };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to generate prediction';\n      return { success: false, message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch predictions\n  const fetchPredictions = async (params = {}) => {\n    if (!isAuthenticated) return;\n    \n    try {\n      const response = await axios.get('http://localhost:5000/api/predictions', { params });\n      setPredictions(response.data.data.predictions);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to fetch predictions:', error);\n      return { predictions: [], pagination: {} };\n    }\n  };\n\n  // Get activity summary\n  const getActivitySummary = async (period = '7d') => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/users/activity-summary', {\n        params: { period }\n      });\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to get activity summary';\n      return { success: false, message };\n    }\n  };\n\n  // Chat with bot\n  const chatWithBot = async (message) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/chatbot/chat', {\n        message\n      });\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Chat failed';\n      return { success: false, message };\n    }\n  };\n\n  // Load initial data when user logs in\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      fetchDashboardData();\n      fetchHealthData({ limit: 10 });\n      fetchGoals({ status: 'active', limit: 5 });\n      fetchPredictions({ limit: 5 });\n    }\n  }, [isAuthenticated, user]);\n\n  const value = {\n    // Data\n    healthData,\n    goals,\n    predictions,\n    dashboardData,\n    loading,\n    \n    // Health Data methods\n    fetchHealthData,\n    addHealthData,\n    updateHealthData,\n    deleteHealthData,\n    \n    // Goals methods\n    fetchGoals,\n    addGoal,\n    updateGoalProgress,\n    \n    // Predictions methods\n    fetchPredictions,\n    generatePrediction,\n    \n    // Dashboard methods\n    fetchDashboardData,\n    getActivitySummary,\n    \n    // Chatbot methods\n    chatWithBot\n  };\n\n  return (\n    <HealthDataContext.Provider value={value}>\n      {children}\n    </HealthDataContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,iBAAiB,gBAAGR,aAAa,CAAC,CAAC;AAEzC,OAAO,MAAMS,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,OAAO,GAAGV,UAAU,CAACO,iBAAiB,CAAC;EAC7C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,aAAa;AAQ1B,OAAO,MAAMI,kBAAkB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAClD,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM0B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACX,eAAe,EAAE;IAEtB,IAAI;MACFU,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,2CAA2C,CAAC;MAC7EL,gBAAgB,CAACI,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMO,eAAe,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7C,IAAI,CAAClB,eAAe,EAAE;IAEtB,IAAI;MACFU,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,uCAAuC,EAAE;QAAEK;MAAO,CAAC,CAAC;MACrFhB,aAAa,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACb,UAAU,CAAC;MAC5C,OAAOW,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEd,UAAU,EAAE,EAAE;QAAEkB,UAAU,EAAE,CAAC;MAAE,CAAC;IAC3C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMU,aAAa,GAAG,MAAON,IAAI,IAAK;IACpC,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAMzB,KAAK,CAACkC,IAAI,CAAC,uCAAuC,EAAEP,IAAI,CAAC;;MAEhF;MACA,MAAMQ,OAAO,GAAGV,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACb,UAAU;MAC7CC,aAAa,CAACqB,IAAI,IAAI,CAACD,OAAO,EAAE,GAAGC,IAAI,CAACC,MAAM,CAACC,IAAI,IACjD,IAAIC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,CAACC,YAAY,CAAC,CAAC,KAAK,IAAIF,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAACC,YAAY,CAAC,CAC7E,CAAC,CAAC,CAAC;;MAEH;MACAjB,kBAAkB,CAAC,CAAC;MAEpB,OAAO;QAAEkB,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEQ;MAAQ,CAAC;IACzC,CAAC,CAAC,OAAOP,KAAK,EAAE;MAAA,IAAAe,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAf,KAAK,CAACH,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhB,IAAI,cAAAiB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,2BAA2B;MAC5E,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,EAAE,EAAEpB,IAAI,KAAK;IAC3C,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAMzB,KAAK,CAACgD,GAAG,CAAC,yCAAyCD,EAAE,EAAE,EAAEpB,IAAI,CAAC;;MAErF;MACA,MAAMsB,WAAW,GAAGxB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACb,UAAU;MACjDC,aAAa,CAACqB,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACZ,IAAI,IACjCA,IAAI,CAACa,GAAG,KAAKJ,EAAE,GAAGE,WAAW,GAAGX,IAClC,CAAC,CAAC;MAEF,OAAO;QAAEI,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEsB;MAAY,CAAC;IAC7C,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACd,MAAMR,OAAO,GAAG,EAAAO,gBAAA,GAAAxB,KAAK,CAACH,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,8BAA8B;MAC/E,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMS,gBAAgB,GAAG,MAAOP,EAAE,IAAK;IACrC,IAAI;MACF,MAAM/C,KAAK,CAACuD,MAAM,CAAC,yCAAyCR,EAAE,EAAE,CAAC;;MAEjE;MACAhC,aAAa,CAACqB,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACa,GAAG,KAAKJ,EAAE,CAAC,CAAC;MAE3D,OAAO;QAAEL,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACd,MAAMZ,OAAO,GAAG,EAAAW,gBAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAI,8BAA8B;MAC/E,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMa,UAAU,GAAG,MAAAA,CAAO3B,MAAM,GAAG,CAAC,CAAC,KAAK;IACxC,IAAI,CAAClB,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,iCAAiC,EAAE;QAAEK;MAAO,CAAC,CAAC;MAC/Ed,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACX,KAAK,CAAC;MAClC,OAAOS,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QAAEZ,KAAK,EAAE,EAAE;QAAEgB,UAAU,EAAE,CAAC;MAAE,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAM2B,OAAO,GAAG,MAAOC,QAAQ,IAAK;IAClC,IAAI;MACF,MAAMnC,QAAQ,GAAG,MAAMzB,KAAK,CAACkC,IAAI,CAAC,iCAAiC,EAAE0B,QAAQ,CAAC;MAE9E,MAAMC,OAAO,GAAGpC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmC,IAAI;MACvC7C,QAAQ,CAACmB,IAAI,IAAI,CAACyB,OAAO,EAAE,GAAGzB,IAAI,CAAC,CAAC;MAEpC,OAAO;QAAEM,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEkC;MAAQ,CAAC;IACzC,CAAC,CAAC,OAAOjC,KAAK,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACd,MAAMnB,OAAO,GAAG,EAAAkB,gBAAA,GAAAnC,KAAK,CAACH,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,oBAAoB;MACrE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMoB,kBAAkB,GAAG,MAAAA,CAAOlB,EAAE,EAAEmB,KAAK,EAAEC,IAAI,GAAG,EAAE,KAAK;IACzD,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMzB,KAAK,CAACgD,GAAG,CAAC,mCAAmCD,EAAE,WAAW,EAAE;QACjFmB,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAMC,WAAW,GAAG3C,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmC,IAAI;MAC3C7C,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACc,GAAG,CAACY,IAAI,IAC5BA,IAAI,CAACX,GAAG,KAAKJ,EAAE,GAAGqB,WAAW,GAAGN,IAClC,CAAC,CAAC;MAEF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEyC;MAAY,CAAC;IAC7C,CAAC,CAAC,OAAOxC,KAAK,EAAE;MAAA,IAAAyC,gBAAA,EAAAC,qBAAA;MACd,MAAMzB,OAAO,GAAG,EAAAwB,gBAAA,GAAAzC,KAAK,CAACH,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,gCAAgC;MACjF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAG,MAAAA,CAAOC,cAAc,GAAG,eAAe,KAAK;IACrE,IAAI;MACFjD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMzB,KAAK,CAACkC,IAAI,CAAC,gDAAgD,EAAE;QAClFsC;MACF,CAAC,CAAC;MAEF,MAAMC,aAAa,GAAGhD,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC+C,UAAU;MACnDvD,cAAc,CAACiB,IAAI,IAAI,CAACqC,aAAa,EAAE,GAAGrC,IAAI,CAAC,CAAC;MAEhD,OAAO;QAAEM,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAE8C;MAAc,CAAC;IAC/C,CAAC,CAAC,OAAO7C,KAAK,EAAE;MAAA,IAAA+C,gBAAA,EAAAC,qBAAA;MACd,MAAM/B,OAAO,GAAG,EAAA8B,gBAAA,GAAA/C,KAAK,CAACH,QAAQ,cAAAkD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhD,IAAI,cAAAiD,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,+BAA+B;MAChF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAG,MAAAA,CAAO9C,MAAM,GAAG,CAAC,CAAC,KAAK;IAC9C,IAAI,CAAClB,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,uCAAuC,EAAE;QAAEK;MAAO,CAAC,CAAC;MACrFZ,cAAc,CAACM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACT,WAAW,CAAC;MAC9C,OAAOO,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEV,WAAW,EAAE,EAAE;QAAEc,UAAU,EAAE,CAAC;MAAE,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAM8C,kBAAkB,GAAG,MAAAA,CAAOC,MAAM,GAAG,IAAI,KAAK;IAClD,IAAI;MACF,MAAMtD,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,kDAAkD,EAAE;QACnFK,MAAM,EAAE;UAAEgD;QAAO;MACnB,CAAC,CAAC;MACF,OAAO;QAAErC,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAoD,gBAAA,EAAAC,qBAAA;MACd,MAAMpC,OAAO,GAAG,EAAAmC,gBAAA,GAAApD,KAAK,CAACH,QAAQ,cAAAuD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrD,IAAI,cAAAsD,qBAAA,uBAApBA,qBAAA,CAAsBpC,OAAO,KAAI,gCAAgC;MACjF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAG,MAAOrC,OAAO,IAAK;IACrC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMzB,KAAK,CAACkC,IAAI,CAAC,wCAAwC,EAAE;QAC1EW;MACF,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,IAAI;QAAEf,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAuD,gBAAA,EAAAC,qBAAA;MACd,MAAMvC,OAAO,GAAG,EAAAsC,gBAAA,GAAAvD,KAAK,CAACH,QAAQ,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxD,IAAI,cAAAyD,qBAAA,uBAApBA,qBAAA,CAAsBvC,OAAO,KAAI,aAAa;MAC9D,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIc,eAAe,IAAID,IAAI,EAAE;MAC3BY,kBAAkB,CAAC,CAAC;MACpBM,eAAe,CAAC;QAAEuD,KAAK,EAAE;MAAG,CAAC,CAAC;MAC9B3B,UAAU,CAAC;QAAE4B,MAAM,EAAE,QAAQ;QAAED,KAAK,EAAE;MAAE,CAAC,CAAC;MAC1CR,gBAAgB,CAAC;QAAEQ,KAAK,EAAE;MAAE,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACxE,eAAe,EAAED,IAAI,CAAC,CAAC;EAE3B,MAAMsD,KAAK,GAAG;IACZ;IACApD,UAAU;IACVE,KAAK;IACLE,WAAW;IACXE,aAAa;IACbE,OAAO;IAEP;IACAQ,eAAe;IACfG,aAAa;IACba,gBAAgB;IAChBQ,gBAAgB;IAEhB;IACAI,UAAU;IACVC,OAAO;IACPM,kBAAkB;IAElB;IACAY,gBAAgB;IAChBN,kBAAkB;IAElB;IACA/C,kBAAkB;IAClBsD,kBAAkB;IAElB;IACAI;EACF,CAAC;EAED,oBACE/E,OAAA,CAACC,iBAAiB,CAACmF,QAAQ;IAACrB,KAAK,EAAEA,KAAM;IAAAxD,QAAA,EACtCA;EAAQ;IAAA8E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEjC,CAAC;AAAChF,GAAA,CAzPWF,kBAAkB;EAAA,QACKR,OAAO;AAAA;AAAA2F,EAAA,GAD9BnF,kBAAkB;AAAA,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}