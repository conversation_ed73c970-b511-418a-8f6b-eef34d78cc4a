const express = require('express');
const { protect } = require('../middleware/auth');
const User = require('../models/User');
const HealthData = require('../models/HealthData');
const Goal = require('../models/Goal');
const Prediction = require('../models/Prediction');
const router = express.Router();

// @desc    Get user dashboard data
// @route   GET /api/users/dashboard
// @access  Private
router.get('/dashboard', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Get user info
    const user = await User.findById(userId);

    // Get today's health data
    const todayData = await HealthData.findOne({
      userId,
      date: {
        $gte: new Date(today.setHours(0, 0, 0, 0)),
        $lt: new Date(today.setHours(23, 59, 59, 999))
      }
    });

    // Get weekly stats
    const weeklyStats = await HealthData.aggregate([
      {
        $match: {
          userId: userId,
          date: { $gte: weekAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalSteps: { $sum: '$steps' },
          avgSteps: { $avg: '$steps' },
          totalDistance: { $sum: '$distance' },
          totalCalories: { $sum: '$caloriesBurned' },
          avgHeartRate: { $avg: '$heartRate.average' },
          avgSleep: { $avg: '$sleep.totalSleep' },
          avgMood: { $avg: '$mood' },
          daysWithData: { $sum: 1 }
        }
      }
    ]);

    // Get active goals
    const activeGoals = await Goal.find({
      userId,
      status: 'active',
      endDate: { $gte: today }
    }).limit(5);

    // Get latest prediction
    const latestPrediction = await Prediction.findOne({
      userId,
      status: 'completed'
    }).sort({ createdAt: -1 });

    // Calculate achievement badges
    const badges = await calculateUserBadges(userId, weeklyStats[0]);

    const dashboardData = {
      user: {
        name: user.name,
        age: user.age,
        bmi: user.bmi,
        activityLevel: user.activityLevel
      },
      today: {
        steps: todayData?.steps || 0,
        distance: todayData?.distance || 0,
        calories: todayData?.caloriesBurned || 0,
        activeMinutes: todayData?.activeMinutes || 0,
        heartRate: todayData?.heartRate?.average || null,
        sleep: todayData?.sleep?.totalSleep || null,
        mood: todayData?.mood || null,
        activityScore: todayData?.activityScore || 0
      },
      weekly: weeklyStats[0] || {},
      goals: {
        active: activeGoals,
        summary: await Goal.getGoalsSummary(userId)
      },
      prediction: latestPrediction ? {
        overallRisk: latestPrediction.overallRiskLevel,
        recommendations: latestPrediction.highPriorityRecommendations,
        createdAt: latestPrediction.createdAt
      } : null,
      badges: badges,
      insights: generateHealthInsights(todayData, weeklyStats[0], user)
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get user activity summary
// @route   GET /api/users/activity-summary
// @access  Private
router.get('/activity-summary', protect, async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const userId = req.user.id;

    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    const activityData = await HealthData.find({
      userId,
      date: { $gte: startDate }
    }).sort({ date: 1 });

    const summary = {
      period,
      totalDays: activityData.length,
      averages: {
        steps: calculateAverage(activityData, 'steps'),
        distance: calculateAverage(activityData, 'distance'),
        calories: calculateAverage(activityData, 'caloriesBurned'),
        activeMinutes: calculateAverage(activityData, 'activeMinutes'),
        heartRate: calculateAverage(activityData, 'heartRate.average'),
        sleep: calculateAverage(activityData, 'sleep.totalSleep'),
        mood: calculateAverage(activityData, 'mood')
      },
      trends: calculateTrends(activityData),
      dailyData: activityData.map(data => ({
        date: data.date,
        steps: data.steps,
        distance: data.distance,
        calories: data.caloriesBurned,
        activeMinutes: data.activeMinutes,
        heartRate: data.heartRate?.average,
        sleep: data.sleep?.totalSleep,
        mood: data.mood,
        activityScore: data.activityScore
      }))
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('Activity summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update user preferences
// @route   PUT /api/users/preferences
// @access  Private
router.put('/preferences', protect, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update preferences
    user.preferences = { ...user.preferences, ...req.body };
    await user.save();

    res.json({
      success: true,
      message: 'Preferences updated successfully',
      data: { preferences: user.preferences }
    });

  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Helper functions
function calculateAverage(data, field) {
  const values = data.map(item => {
    const keys = field.split('.');
    let value = item;
    for (const key of keys) {
      value = value?.[key];
    }
    return value;
  }).filter(val => val != null && !isNaN(val));
  
  return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
}

function calculateTrends(data) {
  if (data.length < 2) return {};

  const firstHalf = data.slice(0, Math.floor(data.length / 2));
  const secondHalf = data.slice(Math.floor(data.length / 2));

  const trends = {};
  const metrics = ['steps', 'distance', 'caloriesBurned', 'activeMinutes'];

  metrics.forEach(metric => {
    const firstAvg = calculateAverage(firstHalf, metric);
    const secondAvg = calculateAverage(secondHalf, metric);
    
    if (firstAvg > 0) {
      const change = ((secondAvg - firstAvg) / firstAvg) * 100;
      trends[metric] = {
        direction: change > 5 ? 'improving' : change < -5 ? 'declining' : 'stable',
        changePercentage: Math.round(change * 10) / 10
      };
    }
  });

  return trends;
}

async function calculateUserBadges(userId, weeklyStats) {
  const badges = [];

  if (weeklyStats) {
    // Step badges
    if (weeklyStats.totalSteps >= 70000) { // 10k steps * 7 days
      badges.push({
        name: 'Step Master',
        description: 'Achieved 70,000+ steps this week',
        icon: '👟',
        earnedAt: new Date()
      });
    }

    // Activity badges
    if (weeklyStats.avgSteps >= 10000) {
      badges.push({
        name: 'Daily Walker',
        description: 'Averaged 10,000+ steps daily',
        icon: '🚶',
        earnedAt: new Date()
      });
    }

    // Sleep badges
    if (weeklyStats.avgSleep >= 420) { // 7 hours
      badges.push({
        name: 'Sleep Champion',
        description: 'Maintained healthy sleep schedule',
        icon: '😴',
        earnedAt: new Date()
      });
    }
  }

  // Goal completion badges
  const completedGoals = await Goal.countDocuments({
    userId,
    status: 'completed',
    updatedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
  });

  if (completedGoals > 0) {
    badges.push({
      name: 'Goal Achiever',
      description: `Completed ${completedGoals} goal${completedGoals > 1 ? 's' : ''} this week`,
      icon: '🎯',
      earnedAt: new Date()
    });
  }

  return badges;
}

function generateHealthInsights(todayData, weeklyStats, user) {
  const insights = [];

  if (todayData) {
    // Step insights
    if (todayData.steps < 5000) {
      insights.push({
        type: 'warning',
        title: 'Low Activity Today',
        message: 'You\'ve taken fewer steps than recommended. Try to get moving!',
        action: 'Take a 10-minute walk'
      });
    } else if (todayData.steps >= 10000) {
      insights.push({
        type: 'success',
        title: 'Great Activity!',
        message: 'You\'ve reached your daily step goal. Keep it up!',
        action: null
      });
    }

    // Heart rate insights
    if (todayData.heartRate && todayData.heartRate > 100) {
      insights.push({
        type: 'info',
        title: 'Elevated Heart Rate',
        message: 'Your heart rate seems elevated today. Make sure to stay hydrated and rest if needed.',
        action: 'Monitor throughout the day'
      });
    }

    // Sleep insights
    if (todayData.sleep && todayData.sleep < 360) { // Less than 6 hours
      insights.push({
        type: 'warning',
        title: 'Insufficient Sleep',
        message: 'You got less sleep than recommended. Try to prioritize rest tonight.',
        action: 'Aim for 7-9 hours tonight'
      });
    }
  }

  if (weeklyStats) {
    // Weekly trends
    if (weeklyStats.avgSteps < 7000) {
      insights.push({
        type: 'suggestion',
        title: 'Increase Weekly Activity',
        message: 'Your weekly average is below recommended levels. Small increases can make a big difference.',
        action: 'Add 1000 steps daily'
      });
    }
  }

  return insights;
}

module.exports = router;
