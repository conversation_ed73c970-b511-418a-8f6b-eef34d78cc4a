import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const AuthDebug = () => {
  const { user, token, loading, isAuthenticated } = useAuth();

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#4CAF50' }}>🔍 Auth Debug</h4>
      <div><strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}</div>
      <div><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</div>
      <div><strong>User:</strong> {user ? `👤 ${user.name}` : '❌ None'}</div>
      <div><strong>Token:</strong> {token ? `🔑 ${token.substring(0, 10)}...` : '❌ None'}</div>
      <div><strong>LocalStorage:</strong> {localStorage.getItem('token') ? '💾 Present' : '❌ Missing'}</div>
    </div>
  );
};

export default AuthDebug;
