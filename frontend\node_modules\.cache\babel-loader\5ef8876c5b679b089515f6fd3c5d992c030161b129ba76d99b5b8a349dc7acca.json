{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Goals.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Goals = () => {\n  _s();\n  const {\n    goals,\n    fetchGoals,\n    addGoal,\n    updateGoalProgress\n  } = useHealthData();\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: 'steps',\n    type: 'daily',\n    target: {\n      value: '',\n      unit: 'steps'\n    },\n    startDate: new Date().toISOString().split('T')[0],\n    endDate: '',\n    priority: 'medium'\n  });\n  useEffect(() => {\n    fetchGoals();\n  }, []);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const result = await addGoal({\n      ...formData,\n      target: {\n        value: parseInt(formData.target.value),\n        unit: formData.target.unit\n      }\n    });\n    if (result.success) {\n      setShowAddForm(false);\n      setFormData({\n        title: '',\n        description: '',\n        category: 'steps',\n        type: 'daily',\n        target: {\n          value: '',\n          unit: 'steps'\n        },\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        priority: 'medium'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"goals-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Fitness Goals\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddForm(true),\n        children: \"Create New Goal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddForm(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Create New Goal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Goal Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input\",\n              value: formData.title,\n              onChange: e => setFormData({\n                ...formData,\n                title: e.target.value\n              }),\n              required: true,\n              placeholder: \"e.g., Walk 10,000 steps daily\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.category,\n                onChange: e => setFormData({\n                  ...formData,\n                  category: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"steps\",\n                  children: \"Steps\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"distance\",\n                  children: \"Distance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"calories\",\n                  children: \"Calories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"exercise\",\n                  children: \"Exercise\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"sleep\",\n                  children: \"Sleep\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"weight\",\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.type,\n                onChange: e => setFormData({\n                  ...formData,\n                  type: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"daily\",\n                  children: \"Daily\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"weekly\",\n                  children: \"Weekly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"monthly\",\n                  children: \"Monthly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Target Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-input\",\n                value: formData.target.value,\n                onChange: e => setFormData({\n                  ...formData,\n                  target: {\n                    ...formData.target,\n                    value: e.target.value\n                  }\n                }),\n                required: true,\n                placeholder: \"e.g., 10000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Unit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                value: formData.target.unit,\n                onChange: e => setFormData({\n                  ...formData,\n                  target: {\n                    ...formData.target,\n                    unit: e.target.value\n                  }\n                }),\n                placeholder: \"e.g., steps, km, minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: () => setShowAddForm(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              children: \"Create Goal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"goals-grid\",\n      children: goals.map(goal => {\n        var _goal$current, _goal$target, _goal$target2, _goal$current2, _goal$target3, _goal$current3, _goal$target4;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"goal-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"goal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: goal.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `priority-badge priority-${goal.priority}`,\n              children: goal.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"goal-description\",\n            children: goal.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"goal-progress\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [((_goal$current = goal.current) === null || _goal$current === void 0 ? void 0 : _goal$current.value) || 0, \" / \", (_goal$target = goal.target) === null || _goal$target === void 0 ? void 0 : _goal$target.value, \" \", (_goal$target2 = goal.target) === null || _goal$target2 === void 0 ? void 0 : _goal$target2.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [Math.round(((_goal$current2 = goal.current) === null || _goal$current2 === void 0 ? void 0 : _goal$current2.value) / ((_goal$target3 = goal.target) === null || _goal$target3 === void 0 ? void 0 : _goal$target3.value) * 100 || 0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-fill\",\n                style: {\n                  width: `${Math.min(((_goal$current3 = goal.current) === null || _goal$current3 === void 0 ? void 0 : _goal$current3.value) / ((_goal$target4 = goal.target) === null || _goal$target4 === void 0 ? void 0 : _goal$target4.value) * 100 || 0, 100)}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"goal-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83D\\uDCC5 \", goal.type]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83C\\uDFF7\\uFE0F \", goal.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u23F0 \", goal.daysRemaining || 0, \" days left\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, goal._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .goals-page { padding: 0; }\n        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }\n        .page-header h1 { margin: 0; color: #2c3e50; }\n        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center; z-index: 1000; }\n        .modal-content { background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; }\n        .form-actions { display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px; }\n        .goals-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n        .goal-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .goal-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }\n        .goal-header h3 { margin: 0; color: #2c3e50; }\n        .priority-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }\n        .priority-high { background: #e74c3c; color: white; }\n        .priority-medium { background: #f39c12; color: white; }\n        .priority-low { background: #27ae60; color: white; }\n        .goal-description { color: #7f8c8d; margin-bottom: 20px; }\n        .progress-info { display: flex; justify-content: space-between; margin-bottom: 8px; font-weight: 600; }\n        .progress-bar { width: 100%; height: 8px; background: rgba(0, 0, 0, 0.1); border-radius: 4px; overflow: hidden; margin-bottom: 15px; }\n        .progress-fill { height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px; transition: width 0.6s ease; }\n        .goal-meta { display: flex; gap: 15px; font-size: 0.85rem; color: #7f8c8d; }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(Goals, \"lqnmLeZfejDHBZ723ulzUadITlw=\", false, function () {\n  return [useHealthData];\n});\n_c = Goals;\nexport default Goals;\nvar _c;\n$RefreshReg$(_c, \"Goals\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHealthData", "jsxDEV", "_jsxDEV", "Goals", "_s", "goals", "fetchGoals", "addGoal", "updateGoalProgress", "showAddForm", "setShowAddForm", "formData", "setFormData", "title", "description", "category", "type", "target", "value", "unit", "startDate", "Date", "toISOString", "split", "endDate", "priority", "handleSubmit", "e", "preventDefault", "result", "parseInt", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "stopPropagation", "onSubmit", "onChange", "required", "placeholder", "map", "goal", "_goal$current", "_goal$target", "_goal$target2", "_goal$current2", "_goal$target3", "_goal$current3", "_goal$target4", "current", "Math", "round", "style", "width", "min", "daysRemaining", "_id", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Goals.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst Goals = () => {\n  const { goals, fetchGoals, addGoal, updateGoalProgress } = useHealthData();\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: 'steps',\n    type: 'daily',\n    target: { value: '', unit: 'steps' },\n    startDate: new Date().toISOString().split('T')[0],\n    endDate: '',\n    priority: 'medium'\n  });\n\n  useEffect(() => {\n    fetchGoals();\n  }, []);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const result = await addGoal({\n      ...formData,\n      target: {\n        value: parseInt(formData.target.value),\n        unit: formData.target.unit\n      }\n    });\n    if (result.success) {\n      setShowAddForm(false);\n      setFormData({\n        title: '', description: '', category: 'steps', type: 'daily',\n        target: { value: '', unit: 'steps' },\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '', priority: 'medium'\n      });\n    }\n  };\n\n  return (\n    <div className=\"goals-page\">\n      <div className=\"page-header\">\n        <h1>Fitness Goals</h1>\n        <button className=\"btn btn-primary\" onClick={() => setShowAddForm(true)}>\n          Create New Goal\n        </button>\n      </div>\n\n      {showAddForm && (\n        <div className=\"modal-overlay\" onClick={() => setShowAddForm(false)}>\n          <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n            <h2>Create New Goal</h2>\n            <form onSubmit={handleSubmit}>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Goal Title</label>\n                <input\n                  type=\"text\"\n                  className=\"form-input\"\n                  value={formData.title}\n                  onChange={(e) => setFormData({...formData, title: e.target.value})}\n                  required\n                  placeholder=\"e.g., Walk 10,000 steps daily\"\n                />\n              </div>\n              <div className=\"grid grid-2\">\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Category</label>\n                  <select\n                    className=\"form-select\"\n                    value={formData.category}\n                    onChange={(e) => setFormData({...formData, category: e.target.value})}\n                  >\n                    <option value=\"steps\">Steps</option>\n                    <option value=\"distance\">Distance</option>\n                    <option value=\"calories\">Calories</option>\n                    <option value=\"exercise\">Exercise</option>\n                    <option value=\"sleep\">Sleep</option>\n                    <option value=\"weight\">Weight</option>\n                  </select>\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Type</label>\n                  <select\n                    className=\"form-select\"\n                    value={formData.type}\n                    onChange={(e) => setFormData({...formData, type: e.target.value})}\n                  >\n                    <option value=\"daily\">Daily</option>\n                    <option value=\"weekly\">Weekly</option>\n                    <option value=\"monthly\">Monthly</option>\n                  </select>\n                </div>\n              </div>\n              <div className=\"grid grid-2\">\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Target Value</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-input\"\n                    value={formData.target.value}\n                    onChange={(e) => setFormData({\n                      ...formData, \n                      target: {...formData.target, value: e.target.value}\n                    })}\n                    required\n                    placeholder=\"e.g., 10000\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Unit</label>\n                  <input\n                    type=\"text\"\n                    className=\"form-input\"\n                    value={formData.target.unit}\n                    onChange={(e) => setFormData({\n                      ...formData, \n                      target: {...formData.target, unit: e.target.value}\n                    })}\n                    placeholder=\"e.g., steps, km, minutes\"\n                  />\n                </div>\n              </div>\n              <div className=\"form-actions\">\n                <button type=\"button\" className=\"btn btn-secondary\" onClick={() => setShowAddForm(false)}>\n                  Cancel\n                </button>\n                <button type=\"submit\" className=\"btn btn-primary\">\n                  Create Goal\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      <div className=\"goals-grid\">\n        {goals.map((goal) => (\n          <div key={goal._id} className=\"goal-card\">\n            <div className=\"goal-header\">\n              <h3>{goal.title}</h3>\n              <span className={`priority-badge priority-${goal.priority}`}>\n                {goal.priority}\n              </span>\n            </div>\n            <p className=\"goal-description\">{goal.description}</p>\n            <div className=\"goal-progress\">\n              <div className=\"progress-info\">\n                <span>{goal.current?.value || 0} / {goal.target?.value} {goal.target?.unit}</span>\n                <span>{Math.round((goal.current?.value / goal.target?.value) * 100 || 0)}%</span>\n              </div>\n              <div className=\"progress-bar\">\n                <div \n                  className=\"progress-fill\"\n                  style={{ width: `${Math.min((goal.current?.value / goal.target?.value) * 100 || 0, 100)}%` }}\n                />\n              </div>\n            </div>\n            <div className=\"goal-meta\">\n              <span>📅 {goal.type}</span>\n              <span>🏷️ {goal.category}</span>\n              <span>⏰ {goal.daysRemaining || 0} days left</span>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <style jsx>{`\n        .goals-page { padding: 0; }\n        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }\n        .page-header h1 { margin: 0; color: #2c3e50; }\n        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center; z-index: 1000; }\n        .modal-content { background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; }\n        .form-actions { display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px; }\n        .goals-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n        .goal-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .goal-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }\n        .goal-header h3 { margin: 0; color: #2c3e50; }\n        .priority-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }\n        .priority-high { background: #e74c3c; color: white; }\n        .priority-medium { background: #f39c12; color: white; }\n        .priority-low { background: #27ae60; color: white; }\n        .goal-description { color: #7f8c8d; margin-bottom: 20px; }\n        .progress-info { display: flex; justify-content: space-between; margin-bottom: 8px; font-weight: 600; }\n        .progress-bar { width: 100%; height: 8px; background: rgba(0, 0, 0, 0.1); border-radius: 4px; overflow: hidden; margin-bottom: 15px; }\n        .progress-fill { height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px; transition: width 0.6s ease; }\n        .goal-meta { display: flex; gap: 15px; font-size: 0.85rem; color: #7f8c8d; }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Goals;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,KAAK;IAAEC,UAAU;IAAEC,OAAO;IAAEC;EAAmB,CAAC,GAAGR,aAAa,CAAC,CAAC;EAC1E,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAQ,CAAC;IACpCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjDC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF1B,SAAS,CAAC,MAAM;IACdO,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,MAAMtB,OAAO,CAAC;MAC3B,GAAGI,QAAQ;MACXM,MAAM,EAAE;QACNC,KAAK,EAAEY,QAAQ,CAACnB,QAAQ,CAACM,MAAM,CAACC,KAAK,CAAC;QACtCC,IAAI,EAAER,QAAQ,CAACM,MAAM,CAACE;MACxB;IACF,CAAC,CAAC;IACF,IAAIU,MAAM,CAACE,OAAO,EAAE;MAClBrB,cAAc,CAAC,KAAK,CAAC;MACrBE,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,IAAI,EAAE,OAAO;QAC5DC,MAAM,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAQ,CAAC;QACpCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE;MACzB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEvB,OAAA;IAAK8B,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzB/B,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/B,OAAA;QAAA+B,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBnC,OAAA;QAAQ8B,SAAS,EAAC,iBAAiB;QAACM,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,IAAI,CAAE;QAAAuB,QAAA,EAAC;MAEzE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL5B,WAAW,iBACVP,OAAA;MAAK8B,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,KAAK,CAAE;MAAAuB,QAAA,eAClE/B,OAAA;QAAK8B,SAAS,EAAC,eAAe;QAACM,OAAO,EAAEX,CAAC,IAAIA,CAAC,CAACY,eAAe,CAAC,CAAE;QAAAN,QAAA,gBAC/D/B,OAAA;UAAA+B,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBnC,OAAA;UAAMsC,QAAQ,EAAEd,YAAa;UAAAO,QAAA,gBAC3B/B,OAAA;YAAK8B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/B,OAAA;cAAO8B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDnC,OAAA;cACEc,IAAI,EAAC,MAAM;cACXgB,SAAS,EAAC,YAAY;cACtBd,KAAK,EAAEP,QAAQ,CAACE,KAAM;cACtB4B,QAAQ,EAAGd,CAAC,IAAKf,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEE,KAAK,EAAEc,CAAC,CAACV,MAAM,CAACC;cAAK,CAAC,CAAE;cACnEwB,QAAQ;cACRC,WAAW,EAAC;YAA+B;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/B,OAAA;gBAAO8B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CnC,OAAA;gBACE8B,SAAS,EAAC,aAAa;gBACvBd,KAAK,EAAEP,QAAQ,CAACI,QAAS;gBACzB0B,QAAQ,EAAGd,CAAC,IAAKf,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEI,QAAQ,EAAEY,CAAC,CAACV,MAAM,CAACC;gBAAK,CAAC,CAAE;gBAAAe,QAAA,gBAEtE/B,OAAA;kBAAQgB,KAAK,EAAC,OAAO;kBAAAe,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnC,OAAA;kBAAQgB,KAAK,EAAC,UAAU;kBAAAe,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnC,OAAA;kBAAQgB,KAAK,EAAC,UAAU;kBAAAe,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnC,OAAA;kBAAQgB,KAAK,EAAC,UAAU;kBAAAe,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnC,OAAA;kBAAQgB,KAAK,EAAC,OAAO;kBAAAe,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnC,OAAA;kBAAQgB,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/B,OAAA;gBAAO8B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CnC,OAAA;gBACE8B,SAAS,EAAC,aAAa;gBACvBd,KAAK,EAAEP,QAAQ,CAACK,IAAK;gBACrByB,QAAQ,EAAGd,CAAC,IAAKf,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEK,IAAI,EAAEW,CAAC,CAACV,MAAM,CAACC;gBAAK,CAAC,CAAE;gBAAAe,QAAA,gBAElE/B,OAAA;kBAAQgB,KAAK,EAAC,OAAO;kBAAAe,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnC,OAAA;kBAAQgB,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCnC,OAAA;kBAAQgB,KAAK,EAAC,SAAS;kBAAAe,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/B,OAAA;gBAAO8B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDnC,OAAA;gBACEc,IAAI,EAAC,QAAQ;gBACbgB,SAAS,EAAC,YAAY;gBACtBd,KAAK,EAAEP,QAAQ,CAACM,MAAM,CAACC,KAAM;gBAC7BuB,QAAQ,EAAGd,CAAC,IAAKf,WAAW,CAAC;kBAC3B,GAAGD,QAAQ;kBACXM,MAAM,EAAE;oBAAC,GAAGN,QAAQ,CAACM,MAAM;oBAAEC,KAAK,EAAES,CAAC,CAACV,MAAM,CAACC;kBAAK;gBACpD,CAAC,CAAE;gBACHwB,QAAQ;gBACRC,WAAW,EAAC;cAAa;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/B,OAAA;gBAAO8B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CnC,OAAA;gBACEc,IAAI,EAAC,MAAM;gBACXgB,SAAS,EAAC,YAAY;gBACtBd,KAAK,EAAEP,QAAQ,CAACM,MAAM,CAACE,IAAK;gBAC5BsB,QAAQ,EAAGd,CAAC,IAAKf,WAAW,CAAC;kBAC3B,GAAGD,QAAQ;kBACXM,MAAM,EAAE;oBAAC,GAAGN,QAAQ,CAACM,MAAM;oBAAEE,IAAI,EAAEQ,CAAC,CAACV,MAAM,CAACC;kBAAK;gBACnD,CAAC,CAAE;gBACHyB,WAAW,EAAC;cAA0B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/B,OAAA;cAAQc,IAAI,EAAC,QAAQ;cAACgB,SAAS,EAAC,mBAAmB;cAACM,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,KAAK,CAAE;cAAAuB,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnC,OAAA;cAAQc,IAAI,EAAC,QAAQ;cAACgB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnC,OAAA;MAAK8B,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxB5B,KAAK,CAACuC,GAAG,CAAEC,IAAI;QAAA,IAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,aAAA;QAAA,oBACdlD,OAAA;UAAoB8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvC/B,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/B,OAAA;cAAA+B,QAAA,EAAKY,IAAI,CAAChC;YAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBnC,OAAA;cAAM8B,SAAS,EAAE,2BAA2Ba,IAAI,CAACpB,QAAQ,EAAG;cAAAQ,QAAA,EACzDY,IAAI,CAACpB;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnC,OAAA;YAAG8B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEY,IAAI,CAAC/B;UAAW;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDnC,OAAA;YAAK8B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B/B,OAAA;cAAK8B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B/B,OAAA;gBAAA+B,QAAA,GAAO,EAAAa,aAAA,GAAAD,IAAI,CAACQ,OAAO,cAAAP,aAAA,uBAAZA,aAAA,CAAc5B,KAAK,KAAI,CAAC,EAAC,KAAG,GAAA6B,YAAA,GAACF,IAAI,CAAC5B,MAAM,cAAA8B,YAAA,uBAAXA,YAAA,CAAa7B,KAAK,EAAC,GAAC,GAAA8B,aAAA,GAACH,IAAI,CAAC5B,MAAM,cAAA+B,aAAA,uBAAXA,aAAA,CAAa7B,IAAI;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFnC,OAAA;gBAAA+B,QAAA,GAAOqB,IAAI,CAACC,KAAK,CAAE,EAAAN,cAAA,GAAAJ,IAAI,CAACQ,OAAO,cAAAJ,cAAA,uBAAZA,cAAA,CAAc/B,KAAK,MAAAgC,aAAA,GAAGL,IAAI,CAAC5B,MAAM,cAAAiC,aAAA,uBAAXA,aAAA,CAAahC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNnC,OAAA;cAAK8B,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B/B,OAAA;gBACE8B,SAAS,EAAC,eAAe;gBACzBwB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGH,IAAI,CAACI,GAAG,CAAE,EAAAP,cAAA,GAAAN,IAAI,CAACQ,OAAO,cAAAF,cAAA,uBAAZA,cAAA,CAAcjC,KAAK,MAAAkC,aAAA,GAAGP,IAAI,CAAC5B,MAAM,cAAAmC,aAAA,uBAAXA,aAAA,CAAalC,KAAK,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;gBAAI;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/B,OAAA;cAAA+B,QAAA,GAAM,eAAG,EAACY,IAAI,CAAC7B,IAAI;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3BnC,OAAA;cAAA+B,QAAA,GAAM,qBAAI,EAACY,IAAI,CAAC9B,QAAQ;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCnC,OAAA;cAAA+B,QAAA,GAAM,SAAE,EAACY,IAAI,CAACc,aAAa,IAAI,CAAC,EAAC,YAAU;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA,GAxBEQ,IAAI,CAACe,GAAG;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBb,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnC,OAAA;MAAO2D,GAAG;MAAA5B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjC,EAAA,CA5LID,KAAK;EAAA,QACkDH,aAAa;AAAA;AAAA8D,EAAA,GADpE3D,KAAK;AA8LX,eAAeA,KAAK;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}