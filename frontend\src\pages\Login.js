import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('Login form submitted with:', formData);

    const result = await login(formData.email, formData.password);

    console.log('Login result:', result);

    if (result.success) {
      console.log('Login successful, navigating to dashboard');
      navigate('/');
    } else {
      console.log('Login failed:', result.message);
      setError(result.message);
    }

    setLoading(false);
  };

  return (
    <div className="flex-center" style={{ minHeight: '100vh', padding: '20px' }}>
      <div className="card" style={{ maxWidth: '400px', width: '100%' }}>
        <div className="text-center mb-4">
          <h1 className="card-title" style={{ fontSize: '2rem', marginBottom: '10px' }}>
            🏥 HealthConnect
          </h1>
          <p className="text-muted">Sign in to your account</p>
        </div>

        {error && (
          <div className="alert alert-danger">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Email Address</label>
            <input
              type="email"
              name="email"
              className="form-input"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="Enter your email"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Password</label>
            <input
              type="password"
              name="password"
              className="form-input"
              value={formData.password}
              onChange={handleChange}
              required
              placeholder="Enter your password"
            />
          </div>

          <button
            type="submit"
            className="btn btn-primary btn-large"
            disabled={loading}
            style={{ width: '100%', marginTop: '20px' }}
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </button>
        </form>

        <div className="text-center mt-3">
          <p className="text-muted">
            Don't have an account?{' '}
            <Link to="/register" className="text-primary" style={{ textDecoration: 'none' }}>
              Sign up here
            </Link>
          </p>
        </div>

        <div className="mt-4 p-3" style={{ background: 'rgba(102, 126, 234, 0.1)', borderRadius: '8px' }}>
          <h4 style={{ fontSize: '1rem', marginBottom: '10px', color: '#667eea' }}>
            Demo Credentials
          </h4>
          <p style={{ fontSize: '0.9rem', margin: '5px 0', color: '#666' }}>
            <strong>Email:</strong> <EMAIL>
          </p>
          <p style={{ fontSize: '0.9rem', margin: '5px 0', color: '#666' }}>
            <strong>Password:</strong> demo123
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
