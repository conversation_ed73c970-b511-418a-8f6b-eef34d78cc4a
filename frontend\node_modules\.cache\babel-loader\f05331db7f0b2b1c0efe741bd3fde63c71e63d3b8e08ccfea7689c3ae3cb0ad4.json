{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\HealthData.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HealthData = () => {\n  _s();\n  const {\n    healthData,\n    fetchHealthData,\n    addHealthData,\n    loading\n  } = useHealthData();\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    steps: '',\n    distance: '',\n    caloriesBurned: '',\n    activeMinutes: '',\n    heartRate: {\n      resting: '',\n      average: '',\n      maximum: ''\n    },\n    sleep: {\n      totalSleep: '',\n      sleepEfficiency: ''\n    },\n    mood: '',\n    energyLevel: '',\n    waterIntake: ''\n  });\n  useEffect(() => {\n    fetchHealthData();\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const result = await addHealthData(formData);\n    if (result.success) {\n      setShowAddForm(false);\n      setFormData({\n        date: new Date().toISOString().split('T')[0],\n        steps: '',\n        distance: '',\n        caloriesBurned: '',\n        activeMinutes: '',\n        heartRate: {\n          resting: '',\n          average: '',\n          maximum: ''\n        },\n        sleep: {\n          totalSleep: '',\n          sleepEfficiency: ''\n        },\n        mood: '',\n        energyLevel: '',\n        waterIntake: ''\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"health-data-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Health Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddForm(true),\n        children: \"Add Today's Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddForm(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Add Health Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                name: \"date\",\n                className: \"form-input\",\n                value: formData.date,\n                onChange: handleInputChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Steps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"steps\",\n                className: \"form-input\",\n                value: formData.steps,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 8500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Distance (km)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.1\",\n                name: \"distance\",\n                className: \"form-input\",\n                value: formData.distance,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 6.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Calories Burned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"caloriesBurned\",\n                className: \"form-input\",\n                value: formData.caloriesBurned,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 2200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Active Minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"activeMinutes\",\n                className: \"form-input\",\n                value: formData.activeMinutes,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Resting Heart Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"heartRate.resting\",\n                className: \"form-input\",\n                value: formData.heartRate.resting,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 65\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Sleep Duration (minutes)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"sleep.totalSleep\",\n                className: \"form-input\",\n                value: formData.sleep.totalSleep,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 480\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Mood (1-10)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"1\",\n                max: \"10\",\n                name: \"mood\",\n                className: \"form-input\",\n                value: formData.mood,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: () => setShowAddForm(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              children: \"Save Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"health-data-list\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this) : healthData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No health data yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Start tracking your health by adding your first entry!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this) : healthData.map(data => {\n        var _data$steps, _data$heartRate, _data$sleep;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"data-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: new Date(data.date).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"activity-score\",\n              children: [\"Score: \", data.activityScore || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-metrics\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-icon\",\n                children: \"\\uD83D\\uDC5F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: ((_data$steps = data.steps) === null || _data$steps === void 0 ? void 0 : _data$steps.toLocaleString()) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Steps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-icon\",\n                children: \"\\uD83D\\uDD25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: data.caloriesBurned || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Calories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-icon\",\n                children: \"\\uD83D\\uDC93\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: ((_data$heartRate = data.heartRate) === null || _data$heartRate === void 0 ? void 0 : _data$heartRate.resting) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Resting HR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-icon\",\n                children: \"\\uD83D\\uDE34\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-value\",\n                children: Math.round((((_data$sleep = data.sleep) === null || _data$sleep === void 0 ? void 0 : _data$sleep.totalSleep) || 0) / 60)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"metric-label\",\n                children: \"Sleep (hrs)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, data._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .health-data-page {\n          padding: 0;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 30px;\n        }\n\n        .page-header h1 {\n          margin: 0;\n          color: #2c3e50;\n        }\n\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 15px;\n          padding: 30px;\n          max-width: 600px;\n          width: 90%;\n          max-height: 80vh;\n          overflow-y: auto;\n        }\n\n        .modal-content h2 {\n          margin: 0 0 20px 0;\n          color: #2c3e50;\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 15px;\n          justify-content: flex-end;\n          margin-top: 20px;\n        }\n\n        .health-data-list {\n          display: grid;\n          gap: 20px;\n        }\n\n        .no-data {\n          text-align: center;\n          padding: 60px 20px;\n          color: #7f8c8d;\n        }\n\n        .data-card {\n          background: rgba(255, 255, 255, 0.95);\n          border-radius: 15px;\n          padding: 25px;\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n\n        .data-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 20px;\n          padding-bottom: 15px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .data-header h3 {\n          margin: 0;\n          color: #2c3e50;\n        }\n\n        .activity-score {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          padding: 5px 12px;\n          border-radius: 20px;\n          font-size: 0.9rem;\n          font-weight: 600;\n        }\n\n        .data-metrics {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n          gap: 20px;\n        }\n\n        .metric {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          padding: 15px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .metric-icon {\n          font-size: 1.5rem;\n          margin-bottom: 8px;\n        }\n\n        .metric-value {\n          font-size: 1.3rem;\n          font-weight: 700;\n          color: #2c3e50;\n          margin-bottom: 4px;\n        }\n\n        .metric-label {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          font-weight: 500;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            gap: 15px;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            padding: 20px;\n            width: 95%;\n          }\n\n          .data-metrics {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(HealthData, \"1jpcG/PqNgQ1lwQaZTUQQZj+kD0=\", false, function () {\n  return [useHealthData];\n});\n_c = HealthData;\nexport default HealthData;\nvar _c;\n$RefreshReg$(_c, \"HealthData\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHealthData", "jsxDEV", "_jsxDEV", "HealthData", "_s", "healthData", "fetchHealthData", "addHealthData", "loading", "showAddForm", "setShowAddForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "steps", "distance", "caloriesBurned", "activeMinutes", "heartRate", "resting", "average", "maximum", "sleep", "totalSleep", "sleepEfficiency", "mood", "energyLevel", "waterIntake", "handleInputChange", "e", "name", "value", "target", "includes", "parent", "child", "prev", "handleSubmit", "preventDefault", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "stopPropagation", "onSubmit", "type", "onChange", "required", "placeholder", "step", "min", "max", "length", "map", "data", "_data$steps", "_data$heartRate", "_data$sleep", "toLocaleDateString", "activityScore", "toLocaleString", "Math", "round", "_id", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/HealthData.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst HealthData = () => {\n  const { healthData, fetchHealthData, addHealthData, loading } = useHealthData();\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    steps: '',\n    distance: '',\n    caloriesBurned: '',\n    activeMinutes: '',\n    heartRate: { resting: '', average: '', maximum: '' },\n    sleep: { totalSleep: '', sleepEfficiency: '' },\n    mood: '',\n    energyLevel: '',\n    waterIntake: ''\n  });\n\n  useEffect(() => {\n    fetchHealthData();\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: { ...prev[parent], [child]: value }\n      }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const result = await addHealthData(formData);\n    if (result.success) {\n      setShowAddForm(false);\n      setFormData({\n        date: new Date().toISOString().split('T')[0],\n        steps: '', distance: '', caloriesBurned: '', activeMinutes: '',\n        heartRate: { resting: '', average: '', maximum: '' },\n        sleep: { totalSleep: '', sleepEfficiency: '' },\n        mood: '', energyLevel: '', waterIntake: ''\n      });\n    }\n  };\n\n  return (\n    <div className=\"health-data-page\">\n      <div className=\"page-header\">\n        <h1>Health Data</h1>\n        <button \n          className=\"btn btn-primary\"\n          onClick={() => setShowAddForm(true)}\n        >\n          Add Today's Data\n        </button>\n      </div>\n\n      {showAddForm && (\n        <div className=\"modal-overlay\" onClick={() => setShowAddForm(false)}>\n          <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n            <h2>Add Health Data</h2>\n            <form onSubmit={handleSubmit}>\n              <div className=\"grid grid-2\">\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Date</label>\n                  <input\n                    type=\"date\"\n                    name=\"date\"\n                    className=\"form-input\"\n                    value={formData.date}\n                    onChange={handleInputChange}\n                    required\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Steps</label>\n                  <input\n                    type=\"number\"\n                    name=\"steps\"\n                    className=\"form-input\"\n                    value={formData.steps}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 8500\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Distance (km)</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.1\"\n                    name=\"distance\"\n                    className=\"form-input\"\n                    value={formData.distance}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 6.5\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Calories Burned</label>\n                  <input\n                    type=\"number\"\n                    name=\"caloriesBurned\"\n                    className=\"form-input\"\n                    value={formData.caloriesBurned}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 2200\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Active Minutes</label>\n                  <input\n                    type=\"number\"\n                    name=\"activeMinutes\"\n                    className=\"form-input\"\n                    value={formData.activeMinutes}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 45\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Resting Heart Rate</label>\n                  <input\n                    type=\"number\"\n                    name=\"heartRate.resting\"\n                    className=\"form-input\"\n                    value={formData.heartRate.resting}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 65\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Sleep Duration (minutes)</label>\n                  <input\n                    type=\"number\"\n                    name=\"sleep.totalSleep\"\n                    className=\"form-input\"\n                    value={formData.sleep.totalSleep}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 480\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Mood (1-10)</label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"10\"\n                    name=\"mood\"\n                    className=\"form-input\"\n                    value={formData.mood}\n                    onChange={handleInputChange}\n                    placeholder=\"e.g., 8\"\n                  />\n                </div>\n              </div>\n              <div className=\"form-actions\">\n                <button type=\"button\" className=\"btn btn-secondary\" onClick={() => setShowAddForm(false)}>\n                  Cancel\n                </button>\n                <button type=\"submit\" className=\"btn btn-primary\">\n                  Save Data\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      <div className=\"health-data-list\">\n        {loading ? (\n          <div className=\"loading-spinner\"></div>\n        ) : healthData.length === 0 ? (\n          <div className=\"no-data\">\n            <h3>No health data yet</h3>\n            <p>Start tracking your health by adding your first entry!</p>\n          </div>\n        ) : (\n          healthData.map((data) => (\n            <div key={data._id} className=\"data-card\">\n              <div className=\"data-header\">\n                <h3>{new Date(data.date).toLocaleDateString()}</h3>\n                <span className=\"activity-score\">Score: {data.activityScore || 0}</span>\n              </div>\n              <div className=\"data-metrics\">\n                <div className=\"metric\">\n                  <span className=\"metric-icon\">👟</span>\n                  <span className=\"metric-value\">{data.steps?.toLocaleString() || 0}</span>\n                  <span className=\"metric-label\">Steps</span>\n                </div>\n                <div className=\"metric\">\n                  <span className=\"metric-icon\">🔥</span>\n                  <span className=\"metric-value\">{data.caloriesBurned || 0}</span>\n                  <span className=\"metric-label\">Calories</span>\n                </div>\n                <div className=\"metric\">\n                  <span className=\"metric-icon\">💓</span>\n                  <span className=\"metric-value\">{data.heartRate?.resting || 0}</span>\n                  <span className=\"metric-label\">Resting HR</span>\n                </div>\n                <div className=\"metric\">\n                  <span className=\"metric-icon\">😴</span>\n                  <span className=\"metric-value\">{Math.round((data.sleep?.totalSleep || 0) / 60)}</span>\n                  <span className=\"metric-label\">Sleep (hrs)</span>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <style jsx>{`\n        .health-data-page {\n          padding: 0;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 30px;\n        }\n\n        .page-header h1 {\n          margin: 0;\n          color: #2c3e50;\n        }\n\n        .modal-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 1000;\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 15px;\n          padding: 30px;\n          max-width: 600px;\n          width: 90%;\n          max-height: 80vh;\n          overflow-y: auto;\n        }\n\n        .modal-content h2 {\n          margin: 0 0 20px 0;\n          color: #2c3e50;\n        }\n\n        .form-actions {\n          display: flex;\n          gap: 15px;\n          justify-content: flex-end;\n          margin-top: 20px;\n        }\n\n        .health-data-list {\n          display: grid;\n          gap: 20px;\n        }\n\n        .no-data {\n          text-align: center;\n          padding: 60px 20px;\n          color: #7f8c8d;\n        }\n\n        .data-card {\n          background: rgba(255, 255, 255, 0.95);\n          border-radius: 15px;\n          padding: 25px;\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n\n        .data-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 20px;\n          padding-bottom: 15px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .data-header h3 {\n          margin: 0;\n          color: #2c3e50;\n        }\n\n        .activity-score {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          padding: 5px 12px;\n          border-radius: 20px;\n          font-size: 0.9rem;\n          font-weight: 600;\n        }\n\n        .data-metrics {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n          gap: 20px;\n        }\n\n        .metric {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          padding: 15px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .metric-icon {\n          font-size: 1.5rem;\n          margin-bottom: 8px;\n        }\n\n        .metric-value {\n          font-size: 1.3rem;\n          font-weight: 700;\n          color: #2c3e50;\n          margin-bottom: 4px;\n        }\n\n        .metric-label {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          font-weight: 500;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            gap: 15px;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            padding: 20px;\n            width: 95%;\n          }\n\n          .data-metrics {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HealthData;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,UAAU;IAAEC,eAAe;IAAEC,aAAa;IAAEC;EAAQ,CAAC,GAAGR,aAAa,CAAC,CAAC;EAC/E,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IACpDC,KAAK,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,eAAe,EAAE;IAAG,CAAC;IAC9CC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF/B,SAAS,CAAC,MAAM;IACdO,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,IAAIF,IAAI,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGL,IAAI,CAACjB,KAAK,CAAC,GAAG,CAAC;MACvCJ,WAAW,CAAC2B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACF,MAAM,GAAG;UAAE,GAAGE,IAAI,CAACF,MAAM,CAAC;UAAE,CAACC,KAAK,GAAGJ;QAAM;MAC9C,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLtB,WAAW,CAAC2B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACN,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,MAAMnC,aAAa,CAACI,QAAQ,CAAC;IAC5C,IAAI+B,MAAM,CAACC,OAAO,EAAE;MAClBjC,cAAc,CAAC,KAAK,CAAC;MACrBE,WAAW,CAAC;QACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5CC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,cAAc,EAAE,EAAE;QAAEC,aAAa,EAAE,EAAE;QAC9DC,SAAS,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC;QACpDC,KAAK,EAAE;UAAEC,UAAU,EAAE,EAAE;UAAEC,eAAe,EAAE;QAAG,CAAC;QAC9CC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,WAAW,EAAE;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACE5B,OAAA;IAAK0C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B3C,OAAA;MAAK0C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3C,OAAA;QAAA2C,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpB/C,OAAA;QACE0C,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAAC,IAAI,CAAE;QAAAmC,QAAA,EACrC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELxC,WAAW,iBACVP,OAAA;MAAK0C,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAAC,KAAK,CAAE;MAAAmC,QAAA,eAClE3C,OAAA;QAAK0C,SAAS,EAAC,eAAe;QAACM,OAAO,EAAElB,CAAC,IAAIA,CAAC,CAACmB,eAAe,CAAC,CAAE;QAAAN,QAAA,gBAC/D3C,OAAA;UAAA2C,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxB/C,OAAA;UAAMkD,QAAQ,EAAEZ,YAAa;UAAAK,QAAA,gBAC3B3C,OAAA;YAAK0C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1C/C,OAAA;gBACEmD,IAAI,EAAC,MAAM;gBACXpB,IAAI,EAAC,MAAM;gBACXW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACE,IAAK;gBACrByC,QAAQ,EAAEvB,iBAAkB;gBAC5BwB,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3C/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbpB,IAAI,EAAC,OAAO;gBACZW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACM,KAAM;gBACtBqC,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAY;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnD/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbI,IAAI,EAAC,KAAK;gBACVxB,IAAI,EAAC,UAAU;gBACfW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACO,QAAS;gBACzBoC,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbpB,IAAI,EAAC,gBAAgB;gBACrBW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACQ,cAAe;gBAC/BmC,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAY;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbpB,IAAI,EAAC,eAAe;gBACpBW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACS,aAAc;gBAC9BkC,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxD/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbpB,IAAI,EAAC,mBAAmB;gBACxBW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACU,SAAS,CAACC,OAAQ;gBAClCgC,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9D/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbpB,IAAI,EAAC,kBAAkB;gBACvBW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACc,KAAK,CAACC,UAAW;gBACjC4B,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3C,OAAA;gBAAO0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD/C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbK,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACR1B,IAAI,EAAC,MAAM;gBACXW,SAAS,EAAC,YAAY;gBACtBV,KAAK,EAAEvB,QAAQ,CAACiB,IAAK;gBACrB0B,QAAQ,EAAEvB,iBAAkB;gBAC5ByB,WAAW,EAAC;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3C,OAAA;cAAQmD,IAAI,EAAC,QAAQ;cAACT,SAAS,EAAC,mBAAmB;cAACM,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAAC,KAAK,CAAE;cAAAmC,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/C,OAAA;cAAQmD,IAAI,EAAC,QAAQ;cAACT,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/C,OAAA;MAAK0C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BrC,OAAO,gBACNN,OAAA;QAAK0C,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,GACrC5C,UAAU,CAACuD,MAAM,KAAK,CAAC,gBACzB1D,OAAA;QAAK0C,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtB3C,OAAA;UAAA2C,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B/C,OAAA;UAAA2C,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,GAEN5C,UAAU,CAACwD,GAAG,CAAEC,IAAI;QAAA,IAAAC,WAAA,EAAAC,eAAA,EAAAC,WAAA;QAAA,oBAClB/D,OAAA;UAAoB0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvC3C,OAAA;YAAK0C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3C,OAAA;cAAA2C,QAAA,EAAK,IAAI/B,IAAI,CAACgD,IAAI,CAACjD,IAAI,CAAC,CAACqD,kBAAkB,CAAC;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnD/C,OAAA;cAAM0C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,SAAO,EAACiB,IAAI,CAACK,aAAa,IAAI,CAAC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3C,OAAA;cAAK0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB3C,OAAA;gBAAM0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,EAAAkB,WAAA,GAAAD,IAAI,CAAC7C,KAAK,cAAA8C,WAAA,uBAAVA,WAAA,CAAYK,cAAc,CAAC,CAAC,KAAI;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzE/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB3C,OAAA;gBAAM0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEiB,IAAI,CAAC3C,cAAc,IAAI;cAAC;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChE/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB3C,OAAA;gBAAM0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,EAAAmB,eAAA,GAAAF,IAAI,CAACzC,SAAS,cAAA2C,eAAA,uBAAdA,eAAA,CAAgB1C,OAAO,KAAI;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpE/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB3C,OAAA;gBAAM0C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEwB,IAAI,CAACC,KAAK,CAAC,CAAC,EAAAL,WAAA,GAAAH,IAAI,CAACrC,KAAK,cAAAwC,WAAA,uBAAVA,WAAA,CAAYvC,UAAU,KAAI,CAAC,IAAI,EAAE;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtF/C,OAAA;gBAAM0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1BEa,IAAI,CAACS,GAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Bb,CAAC;MAAA,CACP;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/C,OAAA;MAAOsE,GAAG;MAAA3B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAzWID,UAAU;EAAA,QACkDH,aAAa;AAAA;AAAAyE,EAAA,GADzEtE,UAAU;AA2WhB,eAAeA,UAAU;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}