const express = require('express');
const mongoose = require('mongoose');
const { protect } = require('../middleware/auth');
const Goal = require('../models/Goal');
const router = express.Router();

// @desc    Get user's goals
// @route   GET /api/goals
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const { status, category, type, limit = 20, page = 1 } = req.query;
    const userId = req.user.id;

    let query = { userId };

    // Filtering
    if (status) query.status = status;
    if (category) query.category = category;
    if (type) query.type = type;

    const skip = (page - 1) * limit;
    
    const goals = await Goal.find(query)
      .sort({ priority: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);

    const total = await Goal.countDocuments(query);

    res.json({
      success: true,
      data: {
        goals,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          hasNext: skip + goals.length < total,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get goals error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Create new goal
// @route   POST /api/goals
// @access  Private
router.post('/', protect, async (req, res) => {
  try {
    const goalData = {
      ...req.body,
      userId: req.user.id
    };

    // Validate required fields
    const { title, category, type, target, startDate, endDate } = goalData;
    if (!title || !category || !type || !target || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      return res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
    }

    const goal = await Goal.create(goalData);

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`user-${req.user.id}`).emit('goalUpdate', {
      type: 'created',
      data: goal
    });

    res.status(201).json({
      success: true,
      message: 'Goal created successfully',
      data: { goal }
    });
  } catch (error) {
    console.error('Create goal error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get active goals
// @route   GET /api/goals/active
// @access  Private
router.get('/active', protect, async (req, res) => {
  try {
    const goals = await Goal.getActiveGoals(req.user.id);

    res.json({
      success: true,
      data: { goals }
    });
  } catch (error) {
    console.error('Get active goals error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get goals summary
// @route   GET /api/goals/summary
// @access  Private
router.get('/summary', protect, async (req, res) => {
  try {
    const summary = await Goal.getGoalsSummary(req.user.id);

    res.json({
      success: true,
      data: { summary }
    });
  } catch (error) {
    console.error('Get goals summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update goal progress
// @route   PUT /api/goals/:id/progress
// @access  Private
router.put('/:id/progress', protect, async (req, res) => {
  try {
    const { value, note } = req.body;

    if (value === undefined || value < 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid progress value'
      });
    }

    const goal = await Goal.findById(req.params.id);

    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      });
    }

    // Check ownership
    if (goal.userId.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    await goal.updateProgress(value, note);

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`user-${req.user.id}`).emit('goalUpdate', {
      type: 'progress_updated',
      data: goal
    });

    res.json({
      success: true,
      message: 'Goal progress updated successfully',
      data: { goal }
    });
  } catch (error) {
    console.error('Update goal progress error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update goal
// @route   PUT /api/goals/:id
// @access  Private
router.put('/:id', protect, async (req, res) => {
  try {
    let goal = await Goal.findById(req.params.id);

    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      });
    }

    // Check ownership
    if (goal.userId.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update goal
    Object.assign(goal, req.body);
    goal = await goal.save();

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`user-${req.user.id}`).emit('goalUpdate', {
      type: 'updated',
      data: goal
    });

    res.json({
      success: true,
      message: 'Goal updated successfully',
      data: { goal }
    });
  } catch (error) {
    console.error('Update goal error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Delete goal
// @route   DELETE /api/goals/:id
// @access  Private
router.delete('/:id', protect, async (req, res) => {
  try {
    const goal = await Goal.findById(req.params.id);

    if (!goal) {
      return res.status(404).json({
        success: false,
        message: 'Goal not found'
      });
    }

    // Check ownership
    if (goal.userId.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    await goal.deleteOne();

    // Emit real-time update
    const io = req.app.get('io');
    io.to(`user-${req.user.id}`).emit('goalUpdate', {
      type: 'deleted',
      data: { id: req.params.id }
    });

    res.json({
      success: true,
      message: 'Goal deleted successfully'
    });
  } catch (error) {
    console.error('Delete goal error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
