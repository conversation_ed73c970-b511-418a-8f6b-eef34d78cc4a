const { google } = require('googleapis');
const { OAuth2Client } = require('google-auth-library');

class GoogleFitService {
  constructor() {
    this.oauth2Client = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );
    
    this.fitness = google.fitness('v1');
    
    // Google Fit data source types
    this.dataSources = {
      STEPS: 'derived:com.google.step_count.delta:com.google.android.gms:estimated_steps',
      HEART_RATE: 'derived:com.google.heart_rate.bpm:com.google.android.gms:merge_heart_rate_bpm',
      CALORIES: 'derived:com.google.calories.expended:com.google.android.gms:merge_calories_expended',
      DISTANCE: 'derived:com.google.distance.delta:com.google.android.gms:merge_distance_delta',
      ACTIVE_MINUTES: 'derived:com.google.active_minutes:com.google.android.gms:merge_active_minutes',
      SLEEP: 'derived:com.google.sleep.segment:com.google.android.gms:merged',
      WEIGHT: 'derived:com.google.weight:com.google.android.gms:merge_weight',
      BLOOD_PRESSURE: 'derived:com.google.blood_pressure:com.google.android.gms:merged',
      BLOOD_GLUCOSE: 'derived:com.google.blood_glucose:com.google.android.gms:merged',
      BODY_TEMPERATURE: 'derived:com.google.body.temperature:com.google.android.gms:merged',
      OXYGEN_SATURATION: 'derived:com.google.oxygen_saturation:com.google.android.gms:merged'
    };
  }

  // Generate OAuth URL for user authorization
  getAuthUrl(userId) {
    const scopes = [
      'https://www.googleapis.com/auth/fitness.activity.read',
      'https://www.googleapis.com/auth/fitness.heart_rate.read',
      'https://www.googleapis.com/auth/fitness.sleep.read',
      'https://www.googleapis.com/auth/fitness.body.read',
      'https://www.googleapis.com/auth/fitness.location.read'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state: userId, // Pass userId to identify user after callback
      prompt: 'consent'
    });
  }

  // Exchange authorization code for tokens
  async getTokens(code) {
    try {
      const { tokens } = await this.oauth2Client.getAccessToken(code);
      return tokens;
    } catch (error) {
      console.error('Error getting tokens:', error);
      throw new Error('Failed to get access tokens');
    }
  }

  // Set credentials for API calls
  setCredentials(tokens) {
    this.oauth2Client.setCredentials(tokens);
    google.options({ auth: this.oauth2Client });
  }

  // Refresh access token if needed
  async refreshTokenIfNeeded(tokens) {
    try {
      this.oauth2Client.setCredentials(tokens);
      
      if (tokens.expiry_date && Date.now() >= tokens.expiry_date) {
        const { credentials } = await this.oauth2Client.refreshAccessToken();
        return credentials;
      }
      
      return tokens;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw new Error('Failed to refresh access token');
    }
  }

  // Get health data from Google Fit
  async getHealthData(tokens, startDate, endDate) {
    try {
      await this.refreshTokenIfNeeded(tokens);
      
      const startTimeNanos = startDate.getTime() * 1000000;
      const endTimeNanos = endDate.getTime() * 1000000;

      // Fetch different types of health data
      const [
        stepsData,
        heartRateData,
        caloriesData,
        distanceData,
        activeMinutesData,
        sleepData,
        weightData
      ] = await Promise.allSettled([
        this.getStepsData(startTimeNanos, endTimeNanos),
        this.getHeartRateData(startTimeNanos, endTimeNanos),
        this.getCaloriesData(startTimeNanos, endTimeNanos),
        this.getDistanceData(startTimeNanos, endTimeNanos),
        this.getActiveMinutesData(startTimeNanos, endTimeNanos),
        this.getSleepData(startTimeNanos, endTimeNanos),
        this.getWeightData(startTimeNanos, endTimeNanos)
      ]);

      // Process and combine the data
      return this.processHealthData({
        steps: stepsData.status === 'fulfilled' ? stepsData.value : null,
        heartRate: heartRateData.status === 'fulfilled' ? heartRateData.value : null,
        calories: caloriesData.status === 'fulfilled' ? caloriesData.value : null,
        distance: distanceData.status === 'fulfilled' ? distanceData.value : null,
        activeMinutes: activeMinutesData.status === 'fulfilled' ? activeMinutesData.value : null,
        sleep: sleepData.status === 'fulfilled' ? sleepData.value : null,
        weight: weightData.status === 'fulfilled' ? weightData.value : null
      }, startDate, endDate);

    } catch (error) {
      console.error('Error fetching health data:', error);
      throw new Error('Failed to fetch health data from Google Fit');
    }
  }

  // Get steps data
  async getStepsData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.STEPS,
      requestBody: {
        aggregateBy: [{
          dataTypeName: 'com.google.step_count.delta'
        }],
        bucketByTime: { durationMillis: 86400000 }, // 1 day buckets
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Get heart rate data
  async getHeartRateData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.HEART_RATE,
      requestBody: {
        aggregateBy: [{
          dataTypeName: 'com.google.heart_rate.bpm'
        }],
        bucketByTime: { durationMillis: 86400000 },
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Get calories data
  async getCaloriesData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.CALORIES,
      requestBody: {
        aggregateBy: [{
          dataTypeName: 'com.google.calories.expended'
        }],
        bucketByTime: { durationMillis: 86400000 },
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Get distance data
  async getDistanceData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.DISTANCE,
      requestBody: {
        aggregateBy: [{
          dataTypeName: 'com.google.distance.delta'
        }],
        bucketByTime: { durationMillis: 86400000 },
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Get active minutes data
  async getActiveMinutesData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.ACTIVE_MINUTES,
      requestBody: {
        aggregateBy: [{
          dataTypeName: 'com.google.active_minutes'
        }],
        bucketByTime: { durationMillis: 86400000 },
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Get sleep data
  async getSleepData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.sessions.list({
      userId: 'me',
      startTime: new Date(startTimeNanos / 1000000).toISOString(),
      endTime: new Date(endTimeNanos / 1000000).toISOString(),
      activityType: 72 // Sleep activity type
    });
    
    return response.data;
  }

  // Get weight data
  async getWeightData(startTimeNanos, endTimeNanos) {
    const response = await this.fitness.users.dataSources.dataPointChanges.list({
      userId: 'me',
      dataSourceId: this.dataSources.WEIGHT,
      requestBody: {
        startTimeMillis: Math.floor(startTimeNanos / 1000000),
        endTimeMillis: Math.floor(endTimeNanos / 1000000)
      }
    });
    
    return response.data;
  }

  // Process and normalize the health data
  processHealthData(rawData, startDate, endDate) {
    const processedData = [];
    const dayMap = new Map();

    // Initialize days
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateKey = currentDate.toDateString();
      dayMap.set(dateKey, {
        date: new Date(currentDate),
        steps: 0,
        distance: 0,
        caloriesBurned: 0,
        activeMinutes: 0,
        heartRate: { resting: null, average: null, maximum: null },
        sleep: { totalSleep: null, deepSleep: null, lightSleep: null, remSleep: null },
        vitals: { weight: null, bloodPressure: null, bloodOxygen: null },
        dataSource: 'google_fit',
        syncedAt: new Date()
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Process each data type
    if (rawData.steps?.bucket) {
      this.processStepsData(rawData.steps.bucket, dayMap);
    }
    
    if (rawData.heartRate?.bucket) {
      this.processHeartRateData(rawData.heartRate.bucket, dayMap);
    }
    
    if (rawData.calories?.bucket) {
      this.processCaloriesData(rawData.calories.bucket, dayMap);
    }
    
    if (rawData.distance?.bucket) {
      this.processDistanceData(rawData.distance.bucket, dayMap);
    }
    
    if (rawData.activeMinutes?.bucket) {
      this.processActiveMinutesData(rawData.activeMinutes.bucket, dayMap);
    }
    
    if (rawData.sleep?.session) {
      this.processSleepData(rawData.sleep.session, dayMap);
    }
    
    if (rawData.weight?.insertedDataPoint) {
      this.processWeightData(rawData.weight.insertedDataPoint, dayMap);
    }

    // Convert map to array
    return Array.from(dayMap.values());
  }

  // Helper methods to process specific data types
  processStepsData(buckets, dayMap) {
    buckets.forEach(bucket => {
      if (bucket.dataset && bucket.dataset[0] && bucket.dataset[0].point) {
        bucket.dataset[0].point.forEach(point => {
          const date = new Date(parseInt(point.startTimeNanos) / 1000000);
          const dateKey = date.toDateString();
          const dayData = dayMap.get(dateKey);
          
          if (dayData && point.value && point.value[0]) {
            dayData.steps += point.value[0].intVal || 0;
          }
        });
      }
    });
  }

  processHeartRateData(buckets, dayMap) {
    buckets.forEach(bucket => {
      if (bucket.dataset && bucket.dataset[0] && bucket.dataset[0].point) {
        const heartRates = [];
        
        bucket.dataset[0].point.forEach(point => {
          const date = new Date(parseInt(point.startTimeNanos) / 1000000);
          const dateKey = date.toDateString();
          
          if (point.value && point.value[0]) {
            heartRates.push(point.value[0].fpVal);
          }
        });
        
        if (heartRates.length > 0) {
          const date = new Date(parseInt(bucket.startTimeMillis));
          const dateKey = date.toDateString();
          const dayData = dayMap.get(dateKey);
          
          if (dayData) {
            dayData.heartRate.average = Math.round(heartRates.reduce((a, b) => a + b, 0) / heartRates.length);
            dayData.heartRate.maximum = Math.round(Math.max(...heartRates));
            dayData.heartRate.resting = Math.round(Math.min(...heartRates));
          }
        }
      }
    });
  }

  processCaloriesData(buckets, dayMap) {
    buckets.forEach(bucket => {
      if (bucket.dataset && bucket.dataset[0] && bucket.dataset[0].point) {
        bucket.dataset[0].point.forEach(point => {
          const date = new Date(parseInt(point.startTimeNanos) / 1000000);
          const dateKey = date.toDateString();
          const dayData = dayMap.get(dateKey);
          
          if (dayData && point.value && point.value[0]) {
            dayData.caloriesBurned += Math.round(point.value[0].fpVal || 0);
          }
        });
      }
    });
  }

  processDistanceData(buckets, dayMap) {
    buckets.forEach(bucket => {
      if (bucket.dataset && bucket.dataset[0] && bucket.dataset[0].point) {
        bucket.dataset[0].point.forEach(point => {
          const date = new Date(parseInt(point.startTimeNanos) / 1000000);
          const dateKey = date.toDateString();
          const dayData = dayMap.get(dateKey);
          
          if (dayData && point.value && point.value[0]) {
            dayData.distance += (point.value[0].fpVal || 0) / 1000; // Convert meters to km
          }
        });
      }
    });
  }

  processActiveMinutesData(buckets, dayMap) {
    buckets.forEach(bucket => {
      if (bucket.dataset && bucket.dataset[0] && bucket.dataset[0].point) {
        bucket.dataset[0].point.forEach(point => {
          const date = new Date(parseInt(point.startTimeNanos) / 1000000);
          const dateKey = date.toDateString();
          const dayData = dayMap.get(dateKey);
          
          if (dayData && point.value && point.value[0]) {
            dayData.activeMinutes += Math.round(point.value[0].intVal || 0);
          }
        });
      }
    });
  }

  processSleepData(sessions, dayMap) {
    sessions.forEach(session => {
      const startDate = new Date(session.startTimeMillis);
      const endDate = new Date(session.endTimeMillis);
      const dateKey = startDate.toDateString();
      const dayData = dayMap.get(dateKey);
      
      if (dayData) {
        const durationMinutes = Math.round((endDate - startDate) / (1000 * 60));
        dayData.sleep.totalSleep = durationMinutes;
        
        // Estimate sleep stages (Google Fit may not provide detailed stages)
        dayData.sleep.deepSleep = Math.round(durationMinutes * 0.2); // ~20% deep sleep
        dayData.sleep.lightSleep = Math.round(durationMinutes * 0.6); // ~60% light sleep
        dayData.sleep.remSleep = Math.round(durationMinutes * 0.2); // ~20% REM sleep
      }
    });
  }

  processWeightData(dataPoints, dayMap) {
    dataPoints.forEach(point => {
      const date = new Date(parseInt(point.startTimeNanos) / 1000000);
      const dateKey = date.toDateString();
      const dayData = dayMap.get(dateKey);
      
      if (dayData && point.value && point.value[0]) {
        dayData.vitals.weight = Math.round(point.value[0].fpVal * 10) / 10; // Round to 1 decimal
      }
    });
  }
}

module.exports = GoogleFitService;
