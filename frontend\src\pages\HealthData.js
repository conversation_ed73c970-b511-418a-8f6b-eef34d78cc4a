import React, { useState, useEffect } from 'react';
import { useHealthData } from '../contexts/HealthDataContext';

const HealthData = () => {
  const { healthData, fetchHealthData, addHealthData, loading } = useHealthData();
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    steps: '',
    distance: '',
    caloriesBurned: '',
    activeMinutes: '',
    heartRate: { resting: '', average: '', maximum: '' },
    sleep: { totalSleep: '', sleepEfficiency: '' },
    mood: '',
    energyLevel: '',
    waterIntake: ''
  });

  useEffect(() => {
    fetchHealthData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: { ...prev[parent], [child]: value }
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const result = await addHealthData(formData);
    if (result.success) {
      setShowAddForm(false);
      setFormData({
        date: new Date().toISOString().split('T')[0],
        steps: '', distance: '', caloriesBurned: '', activeMinutes: '',
        heartRate: { resting: '', average: '', maximum: '' },
        sleep: { totalSleep: '', sleepEfficiency: '' },
        mood: '', energyLevel: '', waterIntake: ''
      });
    }
  };

  return (
    <div className="health-data-page">
      <div className="page-header">
        <h1>Health Data</h1>
        <button 
          className="btn btn-primary"
          onClick={() => setShowAddForm(true)}
        >
          Add Today's Data
        </button>
      </div>

      {showAddForm && (
        <div className="modal-overlay" onClick={() => setShowAddForm(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <h2>Add Health Data</h2>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">Date</label>
                  <input
                    type="date"
                    name="date"
                    className="form-input"
                    value={formData.date}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Steps</label>
                  <input
                    type="number"
                    name="steps"
                    className="form-input"
                    value={formData.steps}
                    onChange={handleInputChange}
                    placeholder="e.g., 8500"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Distance (km)</label>
                  <input
                    type="number"
                    step="0.1"
                    name="distance"
                    className="form-input"
                    value={formData.distance}
                    onChange={handleInputChange}
                    placeholder="e.g., 6.5"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Calories Burned</label>
                  <input
                    type="number"
                    name="caloriesBurned"
                    className="form-input"
                    value={formData.caloriesBurned}
                    onChange={handleInputChange}
                    placeholder="e.g., 2200"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Active Minutes</label>
                  <input
                    type="number"
                    name="activeMinutes"
                    className="form-input"
                    value={formData.activeMinutes}
                    onChange={handleInputChange}
                    placeholder="e.g., 45"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Resting Heart Rate</label>
                  <input
                    type="number"
                    name="heartRate.resting"
                    className="form-input"
                    value={formData.heartRate.resting}
                    onChange={handleInputChange}
                    placeholder="e.g., 65"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Sleep Duration (minutes)</label>
                  <input
                    type="number"
                    name="sleep.totalSleep"
                    className="form-input"
                    value={formData.sleep.totalSleep}
                    onChange={handleInputChange}
                    placeholder="e.g., 480"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Mood (1-10)</label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    name="mood"
                    className="form-input"
                    value={formData.mood}
                    onChange={handleInputChange}
                    placeholder="e.g., 8"
                  />
                </div>
              </div>
              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Save Data
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="health-data-list">
        {loading ? (
          <div className="loading-spinner"></div>
        ) : healthData.length === 0 ? (
          <div className="no-data">
            <h3>No health data yet</h3>
            <p>Start tracking your health by adding your first entry!</p>
          </div>
        ) : (
          healthData.map((data) => (
            <div key={data._id} className="data-card">
              <div className="data-header">
                <h3>{new Date(data.date).toLocaleDateString()}</h3>
                <span className="activity-score">Score: {data.activityScore || 0}</span>
              </div>
              <div className="data-metrics">
                <div className="metric">
                  <span className="metric-icon">👟</span>
                  <span className="metric-value">{data.steps?.toLocaleString() || 0}</span>
                  <span className="metric-label">Steps</span>
                </div>
                <div className="metric">
                  <span className="metric-icon">🔥</span>
                  <span className="metric-value">{data.caloriesBurned || 0}</span>
                  <span className="metric-label">Calories</span>
                </div>
                <div className="metric">
                  <span className="metric-icon">💓</span>
                  <span className="metric-value">{data.heartRate?.resting || 0}</span>
                  <span className="metric-label">Resting HR</span>
                </div>
                <div className="metric">
                  <span className="metric-icon">😴</span>
                  <span className="metric-value">{Math.round((data.sleep?.totalSleep || 0) / 60)}</span>
                  <span className="metric-label">Sleep (hrs)</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <style jsx>{`
        .health-data-page {
          padding: 0;
        }

        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .page-header h1 {
          margin: 0;
          color: #2c3e50;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          border-radius: 15px;
          padding: 30px;
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
        }

        .modal-content h2 {
          margin: 0 0 20px 0;
          color: #2c3e50;
        }

        .form-actions {
          display: flex;
          gap: 15px;
          justify-content: flex-end;
          margin-top: 20px;
        }

        .health-data-list {
          display: grid;
          gap: 20px;
        }

        .no-data {
          text-align: center;
          padding: 60px 20px;
          color: #7f8c8d;
        }

        .data-card {
          background: rgba(255, 255, 255, 0.95);
          border-radius: 15px;
          padding: 25px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .data-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 2px solid #f0f0f0;
        }

        .data-header h3 {
          margin: 0;
          color: #2c3e50;
        }

        .activity-score {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 5px 12px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 600;
        }

        .data-metrics {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 20px;
        }

        .metric {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: 15px;
          background: rgba(102, 126, 234, 0.05);
          border-radius: 12px;
          border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .metric-icon {
          font-size: 1.5rem;
          margin-bottom: 8px;
        }

        .metric-value {
          font-size: 1.3rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 4px;
        }

        .metric-label {
          font-size: 0.8rem;
          color: #7f8c8d;
          font-weight: 500;
        }

        @media (max-width: 768px) {
          .page-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
          }

          .modal-content {
            padding: 20px;
            width: 95%;
          }

          .data-metrics {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      `}</style>
    </div>
  );
};

export default HealthData;
