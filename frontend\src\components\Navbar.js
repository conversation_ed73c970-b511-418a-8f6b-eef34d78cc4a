import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './Navbar.css';

const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  if (!isAuthenticated) {
    return null; // Don't show navbar on login/register pages
  }

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-brand">
          <span className="brand-icon">🏥</span>
          HealthConnect
        </Link>

        <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>
          <Link 
            to="/" 
            className={`navbar-item ${isActive('/') ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(false)}
          >
            <span className="nav-icon">📊</span>
            Dashboard
          </Link>
          
          <Link 
            to="/health-data" 
            className={`navbar-item ${isActive('/health-data') ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(false)}
          >
            <span className="nav-icon">💓</span>
            Health Data
          </Link>
          
          <Link 
            to="/goals" 
            className={`navbar-item ${isActive('/goals') ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(false)}
          >
            <span className="nav-icon">🎯</span>
            Goals
          </Link>
          
          <Link 
            to="/predictions" 
            className={`navbar-item ${isActive('/predictions') ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(false)}
          >
            <span className="nav-icon">🔮</span>
            Predictions
          </Link>
          
          <Link 
            to="/chatbot" 
            className={`navbar-item ${isActive('/chatbot') ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(false)}
          >
            <span className="nav-icon">🤖</span>
            Health Assistant
          </Link>
        </div>

        <div className="navbar-user">
          <div className="user-dropdown">
            <button className="user-button">
              <span className="user-avatar">
                {user?.name?.charAt(0).toUpperCase() || '👤'}
              </span>
              <span className="user-name">{user?.name || 'User'}</span>
              <span className="dropdown-arrow">▼</span>
            </button>
            
            <div className="dropdown-menu">
              <Link 
                to="/profile" 
                className="dropdown-item"
                onClick={() => setIsMenuOpen(false)}
              >
                <span className="dropdown-icon">👤</span>
                Profile
              </Link>
              <button 
                onClick={handleLogout}
                className="dropdown-item logout-btn"
              >
                <span className="dropdown-icon">🚪</span>
                Logout
              </button>
            </div>
          </div>
        </div>

        <button 
          className="navbar-toggle"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
