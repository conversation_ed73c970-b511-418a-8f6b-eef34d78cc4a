{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport StatsCard from '../components/StatsCard';\nimport ActivityChart from '../components/ActivityChart';\nimport GoalProgress from '../components/GoalProgress';\nimport HealthInsights from '../components/HealthInsights';\nimport QuickActions from '../components/QuickActions';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _today$steps, _today$calories, _goals$active, _weekly$totalSteps;\n  const {\n    user\n  } = useAuth();\n  const {\n    dashboardData,\n    loading,\n    fetchDashboardData\n  } = useHealthData();\n  const [selectedPeriod, setSelectedPeriod] = useState('7d');\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  if (loading && !dashboardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-center\",\n      style: {\n        minHeight: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n  const today = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.today) || {};\n  const weekly = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.weekly) || {};\n  const goals = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.goals) || {};\n  const prediction = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.prediction;\n  const badges = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.badges) || [];\n  const insights = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.insights) || [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"card-title\",\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"card-subtitle\",\n            children: \"Here's your health overview for today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          style: {\n            gap: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPeriod,\n            onChange: e => setSelectedPeriod(e.target.value),\n            className: \"form-select\",\n            style: {\n              width: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"7d\",\n              children: \"Last 7 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"30d\",\n              children: \"Last 30 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"90d\",\n              children: \"Last 90 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-4\",\n        children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Steps\",\n          value: ((_today$steps = today.steps) === null || _today$steps === void 0 ? void 0 : _today$steps.toLocaleString()) || '0',\n          target: \"10,000\",\n          icon: \"\\uD83D\\uDC5F\",\n          color: \"#667eea\",\n          progress: today.steps / 10000 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Calories\",\n          value: ((_today$calories = today.calories) === null || _today$calories === void 0 ? void 0 : _today$calories.toLocaleString()) || '0',\n          target: \"2,000\",\n          icon: \"\\uD83D\\uDD25\",\n          color: \"#e74c3c\",\n          progress: today.calories / 2000 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Active Minutes\",\n          value: today.activeMinutes || '0',\n          target: \"60\",\n          icon: \"\\u23F1\\uFE0F\",\n          color: \"#f39c12\",\n          progress: today.activeMinutes / 60 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Activity Score\",\n          value: today.activityScore || '0',\n          target: \"100\",\n          icon: \"\\u2B50\",\n          color: \"#27ae60\",\n          progress: today.activityScore || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Activity Trends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivityChart, {\n          period: selectedPeriod\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Active Goals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-muted\",\n            children: [((_goals$active = goals.active) === null || _goals$active === void 0 ? void 0 : _goals$active.length) || 0, \" active goals\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GoalProgress, {\n          goals: goals.active || []\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Health Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HealthInsights, {\n          insights: insights,\n          prediction: prediction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(QuickActions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), badges.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Recent Achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"achievements-grid\",\n        children: badges.map((badge, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"achievement-badge\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-icon\",\n            children: badge.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: badge.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: badge.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Weekly Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: ((_weekly$totalSteps = weekly.totalSteps) === null || _weekly$totalSteps === void 0 ? void 0 : _weekly$totalSteps.toLocaleString()) || '0'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Steps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Avg: \", Math.round(weekly.avgSteps || 0).toLocaleString(), \"/day\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.totalDistance || 0), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Distance Covered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Avg: \", Math.round((weekly.totalDistance || 0) / 7), \" km/day\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.avgSleep || 0), \" min\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Avg Sleep\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [Math.round((weekly.avgSleep || 0) / 60), \" hours/night\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.avgHeartRate || 0), \" bpm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Avg Heart Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: \"Resting rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .dashboard {\n          padding: 0;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 15px;\n        }\n\n        .achievement-badge {\n          display: flex;\n          align-items: center;\n          padding: 15px;\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.2);\n        }\n\n        .badge-icon {\n          font-size: 2rem;\n          margin-right: 15px;\n        }\n\n        .badge-info h4 {\n          margin: 0 0 5px 0;\n          color: #2c3e50;\n          font-size: 1rem;\n        }\n\n        .badge-info p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .summary-stat {\n          text-align: center;\n          padding: 20px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .summary-stat h3 {\n          margin: 0 0 8px 0;\n          font-size: 1.8rem;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        .summary-stat p {\n          margin: 0 0 5px 0;\n          font-weight: 500;\n          color: #2c3e50;\n        }\n\n        .summary-stat small {\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .grid-4 {\n            grid-template-columns: repeat(2, 1fr);\n          }\n          \n          .grid-2 {\n            grid-template-columns: 1fr;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .grid-4 {\n            grid-template-columns: 1fr;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"DJWPBOPkcx3XZqhiAedDzNGbsCQ=\", false, function () {\n  return [useAuth, useHealthData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useHealthData", "useAuth", "StatsCard", "ActivityChart", "GoalProgress", "HealthInsights", "QuickActions", "GoogleFitIntegration", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_today$steps", "_today$calories", "_goals$active", "_weekly$totalSteps", "user", "dashboardData", "loading", "fetchDashboardData", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "className", "style", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "today", "weekly", "goals", "prediction", "badges", "insights", "name", "gap", "value", "onChange", "e", "target", "width", "title", "steps", "toLocaleString", "icon", "color", "progress", "calories", "activeMinutes", "activityScore", "period", "active", "length", "map", "badge", "index", "description", "totalSteps", "Math", "round", "avgSteps", "totalDistance", "avgSleep", "avgHeartRate", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport StatsCard from '../components/StatsCard';\nimport ActivityChart from '../components/ActivityChart';\nimport GoalProgress from '../components/GoalProgress';\nimport HealthInsights from '../components/HealthInsights';\nimport QuickActions from '../components/QuickActions';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const { dashboardData, loading, fetchDashboardData } = useHealthData();\n  const [selectedPeriod, setSelectedPeriod] = useState('7d');\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  if (loading && !dashboardData) {\n    return (\n      <div className=\"flex-center\" style={{ minHeight: '50vh' }}>\n        <div className=\"loading-spinner\"></div>\n      </div>\n    );\n  }\n\n  const today = dashboardData?.today || {};\n  const weekly = dashboardData?.weekly || {};\n  const goals = dashboardData?.goals || {};\n  const prediction = dashboardData?.prediction;\n  const badges = dashboardData?.badges || [];\n  const insights = dashboardData?.insights || [];\n\n  return (\n    <div className=\"dashboard\">\n      {/* Welcome Section */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div>\n            <h1 className=\"card-title\">\n              Welcome back, {user?.name}! 👋\n            </h1>\n            <p className=\"card-subtitle\">\n              Here's your health overview for today\n            </p>\n          </div>\n          <div className=\"flex\" style={{ gap: '10px' }}>\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"form-select\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"7d\">Last 7 days</option>\n              <option value=\"30d\">Last 30 days</option>\n              <option value=\"90d\">Last 90 days</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Today's Stats */}\n        <div className=\"grid grid-4\">\n          <StatsCard\n            title=\"Steps\"\n            value={today.steps?.toLocaleString() || '0'}\n            target=\"10,000\"\n            icon=\"👟\"\n            color=\"#667eea\"\n            progress={(today.steps / 10000) * 100}\n          />\n          <StatsCard\n            title=\"Calories\"\n            value={today.calories?.toLocaleString() || '0'}\n            target=\"2,000\"\n            icon=\"🔥\"\n            color=\"#e74c3c\"\n            progress={(today.calories / 2000) * 100}\n          />\n          <StatsCard\n            title=\"Active Minutes\"\n            value={today.activeMinutes || '0'}\n            target=\"60\"\n            icon=\"⏱️\"\n            color=\"#f39c12\"\n            progress={(today.activeMinutes / 60) * 100}\n          />\n          <StatsCard\n            title=\"Activity Score\"\n            value={today.activityScore || '0'}\n            target=\"100\"\n            icon=\"⭐\"\n            color=\"#27ae60\"\n            progress={today.activityScore || 0}\n          />\n        </div>\n      </div>\n\n      <div className=\"grid grid-2\">\n        {/* Activity Chart */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Activity Trends</h2>\n          </div>\n          <ActivityChart period={selectedPeriod} />\n        </div>\n\n        {/* Goals Progress */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Active Goals</h2>\n            <span className=\"text-muted\">\n              {goals.active?.length || 0} active goals\n            </span>\n          </div>\n          <GoalProgress goals={goals.active || []} />\n        </div>\n      </div>\n\n      <div className=\"grid grid-2\">\n        {/* Health Insights */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Health Insights</h2>\n          </div>\n          <HealthInsights insights={insights} prediction={prediction} />\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Quick Actions</h2>\n          </div>\n          <QuickActions />\n        </div>\n      </div>\n\n      {/* Achievements */}\n      {badges.length > 0 && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Recent Achievements</h2>\n          </div>\n          <div className=\"achievements-grid\">\n            {badges.map((badge, index) => (\n              <div key={index} className=\"achievement-badge\">\n                <span className=\"badge-icon\">{badge.icon}</span>\n                <div className=\"badge-info\">\n                  <h4>{badge.name}</h4>\n                  <p>{badge.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Weekly Summary */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h2 className=\"card-title\">Weekly Summary</h2>\n        </div>\n        <div className=\"grid grid-4\">\n          <div className=\"summary-stat\">\n            <h3>{weekly.totalSteps?.toLocaleString() || '0'}</h3>\n            <p>Total Steps</p>\n            <small className=\"text-muted\">\n              Avg: {Math.round(weekly.avgSteps || 0).toLocaleString()}/day\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.totalDistance || 0)} km</h3>\n            <p>Distance Covered</p>\n            <small className=\"text-muted\">\n              Avg: {Math.round((weekly.totalDistance || 0) / 7)} km/day\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.avgSleep || 0)} min</h3>\n            <p>Avg Sleep</p>\n            <small className=\"text-muted\">\n              {Math.round((weekly.avgSleep || 0) / 60)} hours/night\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.avgHeartRate || 0)} bpm</h3>\n            <p>Avg Heart Rate</p>\n            <small className=\"text-muted\">\n              Resting rate\n            </small>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .dashboard {\n          padding: 0;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 15px;\n        }\n\n        .achievement-badge {\n          display: flex;\n          align-items: center;\n          padding: 15px;\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.2);\n        }\n\n        .badge-icon {\n          font-size: 2rem;\n          margin-right: 15px;\n        }\n\n        .badge-info h4 {\n          margin: 0 0 5px 0;\n          color: #2c3e50;\n          font-size: 1rem;\n        }\n\n        .badge-info p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .summary-stat {\n          text-align: center;\n          padding: 20px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .summary-stat h3 {\n          margin: 0 0 8px 0;\n          font-size: 1.8rem;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        .summary-stat p {\n          margin: 0 0 5px 0;\n          font-weight: 500;\n          color: #2c3e50;\n        }\n\n        .summary-stat small {\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .grid-4 {\n            grid-template-columns: repeat(2, 1fr);\n          }\n          \n          .grid-2 {\n            grid-template-columns: 1fr;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .grid-4 {\n            grid-template-columns: 1fr;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,kBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEgB,aAAa;IAAEC,OAAO;IAAEC;EAAmB,CAAC,GAAGnB,aAAa,CAAC,CAAC;EACtE,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAE1DD,SAAS,CAAC,MAAM;IACdsB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,IAAI,CAACD,aAAa,EAAE;IAC7B,oBACER,OAAA;MAAKa,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eACxDhB,OAAA;QAAKa,SAAS,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,MAAMC,KAAK,GAAG,CAAAb,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEa,KAAK,KAAI,CAAC,CAAC;EACxC,MAAMC,MAAM,GAAG,CAAAd,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEc,MAAM,KAAI,CAAC,CAAC;EAC1C,MAAMC,KAAK,GAAG,CAAAf,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEe,KAAK,KAAI,CAAC,CAAC;EACxC,MAAMC,UAAU,GAAGhB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgB,UAAU;EAC5C,MAAMC,MAAM,GAAG,CAAAjB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiB,MAAM,KAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAG,CAAAlB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkB,QAAQ,KAAI,EAAE;EAE9C,oBACE1B,OAAA;IAAKa,SAAS,EAAC,WAAW;IAAAG,QAAA,gBAExBhB,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBhB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BhB,OAAA;UAAAgB,QAAA,gBACEhB,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,gBACX,EAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,EAAC,gBAC5B;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAGa,SAAS,EAAC,eAAe;YAAAG,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNpB,OAAA;UAAKa,SAAS,EAAC,MAAM;UAACC,KAAK,EAAE;YAAEc,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,eAC3ChB,OAAA;YACE6B,KAAK,EAAElB,cAAe;YACtBmB,QAAQ,EAAGC,CAAC,IAAKnB,iBAAiB,CAACmB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDhB,SAAS,EAAC,aAAa;YACvBC,KAAK,EAAE;cAAEmB,KAAK,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAEzBhB,OAAA;cAAQ6B,KAAK,EAAC,IAAI;cAAAb,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCpB,OAAA;cAAQ6B,KAAK,EAAC,KAAK;cAAAb,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzCpB,OAAA;cAAQ6B,KAAK,EAAC,KAAK;cAAAb,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BhB,OAAA,CAACP,SAAS;UACRyC,KAAK,EAAC,OAAO;UACbL,KAAK,EAAE,EAAA1B,YAAA,GAAAkB,KAAK,CAACc,KAAK,cAAAhC,YAAA,uBAAXA,YAAA,CAAaiC,cAAc,CAAC,CAAC,KAAI,GAAI;UAC5CJ,MAAM,EAAC,QAAQ;UACfK,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGlB,KAAK,CAACc,KAAK,GAAG,KAAK,GAAI;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACFpB,OAAA,CAACP,SAAS;UACRyC,KAAK,EAAC,UAAU;UAChBL,KAAK,EAAE,EAAAzB,eAAA,GAAAiB,KAAK,CAACmB,QAAQ,cAAApC,eAAA,uBAAdA,eAAA,CAAgBgC,cAAc,CAAC,CAAC,KAAI,GAAI;UAC/CJ,MAAM,EAAC,OAAO;UACdK,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGlB,KAAK,CAACmB,QAAQ,GAAG,IAAI,GAAI;QAAI;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACFpB,OAAA,CAACP,SAAS;UACRyC,KAAK,EAAC,gBAAgB;UACtBL,KAAK,EAAER,KAAK,CAACoB,aAAa,IAAI,GAAI;UAClCT,MAAM,EAAC,IAAI;UACXK,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGlB,KAAK,CAACoB,aAAa,GAAG,EAAE,GAAI;QAAI;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFpB,OAAA,CAACP,SAAS;UACRyC,KAAK,EAAC,gBAAgB;UACtBL,KAAK,EAAER,KAAK,CAACqB,aAAa,IAAI,GAAI;UAClCV,MAAM,EAAC,KAAK;UACZK,IAAI,EAAC,QAAG;UACRC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAElB,KAAK,CAACqB,aAAa,IAAI;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAKa,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAE1BhB,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBhB,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BhB,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNpB,OAAA,CAACN,aAAa;UAACiD,MAAM,EAAEhC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGNpB,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBhB,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1BhB,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CpB,OAAA;YAAMa,SAAS,EAAC,YAAY;YAAAG,QAAA,GACzB,EAAAX,aAAA,GAAAkB,KAAK,CAACqB,MAAM,cAAAvC,aAAA,uBAAZA,aAAA,CAAcwC,MAAM,KAAI,CAAC,EAAC,eAC7B;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpB,OAAA,CAACL,YAAY;UAAC4B,KAAK,EAAEA,KAAK,CAACqB,MAAM,IAAI;QAAG;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAKa,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAE1BhB,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBhB,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BhB,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNpB,OAAA,CAACJ,cAAc;UAAC8B,QAAQ,EAAEA,QAAS;UAACF,UAAU,EAAEA;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAGNpB,OAAA;QAAKa,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBhB,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BhB,OAAA;YAAIa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNpB,OAAA,CAACH,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLK,MAAM,CAACoB,MAAM,GAAG,CAAC,iBAChB7C,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBhB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAG,QAAA,eAC1BhB,OAAA;UAAIa,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNpB,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAG,QAAA,EAC/BS,MAAM,CAACqB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBhD,OAAA;UAAiBa,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAC5ChB,OAAA;YAAMa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAE+B,KAAK,CAACV;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDpB,OAAA;YAAKa,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBhB,OAAA;cAAAgB,QAAA,EAAK+B,KAAK,CAACpB;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBpB,OAAA;cAAAgB,QAAA,EAAI+B,KAAK,CAACE;YAAW;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,GALE4B,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpB,OAAA;MAAKa,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBhB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAG,QAAA,eAC1BhB,OAAA;UAAIa,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACNpB,OAAA;QAAKa,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BhB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BhB,OAAA;YAAAgB,QAAA,EAAK,EAAAV,kBAAA,GAAAgB,MAAM,CAAC4B,UAAU,cAAA5C,kBAAA,uBAAjBA,kBAAA,CAAmB8B,cAAc,CAAC,CAAC,KAAI;UAAG;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrDpB,OAAA;YAAAgB,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClBpB,OAAA;YAAOa,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,OACvB,EAACmC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAAC+B,QAAQ,IAAI,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC,EAAC,MAC1D;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BhB,OAAA;YAAAgB,QAAA,GAAKmC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACgC,aAAa,IAAI,CAAC,CAAC,EAAC,KAAG;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDpB,OAAA;YAAAgB,QAAA,EAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvBpB,OAAA;YAAOa,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,OACvB,EAACmC,IAAI,CAACC,KAAK,CAAC,CAAC9B,MAAM,CAACgC,aAAa,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,SACpD;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BhB,OAAA;YAAAgB,QAAA,GAAKmC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACiC,QAAQ,IAAI,CAAC,CAAC,EAAC,MAAI;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CpB,OAAA;YAAAgB,QAAA,EAAG;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChBpB,OAAA;YAAOa,SAAS,EAAC,YAAY;YAAAG,QAAA,GAC1BmC,IAAI,CAACC,KAAK,CAAC,CAAC9B,MAAM,CAACiC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,cAC3C;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BhB,OAAA;YAAAgB,QAAA,GAAKmC,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAACkC,YAAY,IAAI,CAAC,CAAC,EAAC,MAAI;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDpB,OAAA;YAAAgB,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrBpB,OAAA;YAAOa,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAOyD,GAAG;MAAAzC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClB,EAAA,CAxQID,SAAS;EAAA,QACIT,OAAO,EAC+BD,aAAa;AAAA;AAAAmE,EAAA,GAFhEzD,SAAS;AA0Qf,eAAeA,SAAS;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}