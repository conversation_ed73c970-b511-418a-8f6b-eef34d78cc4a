$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImRlbW8tdXNlci0xIiwiaWF0IjoxNzUxNzE4OTM5LCJleHAiOjE3NTQzMTA5Mzl9.eK6KcYGuEK69iMx0mNO8JPquZDi1byLeJhH5sP4SHkY"

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

$response = Invoke-RestMethod -Uri "http://localhost:5000/api/users/dashboard" -Method GET -Headers $headers

Write-Host "Dashboard Response:"
$response | ConvertTo-Json -Depth 4
