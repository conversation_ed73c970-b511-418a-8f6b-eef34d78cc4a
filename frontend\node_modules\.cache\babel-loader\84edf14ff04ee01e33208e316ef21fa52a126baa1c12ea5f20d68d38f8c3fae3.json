{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      navigate('/');\n    } else {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-center\",\n    style: {\n      minHeight: '100vh',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        maxWidth: '400px',\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"card-title\",\n          style: {\n            fontSize: '2rem',\n            marginBottom: '10px'\n          },\n          children: \"\\uD83C\\uDFE5 HealthConnect\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            className: \"form-input\",\n            value: formData.email,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            className: \"form-input\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary btn-large\",\n          disabled: loading,\n          style: {\n            width: '100%',\n            marginTop: '20px'\n          },\n          children: loading ? 'Signing In...' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"text-primary\",\n            style: {\n              textDecoration: 'none'\n            },\n            children: \"Sign up here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-3\",\n        style: {\n          background: 'rgba(102, 126, 234, 0.1)',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            fontSize: '1rem',\n            marginBottom: '10px',\n            color: '#667eea'\n          },\n          children: \"Demo Credentials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.9rem',\n            margin: '5px 0',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), \" <EMAIL>\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.9rem',\n            margin: '5px 0',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Password:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), \" demo123\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"VqcKZT1mMdmwm2dmTSn9qo+pioY=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "loading", "setLoading", "error", "setError", "login", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "message", "className", "style", "minHeight", "padding", "children", "max<PERSON><PERSON><PERSON>", "width", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "required", "placeholder", "disabled", "marginTop", "to", "textDecoration", "background", "borderRadius", "color", "margin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    const result = await login(formData.email, formData.password);\n    \n    if (result.success) {\n      navigate('/');\n    } else {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  return (\n    <div className=\"flex-center\" style={{ minHeight: '100vh', padding: '20px' }}>\n      <div className=\"card\" style={{ maxWidth: '400px', width: '100%' }}>\n        <div className=\"text-center mb-4\">\n          <h1 className=\"card-title\" style={{ fontSize: '2rem', marginBottom: '10px' }}>\n            🏥 HealthConnect\n          </h1>\n          <p className=\"text-muted\">Sign in to your account</p>\n        </div>\n\n        {error && (\n          <div className=\"alert alert-danger\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label className=\"form-label\">Email Address</label>\n            <input\n              type=\"email\"\n              name=\"email\"\n              className=\"form-input\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"form-label\">Password</label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              className=\"form-input\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your password\"\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary btn-large\"\n            disabled={loading}\n            style={{ width: '100%', marginTop: '20px' }}\n          >\n            {loading ? 'Signing In...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className=\"text-center mt-3\">\n          <p className=\"text-muted\">\n            Don't have an account?{' '}\n            <Link to=\"/register\" className=\"text-primary\" style={{ textDecoration: 'none' }}>\n              Sign up here\n            </Link>\n          </p>\n        </div>\n\n        <div className=\"mt-4 p-3\" style={{ background: 'rgba(102, 126, 234, 0.1)', borderRadius: '8px' }}>\n          <h4 style={{ fontSize: '1rem', marginBottom: '10px', color: '#667eea' }}>\n            Demo Credentials\n          </h4>\n          <p style={{ fontSize: '0.9rem', margin: '5px 0', color: '#666' }}>\n            <strong>Email:</strong> <EMAIL>\n          </p>\n          <p style={{ fontSize: '0.9rem', margin: '5px 0', color: '#666' }}>\n            <strong>Password:</strong> demo123\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEgB;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3B,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BV,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACW,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMU,MAAM,GAAG,MAAMT,KAAK,CAACR,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAE7D,IAAIc,MAAM,CAACC,OAAO,EAAE;MAClBT,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,MAAM;MACLF,QAAQ,CAACU,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAd,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACER,OAAA;IAAKuB,SAAS,EAAC,aAAa;IAACC,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC1E3B,OAAA;MAAKuB,SAAS,EAAC,MAAM;MAACC,KAAK,EAAE;QAAEI,QAAQ,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAChE3B,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAI,QAAA,gBAC/B3B,OAAA;UAAIuB,SAAS,EAAC,YAAY;UAACC,KAAK,EAAE;YAAEM,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAE9E;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnC,OAAA;UAAGuB,SAAS,EAAC,YAAY;UAAAI,QAAA,EAAC;QAAuB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EAEL1B,KAAK,iBACJT,OAAA;QAAKuB,SAAS,EAAC,oBAAoB;QAAAI,QAAA,EAChClB;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDnC,OAAA;QAAMoC,QAAQ,EAAElB,YAAa;QAAAS,QAAA,gBAC3B3B,OAAA;UAAKuB,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzB3B,OAAA;YAAOuB,SAAS,EAAC,YAAY;YAAAI,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDnC,OAAA;YACEqC,IAAI,EAAC,OAAO;YACZrB,IAAI,EAAC,OAAO;YACZO,SAAS,EAAC,YAAY;YACtBN,KAAK,EAAEd,QAAQ,CAACE,KAAM;YACtBiC,QAAQ,EAAEzB,YAAa;YACvB0B,QAAQ;YACRC,WAAW,EAAC;UAAkB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnC,OAAA;UAAKuB,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzB3B,OAAA;YAAOuB,SAAS,EAAC,YAAY;YAAAI,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CnC,OAAA;YACEqC,IAAI,EAAC,UAAU;YACfrB,IAAI,EAAC,UAAU;YACfO,SAAS,EAAC,YAAY;YACtBN,KAAK,EAAEd,QAAQ,CAACG,QAAS;YACzBgC,QAAQ,EAAEzB,YAAa;YACvB0B,QAAQ;YACRC,WAAW,EAAC;UAAqB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnC,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACbd,SAAS,EAAC,2BAA2B;UACrCkB,QAAQ,EAAElC,OAAQ;UAClBiB,KAAK,EAAE;YAAEK,KAAK,EAAE,MAAM;YAAEa,SAAS,EAAE;UAAO,CAAE;UAAAf,QAAA,EAE3CpB,OAAO,GAAG,eAAe,GAAG;QAAS;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPnC,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAI,QAAA,eAC/B3B,OAAA;UAAGuB,SAAS,EAAC,YAAY;UAAAI,QAAA,GAAC,wBACF,EAAC,GAAG,eAC1B3B,OAAA,CAACJ,IAAI;YAAC+C,EAAE,EAAC,WAAW;YAACpB,SAAS,EAAC,cAAc;YAACC,KAAK,EAAE;cAAEoB,cAAc,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAEjF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnC,OAAA;QAAKuB,SAAS,EAAC,UAAU;QAACC,KAAK,EAAE;UAAEqB,UAAU,EAAE,0BAA0B;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAnB,QAAA,gBAC/F3B,OAAA;UAAIwB,KAAK,EAAE;YAAEM,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEgB,KAAK,EAAE;UAAU,CAAE;UAAApB,QAAA,EAAC;QAEzE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnC,OAAA;UAAGwB,KAAK,EAAE;YAAEM,QAAQ,EAAE,QAAQ;YAAEkB,MAAM,EAAE,OAAO;YAAED,KAAK,EAAE;UAAO,CAAE;UAAApB,QAAA,gBAC/D3B,OAAA;YAAA2B,QAAA,EAAQ;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,2BACzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJnC,OAAA;UAAGwB,KAAK,EAAE;YAAEM,QAAQ,EAAE,QAAQ;YAAEkB,MAAM,EAAE,OAAO;YAAED,KAAK,EAAE;UAAO,CAAE;UAAApB,QAAA,gBAC/D3B,OAAA;YAAA2B,QAAA,EAAQ;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,YAC5B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA9GID,KAAK;EAAA,QAQSH,OAAO,EACRD,WAAW;AAAA;AAAAoD,EAAA,GATxBhD,KAAK;AAgHX,eAAeA,KAAK;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}