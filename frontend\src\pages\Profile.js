import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import GoogleFitIntegration from '../components/GoogleFitIntegration';

const Profile = () => {
  const { user, updateProfile, changePassword } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    height: user?.height || '',
    weight: user?.weight || '',
    activityLevel: user?.activityLevel || 'moderately_active'
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [message, setMessage] = useState('');

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    const result = await updateProfile(profileData);
    setMessage(result.success ? 'Profile updated successfully!' : result.message);
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage('New passwords do not match');
      return;
    }
    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);
    setMessage(result.success ? 'Password changed successfully!' : result.message);
    if (result.success) {
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
    }
  };

  return (
    <div className="profile-page">
      <div className="profile-container">
        <div className="profile-header">
          <div className="user-avatar">
            {user?.name?.charAt(0).toUpperCase() || '👤'}
          </div>
          <div className="user-info">
            <h1>{user?.name}</h1>
            <p>{user?.email}</p>
            <div className="user-stats">
              <span>Age: {user?.age || 'N/A'}</span>
              <span>BMI: {user?.bmi || 'N/A'}</span>
              <span>Activity: {user?.activityLevel?.replace('_', ' ') || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className="profile-tabs">
          <button 
            className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            Profile Settings
          </button>
          <button
            className={`tab-button ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => setActiveTab('password')}
          >
            Change Password
          </button>
          <button
            className={`tab-button ${activeTab === 'integrations' ? 'active' : ''}`}
            onClick={() => setActiveTab('integrations')}
          >
            🏃‍♂️ Device Integration
          </button>
        </div>

        {message && (
          <div className={`alert ${message.includes('success') ? 'alert-success' : 'alert-danger'}`}>
            {message}
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="tab-content">
            <h2>Profile Information</h2>
            <form onSubmit={handleProfileUpdate}>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">Full Name</label>
                  <input
                    type="text"
                    className="form-input"
                    value={profileData.name}
                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                    required
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    value={user?.email || ''}
                    disabled
                    style={{ background: '#f8f9fa', color: '#6c757d' }}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Height (cm)</label>
                  <input
                    type="number"
                    className="form-input"
                    value={profileData.height}
                    onChange={(e) => setProfileData({...profileData, height: e.target.value})}
                    min="50"
                    max="300"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Weight (kg)</label>
                  <input
                    type="number"
                    className="form-input"
                    value={profileData.weight}
                    onChange={(e) => setProfileData({...profileData, weight: e.target.value})}
                    min="20"
                    max="500"
                  />
                </div>
              </div>
              <div className="form-group">
                <label className="form-label">Activity Level</label>
                <select
                  className="form-select"
                  value={profileData.activityLevel}
                  onChange={(e) => setProfileData({...profileData, activityLevel: e.target.value})}
                >
                  <option value="sedentary">Sedentary</option>
                  <option value="lightly_active">Lightly Active</option>
                  <option value="moderately_active">Moderately Active</option>
                  <option value="very_active">Very Active</option>
                  <option value="extremely_active">Extremely Active</option>
                </select>
              </div>
              <button type="submit" className="btn btn-primary">
                Update Profile
              </button>
            </form>
          </div>
        )}

        {activeTab === 'password' && (
          <div className="tab-content">
            <h2>Change Password</h2>
            <form onSubmit={handlePasswordChange}>
              <div className="form-group">
                <label className="form-label">Current Password</label>
                <input
                  type="password"
                  className="form-input"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label className="form-label">New Password</label>
                <input
                  type="password"
                  className="form-input"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                  required
                  minLength="6"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Confirm New Password</label>
                <input
                  type="password"
                  className="form-input"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                  required
                  minLength="6"
                />
              </div>
              <button type="submit" className="btn btn-primary">
                Change Password
              </button>
            </form>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div className="tab-content">
            <GoogleFitIntegration />

            <div className="integration-info" style={{ marginTop: '30px' }}>
              <h3>📱 Supported Devices</h3>
              <div className="device-grid">
                <div className="device-card">
                  <h4>🏃‍♂️ Google Fit</h4>
                  <p>Sync data from any device connected to Google Fit</p>
                  <ul>
                    <li>Android Wear</li>
                    <li>Fitbit</li>
                    <li>Garmin</li>
                    <li>Samsung Health</li>
                  </ul>
                </div>
                <div className="device-card coming-soon">
                  <h4>⌚ Apple Health</h4>
                  <p>Direct integration with Apple HealthKit</p>
                  <span className="badge">Coming Soon</span>
                </div>
                <div className="device-card coming-soon">
                  <h4>📊 Fitbit Direct</h4>
                  <p>Direct Fitbit API integration</p>
                  <span className="badge">Coming Soon</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .profile-page { padding: 0; }
        .profile-container { max-width: 800px; margin: 0 auto; }
        .profile-header { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; margin-bottom: 30px; display: flex; align-items: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
        .user-avatar { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700; margin-right: 25px; }
        .user-info h1 { margin: 0 0 5px 0; color: #2c3e50; }
        .user-info p { margin: 0 0 10px 0; color: #7f8c8d; }
        .user-stats { display: flex; gap: 20px; font-size: 0.9rem; color: #667eea; }
        .profile-tabs { display: flex; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 5px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
        .tab-button { flex: 1; padding: 15px 20px; border: none; background: none; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; color: #7f8c8d; }
        .tab-button.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .tab-content { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
        .tab-content h2 { margin: 0 0 25px 0; color: #2c3e50; }
        .device-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-top: 20px;
        }

        .device-card {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 12px;
          padding: 20px;
          position: relative;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .device-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .device-card.coming-soon {
          opacity: 0.6;
          background: #f8f9fa;
        }

        .device-card h4 {
          margin: 0 0 10px 0;
          color: #333;
        }

        .device-card p {
          color: #666;
          margin: 0 0 15px 0;
          font-size: 0.9rem;
        }

        .device-card ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .device-card li {
          padding: 4px 0;
          color: #555;
          font-size: 0.85rem;
        }

        .device-card li:before {
          content: "✓ ";
          color: #28a745;
          font-weight: bold;
        }

        .badge {
          position: absolute;
          top: 10px;
          right: 10px;
          background: #ffc107;
          color: #333;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .integration-info {
          background: #f8f9fa;
          border-radius: 12px;
          padding: 25px;
          border: 1px solid #e9ecef;
        }

        .integration-info h3 {
          margin: 0 0 20px 0;
          color: #333;
        }

        @media (max-width: 768px) {
          .profile-header { flex-direction: column; text-align: center; }
          .user-avatar { margin-right: 0; margin-bottom: 20px; }
          .user-stats { justify-content: center; flex-wrap: wrap; }
          .profile-tabs { flex-direction: column; }
          .device-grid { grid-template-columns: 1fr; }
        }
      `}</style>
    </div>
  );
};

export default Profile;
