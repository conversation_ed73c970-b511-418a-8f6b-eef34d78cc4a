/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { storagebatchoperations_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof storagebatchoperations_v1.Storagebatchoperations;
};
export declare function storagebatchoperations(version: 'v1'): storagebatchoperations_v1.Storagebatchoperations;
export declare function storagebatchoperations(options: storagebatchoperations_v1.Options): storagebatchoperations_v1.Storagebatchoperations;
declare const auth: AuthPlus;
export { auth };
export { storagebatchoperations_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
