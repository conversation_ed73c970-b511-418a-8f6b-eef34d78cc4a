/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { servicemanagement_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof servicemanagement_v1.Servicemanagement;
};
export declare function servicemanagement(version: 'v1'): servicemanagement_v1.Servicemanagement;
export declare function servicemanagement(options: servicemanagement_v1.Options): servicemanagement_v1.Servicemanagement;
declare const auth: AuthPlus;
export { auth };
export { servicemanagement_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
