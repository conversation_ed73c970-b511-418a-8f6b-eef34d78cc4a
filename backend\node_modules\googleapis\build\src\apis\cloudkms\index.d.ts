/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { cloudkms_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof cloudkms_v1.Cloudkms;
};
export declare function cloudkms(version: 'v1'): cloudkms_v1.Cloudkms;
export declare function cloudkms(options: cloudkms_v1.Options): cloudkms_v1.Cloudkms;
declare const auth: AuthPlus;
export { auth };
export { cloudkms_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
