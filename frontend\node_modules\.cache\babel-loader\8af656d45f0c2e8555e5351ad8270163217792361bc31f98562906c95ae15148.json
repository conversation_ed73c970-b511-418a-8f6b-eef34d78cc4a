{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\StatsCard.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = ({\n  title,\n  value,\n  target,\n  icon,\n  color,\n  progress = 0\n}) => {\n  const progressPercentage = Math.min(progress, 100);\n  const isComplete = progress >= 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stats-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"stats-icon\",\n        style: {\n          color\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"stats-value\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"stats-title\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-progress\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-fill\",\n          style: {\n            width: `${progressPercentage}%`,\n            backgroundColor: color\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `progress-text ${isComplete ? 'complete' : ''}`,\n          children: [Math.round(progressPercentage), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), target && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"progress-target\",\n          children: [\"Target: \", target]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .stats-card {\n          background: rgba(255, 255, 255, 0.9);\n          border-radius: 12px;\n          padding: 20px;\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          transition: all 0.3s ease;\n        }\n\n        .stats-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n        }\n\n        .stats-header {\n          display: flex;\n          align-items: center;\n          margin-bottom: 15px;\n        }\n\n        .stats-icon {\n          font-size: 2rem;\n          margin-right: 12px;\n        }\n\n        .stats-info {\n          flex: 1;\n        }\n\n        .stats-value {\n          font-size: 1.8rem;\n          font-weight: 700;\n          margin: 0 0 4px 0;\n          color: #2c3e50;\n        }\n\n        .stats-title {\n          font-size: 0.9rem;\n          color: #7f8c8d;\n          margin: 0;\n          font-weight: 500;\n        }\n\n        .stats-progress {\n          margin-top: 15px;\n        }\n\n        .progress-bar {\n          width: 100%;\n          height: 6px;\n          background: rgba(0, 0, 0, 0.1);\n          border-radius: 3px;\n          overflow: hidden;\n          margin-bottom: 8px;\n        }\n\n        .progress-fill {\n          height: 100%;\n          border-radius: 3px;\n          transition: width 0.6s ease;\n          background: linear-gradient(90deg, currentColor 0%, currentColor 100%);\n        }\n\n        .progress-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .progress-text {\n          font-size: 0.85rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .progress-text.complete {\n          color: #27ae60;\n        }\n\n        .progress-target {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n        }\n\n        @media (max-width: 480px) {\n          .stats-card {\n            padding: 15px;\n          }\n\n          .stats-value {\n            font-size: 1.5rem;\n          }\n\n          .stats-icon {\n            font-size: 1.5rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsCard;\nexport default StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "StatsCard", "title", "value", "target", "icon", "color", "progress", "progressPercentage", "Math", "min", "isComplete", "className", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "backgroundColor", "round", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/StatsCard.js"], "sourcesContent": ["import React from 'react';\n\nconst StatsCard = ({ title, value, target, icon, color, progress = 0 }) => {\n  const progressPercentage = Math.min(progress, 100);\n  const isComplete = progress >= 100;\n\n  return (\n    <div className=\"stats-card\">\n      <div className=\"stats-header\">\n        <span className=\"stats-icon\" style={{ color }}>\n          {icon}\n        </span>\n        <div className=\"stats-info\">\n          <h3 className=\"stats-value\">{value}</h3>\n          <p className=\"stats-title\">{title}</p>\n        </div>\n      </div>\n      \n      <div className=\"stats-progress\">\n        <div className=\"progress-bar\">\n          <div \n            className=\"progress-fill\"\n            style={{ \n              width: `${progressPercentage}%`,\n              backgroundColor: color\n            }}\n          />\n        </div>\n        <div className=\"progress-info\">\n          <span className={`progress-text ${isComplete ? 'complete' : ''}`}>\n            {Math.round(progressPercentage)}%\n          </span>\n          {target && (\n            <span className=\"progress-target\">\n              Target: {target}\n            </span>\n          )}\n        </div>\n      </div>\n\n      <style jsx>{`\n        .stats-card {\n          background: rgba(255, 255, 255, 0.9);\n          border-radius: 12px;\n          padding: 20px;\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          transition: all 0.3s ease;\n        }\n\n        .stats-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n        }\n\n        .stats-header {\n          display: flex;\n          align-items: center;\n          margin-bottom: 15px;\n        }\n\n        .stats-icon {\n          font-size: 2rem;\n          margin-right: 12px;\n        }\n\n        .stats-info {\n          flex: 1;\n        }\n\n        .stats-value {\n          font-size: 1.8rem;\n          font-weight: 700;\n          margin: 0 0 4px 0;\n          color: #2c3e50;\n        }\n\n        .stats-title {\n          font-size: 0.9rem;\n          color: #7f8c8d;\n          margin: 0;\n          font-weight: 500;\n        }\n\n        .stats-progress {\n          margin-top: 15px;\n        }\n\n        .progress-bar {\n          width: 100%;\n          height: 6px;\n          background: rgba(0, 0, 0, 0.1);\n          border-radius: 3px;\n          overflow: hidden;\n          margin-bottom: 8px;\n        }\n\n        .progress-fill {\n          height: 100%;\n          border-radius: 3px;\n          transition: width 0.6s ease;\n          background: linear-gradient(90deg, currentColor 0%, currentColor 100%);\n        }\n\n        .progress-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .progress-text {\n          font-size: 0.85rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .progress-text.complete {\n          color: #27ae60;\n        }\n\n        .progress-target {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n        }\n\n        @media (max-width: 480px) {\n          .stats-card {\n            padding: 15px;\n          }\n\n          .stats-value {\n            font-size: 1.5rem;\n          }\n\n          .stats-icon {\n            font-size: 1.5rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC,IAAI;EAAEC,KAAK;EAAEC,QAAQ,GAAG;AAAE,CAAC,KAAK;EACzE,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACH,QAAQ,EAAE,GAAG,CAAC;EAClD,MAAMI,UAAU,GAAGJ,QAAQ,IAAI,GAAG;EAElC,oBACEP,OAAA;IAAKY,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBb,OAAA;MAAKY,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3Bb,OAAA;QAAMY,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE;UAAER;QAAM,CAAE;QAAAO,QAAA,EAC3CR;MAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACPlB,OAAA;QAAKY,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBb,OAAA;UAAIY,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEV;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxClB,OAAA;UAAGY,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEX;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA;MAAKY,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7Bb,OAAA;QAAKY,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3Bb,OAAA;UACEY,SAAS,EAAC,eAAe;UACzBE,KAAK,EAAE;YACLK,KAAK,EAAE,GAAGX,kBAAkB,GAAG;YAC/BY,eAAe,EAAEd;UACnB;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5Bb,OAAA;UAAMY,SAAS,EAAE,iBAAiBD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAAAE,QAAA,GAC9DJ,IAAI,CAACY,KAAK,CAACb,kBAAkB,CAAC,EAAC,GAClC;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACNd,MAAM,iBACLJ,OAAA;UAAMY,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACxB,EAACT,MAAM;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA;MAAOsB,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACK,EAAA,GA1IItB,SAAS;AA4If,eAAeA,SAAS;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}