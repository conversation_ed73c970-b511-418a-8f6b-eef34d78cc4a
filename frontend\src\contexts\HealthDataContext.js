import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

const HealthDataContext = createContext();

export const useHealthData = () => {
  const context = useContext(HealthDataContext);
  if (!context) {
    throw new Error('useHealthData must be used within a HealthDataProvider');
  }
  return context;
};

export const HealthDataProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [healthData, setHealthData] = useState([]);
  const [goals, setGoals] = useState([]);
  const [predictions, setPredictions] = useState([]);
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    if (!isAuthenticated) return;
    
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/users/dashboard');
      setDashboardData(response.data.data);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch health data
  const fetchHealthData = async (params = {}) => {
    if (!isAuthenticated) return;
    
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/health-data', { params });
      setHealthData(response.data.data.healthData);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch health data:', error);
      return { healthData: [], pagination: {} };
    } finally {
      setLoading(false);
    }
  };

  // Add health data
  const addHealthData = async (data) => {
    try {
      const response = await axios.post('http://localhost:5000/api/health-data', data);
      
      // Update local state
      const newData = response.data.data.healthData;
      setHealthData(prev => [newData, ...prev.filter(item => 
        new Date(item.date).toDateString() !== new Date(newData.date).toDateString()
      )]);
      
      // Refresh dashboard
      fetchDashboardData();
      
      return { success: true, data: newData };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to add health data';
      return { success: false, message };
    }
  };

  // Update health data
  const updateHealthData = async (id, data) => {
    try {
      const response = await axios.put(`http://localhost:5000/api/health-data/${id}`, data);
      
      // Update local state
      const updatedData = response.data.data.healthData;
      setHealthData(prev => prev.map(item => 
        item._id === id ? updatedData : item
      ));
      
      return { success: true, data: updatedData };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update health data';
      return { success: false, message };
    }
  };

  // Delete health data
  const deleteHealthData = async (id) => {
    try {
      await axios.delete(`http://localhost:5000/api/health-data/${id}`);
      
      // Update local state
      setHealthData(prev => prev.filter(item => item._id !== id));
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to delete health data';
      return { success: false, message };
    }
  };

  // Fetch goals
  const fetchGoals = async (params = {}) => {
    if (!isAuthenticated) return;
    
    try {
      const response = await axios.get('http://localhost:5000/api/goals', { params });
      setGoals(response.data.data.goals);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch goals:', error);
      return { goals: [], pagination: {} };
    }
  };

  // Add goal
  const addGoal = async (goalData) => {
    try {
      const response = await axios.post('http://localhost:5000/api/goals', goalData);
      
      const newGoal = response.data.data.goal;
      setGoals(prev => [newGoal, ...prev]);
      
      return { success: true, data: newGoal };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to add goal';
      return { success: false, message };
    }
  };

  // Update goal progress
  const updateGoalProgress = async (id, value, note = '') => {
    try {
      const response = await axios.put(`http://localhost:5000/api/goals/${id}/progress`, {
        value,
        note
      });
      
      const updatedGoal = response.data.data.goal;
      setGoals(prev => prev.map(goal => 
        goal._id === id ? updatedGoal : goal
      ));
      
      return { success: true, data: updatedGoal };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update goal progress';
      return { success: false, message };
    }
  };

  // Generate prediction
  const generatePrediction = async (predictionType = 'comprehensive') => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:5000/api/predictions/generate', {
        predictionType
      });
      
      const newPrediction = response.data.data.prediction;
      setPredictions(prev => [newPrediction, ...prev]);
      
      return { success: true, data: newPrediction };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to generate prediction';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // Fetch predictions
  const fetchPredictions = async (params = {}) => {
    if (!isAuthenticated) return;
    
    try {
      const response = await axios.get('http://localhost:5000/api/predictions', { params });
      setPredictions(response.data.data.predictions);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch predictions:', error);
      return { predictions: [], pagination: {} };
    }
  };

  // Get activity summary
  const getActivitySummary = async (period = '7d') => {
    try {
      const response = await axios.get('http://localhost:5000/api/users/activity-summary', {
        params: { period }
      });
      return { success: true, data: response.data.data };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to get activity summary';
      return { success: false, message };
    }
  };

  // Chat with bot
  const chatWithBot = async (message) => {
    try {
      const response = await axios.post('http://localhost:5000/api/chatbot/chat', {
        message
      });
      return { success: true, data: response.data.data };
    } catch (error) {
      const message = error.response?.data?.message || 'Chat failed';
      return { success: false, message };
    }
  };

  // Load initial data when user logs in
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchDashboardData();
      fetchHealthData({ limit: 10 });
      fetchGoals({ status: 'active', limit: 5 });
      fetchPredictions({ limit: 5 });
    }
  }, [isAuthenticated, user]);

  const value = {
    // Data
    healthData,
    goals,
    predictions,
    dashboardData,
    loading,
    
    // Health Data methods
    fetchHealthData,
    addHealthData,
    updateHealthData,
    deleteHealthData,
    
    // Goals methods
    fetchGoals,
    addGoal,
    updateGoalProgress,
    
    // Predictions methods
    fetchPredictions,
    generatePrediction,
    
    // Dashboard methods
    fetchDashboardData,
    getActivitySummary,
    
    // Chatbot methods
    chatWithBot
  };

  return (
    <HealthDataContext.Provider value={value}>
      {children}
    </HealthDataContext.Provider>
  );
};
