import React, { useState, useEffect } from 'react';
import axios from 'axios';

const ApiTest = () => {
  const [testResults, setTestResults] = useState({
    health: null,
    login: null,
    dashboard: null
  });
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results = { health: null, login: null, dashboard: null };

    try {
      // Test 1: Health Check
      console.log('Testing health endpoint...');
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      results.health = { success: true, data: healthResponse.data };
      console.log('Health test passed:', healthResponse.data);

      // Test 2: Login
      console.log('Testing login endpoint...');
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        email: '<EMAIL>',
        password: 'demo123'
      });
      results.login = { success: true, data: loginResponse.data };
      console.log('Login test passed:', loginResponse.data);

      // Test 3: Dashboard (if login succeeded)
      if (results.login.success) {
        console.log('Testing dashboard endpoint...');
        const dashboardResponse = await axios.get('http://localhost:5000/api/users/dashboard', {
          headers: {
            'Authorization': `Bearer ${results.login.data.data.token}`
          }
        });
        results.dashboard = { success: true, data: dashboardResponse.data };
        console.log('Dashboard test passed:', dashboardResponse.data);
      }

    } catch (error) {
      console.error('API test error:', error);
      if (!results.health) {
        results.health = { success: false, error: error.message };
      } else if (!results.login) {
        results.login = { success: false, error: error.message };
      } else {
        results.dashboard = { success: false, error: error.message };
      }
    }

    setTestResults(results);
    setLoading(false);
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🔧 API Connection Test</h2>
      
      <button 
        onClick={runTests} 
        disabled={loading}
        style={{
          padding: '10px 20px',
          backgroundColor: '#667eea',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {loading ? 'Testing...' : 'Run Tests Again'}
      </button>

      <div style={{ display: 'grid', gap: '15px' }}>
        {/* Health Test */}
        <div style={{
          padding: '15px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: testResults.health?.success ? '#d4edda' : testResults.health ? '#f8d7da' : '#f8f9fa'
        }}>
          <h3>1. Health Check</h3>
          {testResults.health ? (
            testResults.health.success ? (
              <div>
                <p>✅ <strong>Success!</strong> Backend is reachable</p>
                <p>Status: {testResults.health.data.status}</p>
                <p>Message: {testResults.health.data.message}</p>
              </div>
            ) : (
              <div>
                <p>❌ <strong>Failed!</strong></p>
                <p>Error: {testResults.health.error}</p>
              </div>
            )
          ) : (
            <p>⏳ Testing...</p>
          )}
        </div>

        {/* Login Test */}
        <div style={{
          padding: '15px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: testResults.login?.success ? '#d4edda' : testResults.login ? '#f8d7da' : '#f8f9fa'
        }}>
          <h3>2. Login Test</h3>
          {testResults.login ? (
            testResults.login.success ? (
              <div>
                <p>✅ <strong>Success!</strong> Authentication working</p>
                <p>User: {testResults.login.data.data.user.name}</p>
                <p>Email: {testResults.login.data.data.user.email}</p>
                <p>Token: {testResults.login.data.data.token.substring(0, 20)}...</p>
              </div>
            ) : (
              <div>
                <p>❌ <strong>Failed!</strong></p>
                <p>Error: {testResults.login.error}</p>
              </div>
            )
          ) : (
            <p>⏳ Waiting for health check...</p>
          )}
        </div>

        {/* Dashboard Test */}
        <div style={{
          padding: '15px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: testResults.dashboard?.success ? '#d4edda' : testResults.dashboard ? '#f8d7da' : '#f8f9fa'
        }}>
          <h3>3. Dashboard Test</h3>
          {testResults.dashboard ? (
            testResults.dashboard.success ? (
              <div>
                <p>✅ <strong>Success!</strong> Protected routes working</p>
                <p>Today's Steps: {testResults.dashboard.data.data.today.steps}</p>
                <p>Today's Calories: {testResults.dashboard.data.data.today.calories}</p>
                <p>Active Goals: {testResults.dashboard.data.data.goals.active.length}</p>
              </div>
            ) : (
              <div>
                <p>❌ <strong>Failed!</strong></p>
                <p>Error: {testResults.dashboard.error}</p>
              </div>
            )
          ) : (
            <p>⏳ Waiting for login...</p>
          )}
        </div>
      </div>

      {testResults.health?.success && testResults.login?.success && testResults.dashboard?.success && (
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#d1ecf1',
          border: '1px solid #bee5eb',
          borderRadius: '8px'
        }}>
          <h3>🎉 All Tests Passed!</h3>
          <p>The backend API is working correctly. If login still doesn't work in the main app, the issue is likely in the React Router navigation or authentication context.</p>
          <p><strong>Next step:</strong> Try logging in with the main login form and check the browser console for any errors.</p>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
