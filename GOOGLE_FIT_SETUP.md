# 🏃‍♂️ Google Fit API Integration Setup Guide

This guide will help you set up Google Fit API integration to automatically sync health data from your smartwatch to HealthConnect.

## 📋 Prerequisites

1. **Google Account** with access to Google Cloud Console
2. **Smartwatch or fitness tracker** that syncs with Google Fit
3. **Google Fit app** installed on your phone

## 🔧 Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Name your project (e.g., "HealthConnect Integration")
4. Note down your **Project ID**

## 🔑 Step 2: Enable Google Fit API

1. In Google Cloud Console, go to **APIs & Services > Library**
2. Search for "Fitness API"
3. Click on "Fitness API" and click **"Enable"**

## 🛡️ Step 3: Create OAuth 2.0 Credentials

1. Go to **APIs & Services > Credentials**
2. Click **"Create Credentials" > "OAuth 2.0 Client IDs"**
3. If prompted, configure the OAuth consent screen:
   - Choose **"External"** user type
   - Fill in required fields:
     - App name: "HealthConnect"
     - User support email: Your email
     - Developer contact: Your email
   - Add scopes:
     - `https://www.googleapis.com/auth/fitness.activity.read`
     - `https://www.googleapis.com/auth/fitness.heart_rate.read`
     - `https://www.googleapis.com/auth/fitness.sleep.read`
     - `https://www.googleapis.com/auth/fitness.body.read`
     - `https://www.googleapis.com/auth/fitness.location.read`

4. Create OAuth 2.0 Client ID:
   - Application type: **"Web application"**
   - Name: "HealthConnect Web Client"
   - Authorized redirect URIs:
     - `http://localhost:5000/api/google-fit/callback`
     - `http://localhost:3000/google-fit/callback` (for frontend)

5. **Save the Client ID and Client Secret** - you'll need these!

## ⚙️ Step 4: Configure Environment Variables

Update your `backend/.env` file with your Google credentials:

```env
# Google Fit API Configuration
GOOGLE_CLIENT_ID=your_actual_client_id_here
GOOGLE_CLIENT_SECRET=your_actual_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:5000/api/google-fit/callback
```

## 📱 Step 5: Set Up Your Smartwatch

### For Android Watches:
1. Install **Google Fit** app on your phone
2. Open Google Fit and sign in with your Google account
3. Connect your smartwatch to Google Fit
4. Enable data sharing for all health metrics

### For Apple Watch:
1. Install **Google Fit** app on your iPhone
2. Open Google Fit and sign in
3. Go to Settings > Connected apps
4. Connect Apple Health to Google Fit
5. Enable all health data categories

### For Fitbit:
1. Install **Google Fit** app
2. In Google Fit, go to Settings > Connected apps
3. Connect Fitbit account
4. Authorize data sharing

### For Garmin:
1. Install **Google Fit** app
2. In Google Fit, go to Settings > Connected apps
3. Connect Garmin Connect
4. Enable data synchronization

## 🔄 Step 6: Test the Integration

1. Start your HealthConnect backend server:
   ```bash
   cd backend
   npm run dev
   ```

2. Start your frontend:
   ```bash
   cd frontend
   npm start
   ```

3. Go to your HealthConnect app
4. Navigate to Settings or Health Data page
5. Look for "Google Fit Integration" section
6. Click "Connect Google Fit"
7. Complete the authorization process
8. Click "Sync Data" to pull your health data

## 📊 Available Data Types

The integration will automatically sync:

| Data Type | Description | Source |
|-----------|-------------|---------|
| **Steps** | Daily step count | Smartwatch/Phone |
| **Heart Rate** | Resting, average, max HR | Smartwatch |
| **Calories** | Calories burned | Calculated |
| **Distance** | Distance traveled | GPS/Steps |
| **Active Minutes** | Exercise time | Activity detection |
| **Sleep** | Sleep duration & quality | Sleep tracking |
| **Weight** | Body weight | Smart scale/Manual |
| **Blood Pressure** | Systolic/Diastolic | BP monitor |
| **Blood Oxygen** | SpO2 levels | Pulse oximeter |

## 🔒 Privacy & Security

- **Data Encryption**: All data is encrypted in transit and at rest
- **Limited Access**: Only requested health metrics are accessed
- **User Control**: You can disconnect anytime
- **No Data Sharing**: Your data is never shared with third parties
- **Local Storage**: Data is stored securely in your HealthConnect database

## 🚨 Troubleshooting

### Common Issues:

**1. "Invalid Client ID" Error**
- Verify your `GOOGLE_CLIENT_ID` in `.env` file
- Ensure the Client ID is from a Web application type

**2. "Redirect URI Mismatch"**
- Check that redirect URI in Google Console matches your `.env` file
- Ensure you're using the correct port (5000 for backend)

**3. "Insufficient Permissions"**
- Verify all required scopes are added in OAuth consent screen
- Re-authorize the application

**4. "No Data Synced"**
- Ensure your smartwatch is syncing with Google Fit
- Check that data sharing is enabled in Google Fit app
- Try syncing manually in Google Fit first

**5. "Token Expired"**
- The integration will automatically refresh tokens
- If issues persist, disconnect and reconnect

### Debug Steps:

1. Check backend logs for detailed error messages
2. Verify Google Fit app has recent data
3. Test API endpoints directly using Postman
4. Check network connectivity and firewall settings

## 📈 Data Analysis Features

Once connected, HealthConnect will:

1. **Automatically sync** data daily
2. **Analyze trends** in your health metrics
3. **Predict health risks** using AI/ML models
4. **Provide personalized recommendations**
5. **Track goal progress** automatically
6. **Generate health reports**

## 🔄 Sync Schedule

- **Manual Sync**: Click "Sync Data" anytime
- **Automatic Sync**: Every 6 hours (configurable)
- **Real-time**: Some metrics update in near real-time
- **Historical Data**: Can sync up to 1 year of historical data

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review backend console logs
3. Verify Google Fit app is working correctly
4. Check Google Cloud Console for API quotas/errors

## 🎯 Next Steps

After successful integration:

1. **Set Health Goals**: Use the Goals page to set targets
2. **Review Predictions**: Check AI health risk assessments
3. **Monitor Trends**: Use the dashboard for insights
4. **Chat with AI**: Ask health questions to the chatbot
5. **Export Data**: Download your health reports

---

**🎉 Congratulations!** Your smartwatch data is now automatically flowing into HealthConnect for comprehensive health analysis and risk prediction.
