{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading,\n    user,\n    token\n  } = useAuth();\n  console.log('🛡️ ProtectedRoute check:', {\n    isAuthenticated,\n    loading,\n    hasUser: !!user,\n    hasToken: !!token,\n    userName: user === null || user === void 0 ? void 0 : user.name\n  });\n  if (loading) {\n    console.log('⏳ ProtectedRoute: Still loading, showing spinner');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-center\",\n      style: {\n        minHeight: '50vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginTop: '20px',\n          color: '#666'\n        },\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    console.log('❌ ProtectedRoute: Not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('✅ ProtectedRoute: Authenticated, rendering children');\n  return children;\n};\n_s(ProtectedRoute, \"CvvfGIz0nGOwYtilufgGxKK04/E=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "user", "token", "console", "log", "<PERSON><PERSON>ser", "hasToken", "userName", "name", "className", "style", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "color", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated, loading, user, token } = useAuth();\n\n  console.log('🛡️ ProtectedRoute check:', {\n    isAuthenticated,\n    loading,\n    hasUser: !!user,\n    hasToken: !!token,\n    userName: user?.name\n  });\n\n  if (loading) {\n    console.log('⏳ ProtectedRoute: Still loading, showing spinner');\n    return (\n      <div className=\"flex-center\" style={{ minHeight: '50vh' }}>\n        <div className=\"loading-spinner\"></div>\n        <p style={{ marginTop: '20px', color: '#666' }}>Loading...</p>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    console.log('❌ ProtectedRoute: Not authenticated, redirecting to login');\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  console.log('✅ ProtectedRoute: Authenticated, rendering children');\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGT,OAAO,CAAC,CAAC;EAE3DU,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IACvCL,eAAe;IACfC,OAAO;IACPK,OAAO,EAAE,CAAC,CAACJ,IAAI;IACfK,QAAQ,EAAE,CAAC,CAACJ,KAAK;IACjBK,QAAQ,EAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;EAClB,CAAC,CAAC;EAEF,IAAIR,OAAO,EAAE;IACXG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/D,oBACET,OAAA;MAAKc,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAd,QAAA,gBACxDF,OAAA;QAAKc,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCpB,OAAA;QAAGe,KAAK,EAAE;UAAEM,SAAS,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAApB,QAAA,EAAC;MAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,IAAI,CAAChB,eAAe,EAAE;IACpBI,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IACxE,oBAAOT,OAAA,CAACH,QAAQ;MAAC0B,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEAZ,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EAClE,OAAOP,QAAQ;AACjB,CAAC;AAACC,EAAA,CA5BIF,cAAc;EAAA,QACgCH,OAAO;AAAA;AAAA2B,EAAA,GADrDxB,cAAc;AA8BpB,eAAeA,cAAc;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}