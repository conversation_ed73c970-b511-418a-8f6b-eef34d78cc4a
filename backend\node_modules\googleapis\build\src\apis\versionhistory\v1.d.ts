import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace versionhistory_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * versionhistory.googleapis.com API
     *
     * Version History API - Prod
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const versionhistory = google.versionhistory('v1');
     * ```
     */
    export class Versionhistory {
        context: APIRequestContext;
        platforms: Resource$Platforms;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Each Channel is owned by a Platform and owns a collection of versions. Possible Channels are listed in the Channel enum below. Not all Channels are available for every Platform (e.g. CANARY does not exist for LINUX).
     */
    export interface Schema$Channel {
        /**
         * Type of channel.
         */
        channelType?: string | null;
        /**
         * Channel name. Format is "{product\}/platforms/{platform\}/channels/{channel\}"
         */
        name?: string | null;
    }
    /**
     * Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive). The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time.
     */
    export interface Schema$Interval {
        /**
         * Optional. Exclusive end of the interval. If specified, a Timestamp matching this interval will have to be before the end.
         */
        endTime?: string | null;
        /**
         * Optional. Inclusive start of the interval. If specified, a Timestamp matching this interval will have to be the same or after the start.
         */
        startTime?: string | null;
    }
    /**
     * Response message for ListChannels.
     */
    export interface Schema$ListChannelsResponse {
        /**
         * The list of channels.
         */
        channels?: Schema$Channel[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListPlatforms.
     */
    export interface Schema$ListPlatformsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The list of platforms.
         */
        platforms?: Schema$Platform[];
    }
    /**
     * Response message for ListReleases.
     */
    export interface Schema$ListReleasesResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The list of releases.
         */
        releases?: Schema$Release[];
    }
    /**
     * Response message for ListVersions.
     */
    export interface Schema$ListVersionsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The list of versions.
         */
        versions?: Schema$Version[];
    }
    /**
     * Each Platform is owned by a Product and owns a collection of channels. Available platforms are listed in Platform enum below. Not all Channels are available for every Platform (e.g. CANARY does not exist for LINUX).
     */
    export interface Schema$Platform {
        /**
         * Platform name. Format is "{product\}/platforms/{platform\}"
         */
        name?: string | null;
        /**
         * Type of platform.
         */
        platformType?: string | null;
    }
    /**
     * A Release is owned by a Version. A Release contains information about the release(s) of its parent version. This includes when the release began and ended, as well as what percentage it was released at. If the version is released again, or if the serving percentage changes, it will create another release under the version.
     */
    export interface Schema$Release {
        /**
         * Rollout fraction. This fraction indicates the fraction of people that should receive this version in this release. If the fraction is not specified in ReleaseManager, the API will assume fraction is 1.
         */
        fraction?: number | null;
        /**
         * Rollout fraction group. Only fractions with the same fraction_group are statistically comparable: there may be non-fractional differences between different fraction groups.
         */
        fractionGroup?: string | null;
        /**
         * Release name. Format is "{product\}/platforms/{platform\}/channels/{channel\}/versions/{version\}/releases/{release\}"
         */
        name?: string | null;
        /**
         * Whether or not the release was available for version pinning.
         */
        pinnable?: boolean | null;
        /**
         * Timestamp interval of when the release was live. If end_time is unspecified, the release is currently live.
         */
        serving?: Schema$Interval;
        /**
         * String containing just the version number. e.g. "84.0.4147.38"
         */
        version?: string | null;
    }
    /**
     * Each Version is owned by a Channel. A Version only displays the Version number (e.g. 84.0.4147.38). A Version owns a collection of releases.
     */
    export interface Schema$Version {
        /**
         * Version name. Format is "{product\}/platforms/{platform\}/channels/{channel\}/versions/{version\}" e.g. "chrome/platforms/win/channels/beta/versions/84.0.4147.38"
         */
        name?: string | null;
        /**
         * String containing just the version number. e.g. "84.0.4147.38"
         */
        version?: string | null;
    }
    export class Resource$Platforms {
        context: APIRequestContext;
        channels: Resource$Platforms$Channels;
        constructor(context: APIRequestContext);
        /**
         * Returns list of platforms that are available for a given product. The resource "product" has no resource name in its name.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Platforms$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Platforms$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPlatformsResponse>>;
        list(params: Params$Resource$Platforms$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Platforms$List, options: MethodOptions | BodyResponseCallback<Schema$ListPlatformsResponse>, callback: BodyResponseCallback<Schema$ListPlatformsResponse>): void;
        list(params: Params$Resource$Platforms$List, callback: BodyResponseCallback<Schema$ListPlatformsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPlatformsResponse>): void;
    }
    export interface Params$Resource$Platforms$List extends StandardParameters {
        /**
         * Optional. Optional limit on the number of channels to include in the response. If unspecified, the server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListChannels` call. Provide this to retrieve the subsequent page.
         */
        pageToken?: string;
        /**
         * Required. The product, which owns this collection of platforms. Format: {product\}
         */
        parent?: string;
    }
    export class Resource$Platforms$Channels {
        context: APIRequestContext;
        versions: Resource$Platforms$Channels$Versions;
        constructor(context: APIRequestContext);
        /**
         * Returns list of channels that are available for a given platform.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Platforms$Channels$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Platforms$Channels$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListChannelsResponse>>;
        list(params: Params$Resource$Platforms$Channels$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Platforms$Channels$List, options: MethodOptions | BodyResponseCallback<Schema$ListChannelsResponse>, callback: BodyResponseCallback<Schema$ListChannelsResponse>): void;
        list(params: Params$Resource$Platforms$Channels$List, callback: BodyResponseCallback<Schema$ListChannelsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListChannelsResponse>): void;
    }
    export interface Params$Resource$Platforms$Channels$List extends StandardParameters {
        /**
         * Optional. Optional limit on the number of channels to include in the response. If unspecified, the server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListChannels` call. Provide this to retrieve the subsequent page.
         */
        pageToken?: string;
        /**
         * Required. The platform, which owns this collection of channels. Format: {product\}/platforms/{platform\}
         */
        parent?: string;
    }
    export class Resource$Platforms$Channels$Versions {
        context: APIRequestContext;
        releases: Resource$Platforms$Channels$Versions$Releases;
        constructor(context: APIRequestContext);
        /**
         * Returns list of version for the given platform/channel.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Platforms$Channels$Versions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Platforms$Channels$Versions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListVersionsResponse>>;
        list(params: Params$Resource$Platforms$Channels$Versions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Platforms$Channels$Versions$List, options: MethodOptions | BodyResponseCallback<Schema$ListVersionsResponse>, callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
        list(params: Params$Resource$Platforms$Channels$Versions$List, callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
    }
    export interface Params$Resource$Platforms$Channels$Versions$List extends StandardParameters {
        /**
         * Optional. Filter string. Format is a comma separated list of All comma separated filter clauses are conjoined with a logical "and". Valid field_names are "version", "name", "platform", and "channel". Valid operators are "<", "<=", "=", "\>=", and "\>". Channel comparison is done by distance from stable. Ex) stable < beta, beta < dev, canary < canary_asan. Version comparison is done numerically. If version is not entirely written, the version will be appended with 0 in missing fields. Ex) version \> 80 becoms version \> 80.0.0.0 Name and platform are filtered by string comparison. Ex) "...?filter=channel<=beta, version \>= 80 Ex) "...?filter=version \> 80, version < 81
         */
        filter?: string;
        /**
         * Optional. Ordering string. Valid order_by strings are "version", "name", "platform", and "channel". Optionally, you can append " desc" or " asc" to specify the sorting order. Multiple order_by strings can be used in a comma separated list. Ordering by channel will sort by distance from the stable channel (not alphabetically). A list of channels sorted in this order is: stable, beta, dev, canary, and canary_asan. Sorting by name may cause unexpected behaviour as it is a naive string sort. For example, ******* will be before ******** in descending order. If order_by is not specified the response will be sorted by version in descending order. Ex) "...?order_by=version asc" Ex) "...?order_by=platform desc, channel, version"
         */
        orderBy?: string;
        /**
         * Optional. Optional limit on the number of versions to include in the response. If unspecified, the server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListVersions` call. Provide this to retrieve the subsequent page.
         */
        pageToken?: string;
        /**
         * Required. The channel, which owns this collection of versions. Format: {product\}/platforms/{platform\}/channels/{channel\}
         */
        parent?: string;
    }
    export class Resource$Platforms$Channels$Versions$Releases {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Returns list of releases of the given version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Platforms$Channels$Versions$Releases$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Platforms$Channels$Versions$Releases$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListReleasesResponse>>;
        list(params: Params$Resource$Platforms$Channels$Versions$Releases$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Platforms$Channels$Versions$Releases$List, options: MethodOptions | BodyResponseCallback<Schema$ListReleasesResponse>, callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
        list(params: Params$Resource$Platforms$Channels$Versions$Releases$List, callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListReleasesResponse>): void;
    }
    export interface Params$Resource$Platforms$Channels$Versions$Releases$List extends StandardParameters {
        /**
         * Optional. Filter string. Format is a comma separated list of All comma separated filter clauses are conjoined with a logical "and". Valid field_names are "version", "name", "platform", "channel", "fraction" "starttime", and "endtime". Valid operators are "<", "<=", "=", "\>=", and "\>". Channel comparison is done by distance from stable. must be a valid channel when filtering by channel. Ex) stable < beta, beta < dev, canary < canary_asan. Version comparison is done numerically. Ex) ******* < ********. If version is not entirely written, the version will be appended with 0 for the missing fields. Ex) version \> 80 becoms version \> 80.0.0.0 When filtering by starttime or endtime, string must be in RFC 3339 date string format. Name and platform are filtered by string comparison. Ex) "...?filter=channel<=beta, version \>= 80 Ex) "...?filter=version \> 80, version < 81 Ex) "...?filter=starttime\>2020-01-01T00:00:00Z
         */
        filter?: string;
        /**
         * Optional. Ordering string. Valid order_by strings are "version", "name", "starttime", "endtime", "platform", "channel", and "fraction". Optionally, you can append "desc" or "asc" to specify the sorting order. Multiple order_by strings can be used in a comma separated list. Ordering by channel will sort by distance from the stable channel (not alphabetically). A list of channels sorted in this order is: stable, beta, dev, canary, and canary_asan. Sorting by name may cause unexpected behaviour as it is a naive string sort. For example, ******* will be before ******** in descending order. If order_by is not specified the response will be sorted by starttime in descending order. Ex) "...?order_by=starttime asc" Ex) "...?order_by=platform desc, channel, startime desc"
         */
        orderBy?: string;
        /**
         * Optional. Optional limit on the number of releases to include in the response. If unspecified, the server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListReleases` call. Provide this to retrieve the subsequent page.
         */
        pageToken?: string;
        /**
         * Required. The version, which owns this collection of releases. Format: {product\}/platforms/{platform\}/channels/{channel\}/versions/{version\}
         */
        parent?: string;
    }
    export {};
}
