import React, { useEffect, useState } from 'react';
import { useHealthData } from '../contexts/HealthDataContext';
import { useAuth } from '../contexts/AuthContext';
import StatsCard from '../components/StatsCard';
import ActivityChart from '../components/ActivityChart';
import GoalProgress from '../components/GoalProgress';
import HealthInsights from '../components/HealthInsights';
import QuickActions from '../components/QuickActions';

const Dashboard = () => {
  const { user } = useAuth();
  const { dashboardData, loading, fetchDashboardData } = useHealthData();
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading && !dashboardData) {
    return (
      <div className="flex-center" style={{ minHeight: '50vh' }}>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  const today = dashboardData?.today || {};
  const weekly = dashboardData?.weekly || {};
  const goals = dashboardData?.goals || {};
  const prediction = dashboardData?.prediction;
  const badges = dashboardData?.badges || [];
  const insights = dashboardData?.insights || [];

  return (
    <div className="dashboard">
      {/* Welcome Section */}
      <div className="card">
        <div className="card-header">
          <div>
            <h1 className="card-title">
              Welcome back, {user?.name}! 👋
            </h1>
            <p className="card-subtitle">
              Here's your health overview for today
            </p>
          </div>
          <div className="flex" style={{ gap: '10px' }}>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="form-select"
              style={{ width: 'auto' }}
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
          </div>
        </div>

        {/* Today's Stats */}
        <div className="grid grid-4">
          <StatsCard
            title="Steps"
            value={today.steps?.toLocaleString() || '0'}
            target="10,000"
            icon="👟"
            color="#667eea"
            progress={(today.steps / 10000) * 100}
          />
          <StatsCard
            title="Calories"
            value={today.calories?.toLocaleString() || '0'}
            target="2,000"
            icon="🔥"
            color="#e74c3c"
            progress={(today.calories / 2000) * 100}
          />
          <StatsCard
            title="Active Minutes"
            value={today.activeMinutes || '0'}
            target="60"
            icon="⏱️"
            color="#f39c12"
            progress={(today.activeMinutes / 60) * 100}
          />
          <StatsCard
            title="Activity Score"
            value={today.activityScore || '0'}
            target="100"
            icon="⭐"
            color="#27ae60"
            progress={today.activityScore || 0}
          />
        </div>
      </div>

      <div className="grid grid-2">
        {/* Activity Chart */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Activity Trends</h2>
          </div>
          <ActivityChart period={selectedPeriod} />
        </div>

        {/* Goals Progress */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Active Goals</h2>
            <span className="text-muted">
              {goals.active?.length || 0} active goals
            </span>
          </div>
          <GoalProgress goals={goals.active || []} />
        </div>
      </div>

      <div className="grid grid-2">
        {/* Health Insights */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Health Insights</h2>
          </div>
          <HealthInsights insights={insights} prediction={prediction} />
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Quick Actions</h2>
          </div>
          <QuickActions />
        </div>
      </div>

      {/* Achievements */}
      {badges.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Recent Achievements</h2>
          </div>
          <div className="achievements-grid">
            {badges.map((badge, index) => (
              <div key={index} className="achievement-badge">
                <span className="badge-icon">{badge.icon}</span>
                <div className="badge-info">
                  <h4>{badge.name}</h4>
                  <p>{badge.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Weekly Summary */}
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">Weekly Summary</h2>
        </div>
        <div className="grid grid-4">
          <div className="summary-stat">
            <h3>{weekly.totalSteps?.toLocaleString() || '0'}</h3>
            <p>Total Steps</p>
            <small className="text-muted">
              Avg: {Math.round(weekly.avgSteps || 0).toLocaleString()}/day
            </small>
          </div>
          <div className="summary-stat">
            <h3>{Math.round(weekly.totalDistance || 0)} km</h3>
            <p>Distance Covered</p>
            <small className="text-muted">
              Avg: {Math.round((weekly.totalDistance || 0) / 7)} km/day
            </small>
          </div>
          <div className="summary-stat">
            <h3>{Math.round(weekly.avgSleep || 0)} min</h3>
            <p>Avg Sleep</p>
            <small className="text-muted">
              {Math.round((weekly.avgSleep || 0) / 60)} hours/night
            </small>
          </div>
          <div className="summary-stat">
            <h3>{Math.round(weekly.avgHeartRate || 0)} bpm</h3>
            <p>Avg Heart Rate</p>
            <small className="text-muted">
              Resting rate
            </small>
          </div>
        </div>
      </div>

      <style jsx>{`
        .dashboard {
          padding: 0;
        }

        .achievements-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;
        }

        .achievement-badge {
          display: flex;
          align-items: center;
          padding: 15px;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
          border-radius: 12px;
          border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .badge-icon {
          font-size: 2rem;
          margin-right: 15px;
        }

        .badge-info h4 {
          margin: 0 0 5px 0;
          color: #2c3e50;
          font-size: 1rem;
        }

        .badge-info p {
          margin: 0;
          color: #7f8c8d;
          font-size: 0.9rem;
        }

        .summary-stat {
          text-align: center;
          padding: 20px;
          background: rgba(102, 126, 234, 0.05);
          border-radius: 12px;
          border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .summary-stat h3 {
          margin: 0 0 8px 0;
          font-size: 1.8rem;
          font-weight: 700;
          color: #2c3e50;
        }

        .summary-stat p {
          margin: 0 0 5px 0;
          font-weight: 500;
          color: #2c3e50;
        }

        .summary-stat small {
          font-size: 0.8rem;
        }

        @media (max-width: 768px) {
          .grid-4 {
            grid-template-columns: repeat(2, 1fr);
          }
          
          .grid-2 {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 480px) {
          .grid-4 {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Dashboard;
