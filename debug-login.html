<!DOCTYPE html>
<html>
<head>
    <title>HealthConnect Login Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #667eea; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #5a6fd8; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 HealthConnect Login Debug</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="demo123" required>
            </div>
            
            <button type="submit">Test Login</button>
        </form>
        
        <div id="result"></div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Test Login" to test the API directly</li>
                <li>Check the console (F12) for detailed logs</li>
                <li>If this works, the issue is in the React app</li>
                <li>If this fails, the issue is in the backend API</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="info">Testing login...</div>';
            
            try {
                console.log('🔍 Testing login with:', { email, password });
                
                // Test 1: Check if backend is reachable
                console.log('📡 Step 1: Testing backend health...');
                const healthResponse = await fetch('http://localhost:5000/api/health');
                const healthData = await healthResponse.text();
                console.log('✅ Backend health check:', healthResponse.status, healthData);
                
                // Test 2: Attempt login
                console.log('🔐 Step 2: Attempting login...');
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('📊 Login response status:', loginResponse.status);
                console.log('📊 Login response headers:', Object.fromEntries(loginResponse.headers.entries()));
                
                const loginData = await loginResponse.json();
                console.log('📊 Login response data:', loginData);
                
                if (loginData.success) {
                    // Test 3: Test protected route
                    console.log('🛡️ Step 3: Testing protected route...');
                    const dashboardResponse = await fetch('http://localhost:5000/api/users/dashboard', {
                        headers: {
                            'Authorization': `Bearer ${loginData.data.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const dashboardData = await dashboardResponse.json();
                    console.log('📊 Dashboard response:', dashboardData);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Test Successful!</h3>
                            <p><strong>User:</strong> ${loginData.data.user.name}</p>
                            <p><strong>Email:</strong> ${loginData.data.user.email}</p>
                            <p><strong>Token:</strong> ${loginData.data.token.substring(0, 20)}...</p>
                            <p><strong>Dashboard Access:</strong> ${dashboardData.success ? '✅ Working' : '❌ Failed'}</p>
                            <p><strong>Today\'s Steps:</strong> ${dashboardData.data?.today?.steps || 'No data'}</p>
                            
                            <h4>Full Response:</h4>
                            <pre>${JSON.stringify(loginData, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Message:</strong> ${loginData.message}</p>
                            <p><strong>Status:</strong> ${loginResponse.status}</p>
                            
                            <h4>Full Response:</h4>
                            <pre>${JSON.stringify(loginData, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('❌ Login test error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Connection Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>This usually means the backend server is not running or there's a CORS issue.</p>
                        
                        <h4>Troubleshooting:</h4>
                        <ul>
                            <li>Make sure backend is running on http://localhost:5000</li>
                            <li>Check if CORS is properly configured</li>
                            <li>Verify the API endpoint is correct</li>
                        </ul>
                    </div>
                `;
            }
        });
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('🚀 HealthConnect Login Debug Tool Loaded');
            console.log('📍 Backend URL: http://localhost:5000');
            console.log('📍 Frontend URL: http://localhost:3000');
        });
    </script>
</body>
</html>
