import React, { useState, useEffect } from 'react';
import { useHealthData } from '../contexts/HealthDataContext';

const Predictions = () => {
  const { predictions, fetchPredictions, generatePrediction, loading } = useHealthData();
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    fetchPredictions();
  }, []);

  const handleGeneratePrediction = async () => {
    setGenerating(true);
    await generatePrediction('comprehensive');
    setGenerating(false);
  };

  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'very_low': case 'low': return '#27ae60';
      case 'moderate': return '#f39c12';
      case 'high': case 'very_high': return '#e74c3c';
      default: return '#667eea';
    }
  };

  return (
    <div className="predictions-page">
      <div className="page-header">
        <div>
          <h1>Health Predictions</h1>
          <p>AI-powered health insights based on your fitness data</p>
        </div>
        <button 
          className="btn btn-primary"
          onClick={handleGeneratePrediction}
          disabled={generating}
        >
          {generating ? 'Generating...' : 'Generate New Analysis'}
        </button>
      </div>

      {loading ? (
        <div className="loading-spinner"></div>
      ) : predictions.length === 0 ? (
        <div className="no-predictions">
          <div className="no-predictions-icon">🔮</div>
          <h3>No Health Predictions Yet</h3>
          <p>Generate your first AI health analysis to get personalized insights!</p>
          <button className="btn btn-primary" onClick={handleGeneratePrediction}>
            Generate Analysis
          </button>
        </div>
      ) : (
        <div className="predictions-list">
          {predictions.map((prediction) => (
            <div key={prediction._id} className="prediction-card">
              <div className="prediction-header">
                <div>
                  <h3>Health Analysis</h3>
                  <p className="prediction-date">
                    {new Date(prediction.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <span 
                  className="overall-risk"
                  style={{ backgroundColor: getRiskColor(prediction.overallRiskLevel) }}
                >
                  {prediction.overallRiskLevel?.replace('_', ' ').toUpperCase() || 'UNKNOWN'} RISK
                </span>
              </div>

              {prediction.predictions && prediction.predictions.length > 0 && (
                <div className="predictions-section">
                  <h4>Risk Assessment</h4>
                  <div className="predictions-grid">
                    {prediction.predictions.map((pred, index) => (
                      <div key={index} className="prediction-item">
                        <div className="prediction-condition">
                          <h5>{pred.condition}</h5>
                          <span 
                            className="risk-badge"
                            style={{ backgroundColor: getRiskColor(pred.riskLevel) }}
                          >
                            {pred.riskLevel?.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>
                        <p className="prediction-description">{pred.description}</p>
                        <div className="prediction-confidence">
                          Confidence: {pred.confidence}%
                        </div>
                        {pred.recommendations && pred.recommendations.length > 0 && (
                          <div className="prediction-recommendations">
                            <strong>Recommendations:</strong>
                            <ul>
                              {pred.recommendations.slice(0, 3).map((rec, i) => (
                                <li key={i}>{rec}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {prediction.recommendations && prediction.recommendations.length > 0 && (
                <div className="recommendations-section">
                  <h4>Personalized Recommendations</h4>
                  <div className="recommendations-grid">
                    {prediction.recommendations.map((rec, index) => (
                      <div key={index} className="recommendation-item">
                        <div className="recommendation-header">
                          <h5>{rec.title}</h5>
                          <span className={`priority-badge priority-${rec.priority}`}>
                            {rec.priority}
                          </span>
                        </div>
                        <p className="recommendation-description">{rec.description}</p>
                        {rec.actionItems && rec.actionItems.length > 0 && (
                          <div className="action-items">
                            <strong>Action Items:</strong>
                            <ul>
                              {rec.actionItems.map((action, i) => (
                                <li key={i}>{action}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        <div className="recommendation-meta">
                          <span>📈 {rec.expectedBenefit}</span>
                          <span>⏰ {rec.timeframe}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {prediction.modelInfo && (
                <div className="model-info">
                  <h4>Analysis Details</h4>
                  <div className="model-details">
                    <span>Model: {prediction.modelInfo.modelName}</span>
                    <span>Version: {prediction.modelInfo.modelVersion}</span>
                    <span>Algorithm: {prediction.modelInfo.algorithm}</span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      <style jsx>{`
        .predictions-page { padding: 0; }
        .page-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 30px; }
        .page-header h1 { margin: 0 0 5px 0; color: #2c3e50; }
        .page-header p { margin: 0; color: #7f8c8d; }
        .no-predictions { text-align: center; padding: 60px 20px; color: #7f8c8d; }
        .no-predictions-icon { font-size: 4rem; margin-bottom: 20px; }
        .predictions-list { display: flex; flex-direction: column; gap: 30px; }
        .prediction-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
        .prediction-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 25px; }
        .prediction-header h3 { margin: 0; color: #2c3e50; }
        .prediction-date { margin: 5px 0 0 0; color: #7f8c8d; font-size: 0.9rem; }
        .overall-risk { padding: 8px 16px; border-radius: 20px; color: white; font-size: 0.8rem; font-weight: 700; letter-spacing: 0.5px; }
        .predictions-section, .recommendations-section, .model-info { margin-bottom: 25px; }
        .predictions-section h4, .recommendations-section h4, .model-info h4 { margin: 0 0 15px 0; color: #2c3e50; font-size: 1.1rem; }
        .predictions-grid, .recommendations-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .prediction-item, .recommendation-item { background: rgba(102, 126, 234, 0.05); border: 1px solid rgba(102, 126, 234, 0.1); border-radius: 12px; padding: 20px; }
        .prediction-condition, .recommendation-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px; }
        .prediction-condition h5, .recommendation-header h5 { margin: 0; color: #2c3e50; }
        .risk-badge, .priority-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }
        .priority-high { background: #e74c3c; }
        .priority-medium { background: #f39c12; }
        .priority-low { background: #27ae60; }
        .prediction-description, .recommendation-description { color: #2c3e50; margin-bottom: 10px; font-size: 0.9rem; line-height: 1.4; }
        .prediction-confidence { font-size: 0.85rem; color: #667eea; font-weight: 600; margin-bottom: 10px; }
        .prediction-recommendations, .action-items { margin-top: 10px; }
        .prediction-recommendations ul, .action-items ul { margin: 5px 0 0 20px; padding: 0; }
        .prediction-recommendations li, .action-items li { margin-bottom: 5px; color: #2c3e50; font-size: 0.85rem; }
        .recommendation-meta { display: flex; gap: 15px; margin-top: 10px; font-size: 0.8rem; color: #7f8c8d; }
        .model-details { display: flex; gap: 20px; flex-wrap: wrap; font-size: 0.85rem; color: #7f8c8d; }
        @media (max-width: 768px) {
          .page-header { flex-direction: column; gap: 15px; align-items: stretch; }
          .predictions-grid, .recommendations-grid { grid-template-columns: 1fr; }
          .prediction-condition, .recommendation-header { flex-direction: column; gap: 10px; }
          .model-details { flex-direction: column; gap: 5px; }
        }
      `}</style>
    </div>
  );
};

export default Predictions;
