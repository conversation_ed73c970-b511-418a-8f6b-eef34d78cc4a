{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-center\",\n      style: {\n        minHeight: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n  return isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 39\n  }, this);\n};\n_s(ProtectedRoute, \"F3aPsg481KjBH7Z7iYl6LJifZz0=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "className", "style", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"flex-center\" style={{ minHeight: '50vh' }}>\n        <div className=\"loading-spinner\"></div>\n      </div>\n    );\n  }\n\n  return isAuthenticated ? children : <Navigate to=\"/login\" replace />;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EAE9C,IAAIO,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKM,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAN,QAAA,eACxDF,OAAA;QAAKM,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,OAAOR,eAAe,GAAGF,QAAQ,gBAAGF,OAAA,CAACH,QAAQ;IAACgB,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACtE,CAAC;AAACT,EAAA,CAZIF,cAAc;EAAA,QACmBH,OAAO;AAAA;AAAAiB,EAAA,GADxCd,cAAc;AAcpB,eAAeA,cAAc;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}