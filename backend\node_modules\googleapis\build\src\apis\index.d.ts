/*! THIS FILE IS AUTO-GENERATED */
import { abusiveexperiencereport } from './abusiveexperiencereport';
import { acceleratedmobilepageurl } from './acceleratedmobilepageurl';
import { accessapproval } from './accessapproval';
import { accesscontextmanager } from './accesscontextmanager';
import { acmedns } from './acmedns';
import { addressvalidation } from './addressvalidation';
import { adexchangebuyer } from './adexchangebuyer';
import { adexchangebuyer2 } from './adexchangebuyer2';
import { adexperiencereport } from './adexperiencereport';
import { admin } from './admin';
import { admob } from './admob';
import { adsense } from './adsense';
import { adsensehost } from './adsensehost';
import { adsenseplatform } from './adsenseplatform';
import { advisorynotifications } from './advisorynotifications';
import { aiplatform } from './aiplatform';
import { airquality } from './airquality';
import { alertcenter } from './alertcenter';
import { alloydb } from './alloydb';
import { analytics } from './analytics';
import { analyticsadmin } from './analyticsadmin';
import { analyticsdata } from './analyticsdata';
import { analyticshub } from './analyticshub';
import { analyticsreporting } from './analyticsreporting';
import { androiddeviceprovisioning } from './androiddeviceprovisioning';
import { androidenterprise } from './androidenterprise';
import { androidmanagement } from './androidmanagement';
import { androidpublisher } from './androidpublisher';
import { apigateway } from './apigateway';
import { apigeeregistry } from './apigeeregistry';
import { apihub } from './apihub';
import { apikeys } from './apikeys';
import { apim } from './apim';
import { appengine } from './appengine';
import { apphub } from './apphub';
import { appsactivity } from './appsactivity';
import { area120tables } from './area120tables';
import { areainsights } from './areainsights';
import { artifactregistry } from './artifactregistry';
import { assuredworkloads } from './assuredworkloads';
import { authorizedbuyersmarketplace } from './authorizedbuyersmarketplace';
import { backupdr } from './backupdr';
import { baremetalsolution } from './baremetalsolution';
import { batch } from './batch';
import { beyondcorp } from './beyondcorp';
import { biglake } from './biglake';
import { bigquery } from './bigquery';
import { bigqueryconnection } from './bigqueryconnection';
import { bigquerydatapolicy } from './bigquerydatapolicy';
import { bigquerydatatransfer } from './bigquerydatatransfer';
import { bigqueryreservation } from './bigqueryreservation';
import { bigtableadmin } from './bigtableadmin';
import { billingbudgets } from './billingbudgets';
import { binaryauthorization } from './binaryauthorization';
import { blockchainnodeengine } from './blockchainnodeengine';
import { blogger } from './blogger';
import { books } from './books';
import { businessprofileperformance } from './businessprofileperformance';
import { calendar } from './calendar';
import { certificatemanager } from './certificatemanager';
import { chat } from './chat';
import { checks } from './checks';
import { chromemanagement } from './chromemanagement';
import { chromepolicy } from './chromepolicy';
import { chromeuxreport } from './chromeuxreport';
import { civicinfo } from './civicinfo';
import { classroom } from './classroom';
import { cloudasset } from './cloudasset';
import { cloudbilling } from './cloudbilling';
import { cloudbuild } from './cloudbuild';
import { cloudchannel } from './cloudchannel';
import { cloudcontrolspartner } from './cloudcontrolspartner';
import { clouddebugger } from './clouddebugger';
import { clouddeploy } from './clouddeploy';
import { clouderrorreporting } from './clouderrorreporting';
import { cloudfunctions } from './cloudfunctions';
import { cloudidentity } from './cloudidentity';
import { cloudiot } from './cloudiot';
import { cloudkms } from './cloudkms';
import { cloudprofiler } from './cloudprofiler';
import { cloudresourcemanager } from './cloudresourcemanager';
import { cloudscheduler } from './cloudscheduler';
import { cloudsearch } from './cloudsearch';
import { cloudshell } from './cloudshell';
import { cloudsupport } from './cloudsupport';
import { cloudtasks } from './cloudtasks';
import { cloudtrace } from './cloudtrace';
import { composer } from './composer';
import { compute } from './compute';
import { config } from './config';
import { connectors } from './connectors';
import { contactcenteraiplatform } from './contactcenteraiplatform';
import { contactcenterinsights } from './contactcenterinsights';
import { container } from './container';
import { containeranalysis } from './containeranalysis';
import { content } from './content';
import { contentwarehouse } from './contentwarehouse';
import { css } from './css';
import { customsearch } from './customsearch';
import { datacatalog } from './datacatalog';
import { dataflow } from './dataflow';
import { dataform } from './dataform';
import { datafusion } from './datafusion';
import { datalabeling } from './datalabeling';
import { datalineage } from './datalineage';
import { datamigration } from './datamigration';
import { datapipelines } from './datapipelines';
import { dataplex } from './dataplex';
import { dataportability } from './dataportability';
import { dataproc } from './dataproc';
import { datastore } from './datastore';
import { datastream } from './datastream';
import { deploymentmanager } from './deploymentmanager';
import { developerconnect } from './developerconnect';
import { dfareporting } from './dfareporting';
import { dialogflow } from './dialogflow';
import { digitalassetlinks } from './digitalassetlinks';
import { discovery } from './discovery';
import { discoveryengine } from './discoveryengine';
import { displayvideo } from './displayvideo';
import { dlp } from './dlp';
import { dns } from './dns';
import { docs } from './docs';
import { documentai } from './documentai';
import { domains } from './domains';
import { domainsrdap } from './domainsrdap';
import { doubleclickbidmanager } from './doubleclickbidmanager';
import { doubleclicksearch } from './doubleclicksearch';
import { drive } from './drive';
import { driveactivity } from './driveactivity';
import { drivelabels } from './drivelabels';
import { essentialcontacts } from './essentialcontacts';
import { eventarc } from './eventarc';
import { factchecktools } from './factchecktools';
import { fcm } from './fcm';
import { fcmdata } from './fcmdata';
import { file } from './file';
import { firebase } from './firebase';
import { firebaseappcheck } from './firebaseappcheck';
import { firebaseappdistribution } from './firebaseappdistribution';
import { firebaseapphosting } from './firebaseapphosting';
import { firebasedatabase } from './firebasedatabase';
import { firebasedataconnect } from './firebasedataconnect';
import { firebasedynamiclinks } from './firebasedynamiclinks';
import { firebasehosting } from './firebasehosting';
import { firebaseml } from './firebaseml';
import { firebaserules } from './firebaserules';
import { firebasestorage } from './firebasestorage';
import { firestore } from './firestore';
import { fitness } from './fitness';
import { forms } from './forms';
import { games } from './games';
import { gamesConfiguration } from './gamesConfiguration';
import { gamesManagement } from './gamesManagement';
import { gameservices } from './gameservices';
import { genomics } from './genomics';
import { gkebackup } from './gkebackup';
import { gkehub } from './gkehub';
import { gkeonprem } from './gkeonprem';
import { gmail } from './gmail';
import { gmailpostmastertools } from './gmailpostmastertools';
import { groupsmigration } from './groupsmigration';
import { groupssettings } from './groupssettings';
import { healthcare } from './healthcare';
import { homegraph } from './homegraph';
import { iam } from './iam';
import { iamcredentials } from './iamcredentials';
import { iap } from './iap';
import { ideahub } from './ideahub';
import { identitytoolkit } from './identitytoolkit';
import { ids } from './ids';
import { indexing } from './indexing';
import { integrations } from './integrations';
import { jobs } from './jobs';
import { keep } from './keep';
import { kgsearch } from './kgsearch';
import { kmsinventory } from './kmsinventory';
import { language } from './language';
import { libraryagent } from './libraryagent';
import { licensing } from './licensing';
import { lifesciences } from './lifesciences';
import { localservices } from './localservices';
import { logging } from './logging';
import { looker } from './looker';
import { managedidentities } from './managedidentities';
import { managedkafka } from './managedkafka';
import { manufacturers } from './manufacturers';
import { marketingplatformadmin } from './marketingplatformadmin';
import { meet } from './meet';
import { memcache } from './memcache';
import { merchantapi } from './merchantapi';
import { metastore } from './metastore';
import { migrationcenter } from './migrationcenter';
import { ml } from './ml';
import { monitoring } from './monitoring';
import { mybusinessaccountmanagement } from './mybusinessaccountmanagement';
import { mybusinessbusinesscalls } from './mybusinessbusinesscalls';
import { mybusinessbusinessinformation } from './mybusinessbusinessinformation';
import { mybusinesslodging } from './mybusinesslodging';
import { mybusinessnotifications } from './mybusinessnotifications';
import { mybusinessplaceactions } from './mybusinessplaceactions';
import { mybusinessqanda } from './mybusinessqanda';
import { mybusinessverifications } from './mybusinessverifications';
import { netapp } from './netapp';
import { networkconnectivity } from './networkconnectivity';
import { networkmanagement } from './networkmanagement';
import { networksecurity } from './networksecurity';
import { networkservices } from './networkservices';
import { notebooks } from './notebooks';
import { oauth2 } from './oauth2';
import { observability } from './observability';
import { ondemandscanning } from './ondemandscanning';
import { oracledatabase } from './oracledatabase';
import { orgpolicy } from './orgpolicy';
import { osconfig } from './osconfig';
import { oslogin } from './oslogin';
import { pagespeedonline } from './pagespeedonline';
import { parallelstore } from './parallelstore';
import { paymentsresellersubscription } from './paymentsresellersubscription';
import { people } from './people';
import { places } from './places';
import { playablelocations } from './playablelocations';
import { playcustomapp } from './playcustomapp';
import { playdeveloperreporting } from './playdeveloperreporting';
import { playgrouping } from './playgrouping';
import { playintegrity } from './playintegrity';
import { plus } from './plus';
import { policyanalyzer } from './policyanalyzer';
import { policysimulator } from './policysimulator';
import { policytroubleshooter } from './policytroubleshooter';
import { pollen } from './pollen';
import { poly } from './poly';
import { privateca } from './privateca';
import { prod_tt_sasportal } from './prod_tt_sasportal';
import { publicca } from './publicca';
import { pubsub } from './pubsub';
import { pubsublite } from './pubsublite';
import { rapidmigrationassessment } from './rapidmigrationassessment';
import { readerrevenuesubscriptionlinking } from './readerrevenuesubscriptionlinking';
import { realtimebidding } from './realtimebidding';
import { recaptchaenterprise } from './recaptchaenterprise';
import { recommendationengine } from './recommendationengine';
import { recommender } from './recommender';
import { redis } from './redis';
import { remotebuildexecution } from './remotebuildexecution';
import { reseller } from './reseller';
import { resourcesettings } from './resourcesettings';
import { retail } from './retail';
import { run } from './run';
import { runtimeconfig } from './runtimeconfig';
import { safebrowsing } from './safebrowsing';
import { sasportal } from './sasportal';
import { script } from './script';
import { searchads360 } from './searchads360';
import { searchconsole } from './searchconsole';
import { secretmanager } from './secretmanager';
import { securitycenter } from './securitycenter';
import { securityposture } from './securityposture';
import { serviceconsumermanagement } from './serviceconsumermanagement';
import { servicecontrol } from './servicecontrol';
import { servicedirectory } from './servicedirectory';
import { servicemanagement } from './servicemanagement';
import { servicenetworking } from './servicenetworking';
import { serviceusage } from './serviceusage';
import { sheets } from './sheets';
import { siteVerification } from './siteVerification';
import { slides } from './slides';
import { smartdevicemanagement } from './smartdevicemanagement';
import { solar } from './solar';
import { sourcerepo } from './sourcerepo';
import { spanner } from './spanner';
import { speech } from './speech';
import { sql } from './sql';
import { sqladmin } from './sqladmin';
import { storage } from './storage';
import { storagebatchoperations } from './storagebatchoperations';
import { storagetransfer } from './storagetransfer';
import { streetviewpublish } from './streetviewpublish';
import { sts } from './sts';
import { tagmanager } from './tagmanager';
import { tasks } from './tasks';
import { testing } from './testing';
import { texttospeech } from './texttospeech';
import { toolresults } from './toolresults';
import { tpu } from './tpu';
import { trafficdirector } from './trafficdirector';
import { transcoder } from './transcoder';
import { translate } from './translate';
import { travelimpactmodel } from './travelimpactmodel';
import { vault } from './vault';
import { vectortile } from './vectortile';
import { verifiedaccess } from './verifiedaccess';
import { versionhistory } from './versionhistory';
import { videointelligence } from './videointelligence';
import { vision } from './vision';
import { vmmigration } from './vmmigration';
import { vmwareengine } from './vmwareengine';
import { vpcaccess } from './vpcaccess';
import { walletobjects } from './walletobjects';
import { webfonts } from './webfonts';
import { webmasters } from './webmasters';
import { webrisk } from './webrisk';
import { websecurityscanner } from './websecurityscanner';
import { workflowexecutions } from './workflowexecutions';
import { workflows } from './workflows';
import { workloadmanager } from './workloadmanager';
import { workspaceevents } from './workspaceevents';
import { workstations } from './workstations';
import { youtube } from './youtube';
import { youtubeAnalytics } from './youtubeAnalytics';
import { youtubereporting } from './youtubereporting';
export interface APIList {
    [index: string]: {
        [index: string]: any;
    };
}
export declare const APIS: APIList;
export declare class GeneratedAPIs {
    abusiveexperiencereport: typeof abusiveexperiencereport;
    acceleratedmobilepageurl: typeof acceleratedmobilepageurl;
    accessapproval: typeof accessapproval;
    accesscontextmanager: typeof accesscontextmanager;
    acmedns: typeof acmedns;
    addressvalidation: typeof addressvalidation;
    adexchangebuyer: typeof adexchangebuyer;
    adexchangebuyer2: typeof adexchangebuyer2;
    adexperiencereport: typeof adexperiencereport;
    admin: typeof admin;
    admob: typeof admob;
    adsense: typeof adsense;
    adsensehost: typeof adsensehost;
    adsenseplatform: typeof adsenseplatform;
    advisorynotifications: typeof advisorynotifications;
    aiplatform: typeof aiplatform;
    airquality: typeof airquality;
    alertcenter: typeof alertcenter;
    alloydb: typeof alloydb;
    analytics: typeof analytics;
    analyticsadmin: typeof analyticsadmin;
    analyticsdata: typeof analyticsdata;
    analyticshub: typeof analyticshub;
    analyticsreporting: typeof analyticsreporting;
    androiddeviceprovisioning: typeof androiddeviceprovisioning;
    androidenterprise: typeof androidenterprise;
    androidmanagement: typeof androidmanagement;
    androidpublisher: typeof androidpublisher;
    apigateway: typeof apigateway;
    apigeeregistry: typeof apigeeregistry;
    apihub: typeof apihub;
    apikeys: typeof apikeys;
    apim: typeof apim;
    appengine: typeof appengine;
    apphub: typeof apphub;
    appsactivity: typeof appsactivity;
    area120tables: typeof area120tables;
    areainsights: typeof areainsights;
    artifactregistry: typeof artifactregistry;
    assuredworkloads: typeof assuredworkloads;
    authorizedbuyersmarketplace: typeof authorizedbuyersmarketplace;
    backupdr: typeof backupdr;
    baremetalsolution: typeof baremetalsolution;
    batch: typeof batch;
    beyondcorp: typeof beyondcorp;
    biglake: typeof biglake;
    bigquery: typeof bigquery;
    bigqueryconnection: typeof bigqueryconnection;
    bigquerydatapolicy: typeof bigquerydatapolicy;
    bigquerydatatransfer: typeof bigquerydatatransfer;
    bigqueryreservation: typeof bigqueryreservation;
    bigtableadmin: typeof bigtableadmin;
    billingbudgets: typeof billingbudgets;
    binaryauthorization: typeof binaryauthorization;
    blockchainnodeengine: typeof blockchainnodeengine;
    blogger: typeof blogger;
    books: typeof books;
    businessprofileperformance: typeof businessprofileperformance;
    calendar: typeof calendar;
    certificatemanager: typeof certificatemanager;
    chat: typeof chat;
    checks: typeof checks;
    chromemanagement: typeof chromemanagement;
    chromepolicy: typeof chromepolicy;
    chromeuxreport: typeof chromeuxreport;
    civicinfo: typeof civicinfo;
    classroom: typeof classroom;
    cloudasset: typeof cloudasset;
    cloudbilling: typeof cloudbilling;
    cloudbuild: typeof cloudbuild;
    cloudchannel: typeof cloudchannel;
    cloudcontrolspartner: typeof cloudcontrolspartner;
    clouddebugger: typeof clouddebugger;
    clouddeploy: typeof clouddeploy;
    clouderrorreporting: typeof clouderrorreporting;
    cloudfunctions: typeof cloudfunctions;
    cloudidentity: typeof cloudidentity;
    cloudiot: typeof cloudiot;
    cloudkms: typeof cloudkms;
    cloudprofiler: typeof cloudprofiler;
    cloudresourcemanager: typeof cloudresourcemanager;
    cloudscheduler: typeof cloudscheduler;
    cloudsearch: typeof cloudsearch;
    cloudshell: typeof cloudshell;
    cloudsupport: typeof cloudsupport;
    cloudtasks: typeof cloudtasks;
    cloudtrace: typeof cloudtrace;
    composer: typeof composer;
    compute: typeof compute;
    config: typeof config;
    connectors: typeof connectors;
    contactcenteraiplatform: typeof contactcenteraiplatform;
    contactcenterinsights: typeof contactcenterinsights;
    container: typeof container;
    containeranalysis: typeof containeranalysis;
    content: typeof content;
    contentwarehouse: typeof contentwarehouse;
    css: typeof css;
    customsearch: typeof customsearch;
    datacatalog: typeof datacatalog;
    dataflow: typeof dataflow;
    dataform: typeof dataform;
    datafusion: typeof datafusion;
    datalabeling: typeof datalabeling;
    datalineage: typeof datalineage;
    datamigration: typeof datamigration;
    datapipelines: typeof datapipelines;
    dataplex: typeof dataplex;
    dataportability: typeof dataportability;
    dataproc: typeof dataproc;
    datastore: typeof datastore;
    datastream: typeof datastream;
    deploymentmanager: typeof deploymentmanager;
    developerconnect: typeof developerconnect;
    dfareporting: typeof dfareporting;
    dialogflow: typeof dialogflow;
    digitalassetlinks: typeof digitalassetlinks;
    discovery: typeof discovery;
    discoveryengine: typeof discoveryengine;
    displayvideo: typeof displayvideo;
    dlp: typeof dlp;
    dns: typeof dns;
    docs: typeof docs;
    documentai: typeof documentai;
    domains: typeof domains;
    domainsrdap: typeof domainsrdap;
    doubleclickbidmanager: typeof doubleclickbidmanager;
    doubleclicksearch: typeof doubleclicksearch;
    drive: typeof drive;
    driveactivity: typeof driveactivity;
    drivelabels: typeof drivelabels;
    essentialcontacts: typeof essentialcontacts;
    eventarc: typeof eventarc;
    factchecktools: typeof factchecktools;
    fcm: typeof fcm;
    fcmdata: typeof fcmdata;
    file: typeof file;
    firebase: typeof firebase;
    firebaseappcheck: typeof firebaseappcheck;
    firebaseappdistribution: typeof firebaseappdistribution;
    firebaseapphosting: typeof firebaseapphosting;
    firebasedatabase: typeof firebasedatabase;
    firebasedataconnect: typeof firebasedataconnect;
    firebasedynamiclinks: typeof firebasedynamiclinks;
    firebasehosting: typeof firebasehosting;
    firebaseml: typeof firebaseml;
    firebaserules: typeof firebaserules;
    firebasestorage: typeof firebasestorage;
    firestore: typeof firestore;
    fitness: typeof fitness;
    forms: typeof forms;
    games: typeof games;
    gamesConfiguration: typeof gamesConfiguration;
    gamesManagement: typeof gamesManagement;
    gameservices: typeof gameservices;
    genomics: typeof genomics;
    gkebackup: typeof gkebackup;
    gkehub: typeof gkehub;
    gkeonprem: typeof gkeonprem;
    gmail: typeof gmail;
    gmailpostmastertools: typeof gmailpostmastertools;
    groupsmigration: typeof groupsmigration;
    groupssettings: typeof groupssettings;
    healthcare: typeof healthcare;
    homegraph: typeof homegraph;
    iam: typeof iam;
    iamcredentials: typeof iamcredentials;
    iap: typeof iap;
    ideahub: typeof ideahub;
    identitytoolkit: typeof identitytoolkit;
    ids: typeof ids;
    indexing: typeof indexing;
    integrations: typeof integrations;
    jobs: typeof jobs;
    keep: typeof keep;
    kgsearch: typeof kgsearch;
    kmsinventory: typeof kmsinventory;
    language: typeof language;
    libraryagent: typeof libraryagent;
    licensing: typeof licensing;
    lifesciences: typeof lifesciences;
    localservices: typeof localservices;
    logging: typeof logging;
    looker: typeof looker;
    managedidentities: typeof managedidentities;
    managedkafka: typeof managedkafka;
    manufacturers: typeof manufacturers;
    marketingplatformadmin: typeof marketingplatformadmin;
    meet: typeof meet;
    memcache: typeof memcache;
    merchantapi: typeof merchantapi;
    metastore: typeof metastore;
    migrationcenter: typeof migrationcenter;
    ml: typeof ml;
    monitoring: typeof monitoring;
    mybusinessaccountmanagement: typeof mybusinessaccountmanagement;
    mybusinessbusinesscalls: typeof mybusinessbusinesscalls;
    mybusinessbusinessinformation: typeof mybusinessbusinessinformation;
    mybusinesslodging: typeof mybusinesslodging;
    mybusinessnotifications: typeof mybusinessnotifications;
    mybusinessplaceactions: typeof mybusinessplaceactions;
    mybusinessqanda: typeof mybusinessqanda;
    mybusinessverifications: typeof mybusinessverifications;
    netapp: typeof netapp;
    networkconnectivity: typeof networkconnectivity;
    networkmanagement: typeof networkmanagement;
    networksecurity: typeof networksecurity;
    networkservices: typeof networkservices;
    notebooks: typeof notebooks;
    oauth2: typeof oauth2;
    observability: typeof observability;
    ondemandscanning: typeof ondemandscanning;
    oracledatabase: typeof oracledatabase;
    orgpolicy: typeof orgpolicy;
    osconfig: typeof osconfig;
    oslogin: typeof oslogin;
    pagespeedonline: typeof pagespeedonline;
    parallelstore: typeof parallelstore;
    paymentsresellersubscription: typeof paymentsresellersubscription;
    people: typeof people;
    places: typeof places;
    playablelocations: typeof playablelocations;
    playcustomapp: typeof playcustomapp;
    playdeveloperreporting: typeof playdeveloperreporting;
    playgrouping: typeof playgrouping;
    playintegrity: typeof playintegrity;
    plus: typeof plus;
    policyanalyzer: typeof policyanalyzer;
    policysimulator: typeof policysimulator;
    policytroubleshooter: typeof policytroubleshooter;
    pollen: typeof pollen;
    poly: typeof poly;
    privateca: typeof privateca;
    prod_tt_sasportal: typeof prod_tt_sasportal;
    publicca: typeof publicca;
    pubsub: typeof pubsub;
    pubsublite: typeof pubsublite;
    rapidmigrationassessment: typeof rapidmigrationassessment;
    readerrevenuesubscriptionlinking: typeof readerrevenuesubscriptionlinking;
    realtimebidding: typeof realtimebidding;
    recaptchaenterprise: typeof recaptchaenterprise;
    recommendationengine: typeof recommendationengine;
    recommender: typeof recommender;
    redis: typeof redis;
    remotebuildexecution: typeof remotebuildexecution;
    reseller: typeof reseller;
    resourcesettings: typeof resourcesettings;
    retail: typeof retail;
    run: typeof run;
    runtimeconfig: typeof runtimeconfig;
    safebrowsing: typeof safebrowsing;
    sasportal: typeof sasportal;
    script: typeof script;
    searchads360: typeof searchads360;
    searchconsole: typeof searchconsole;
    secretmanager: typeof secretmanager;
    securitycenter: typeof securitycenter;
    securityposture: typeof securityposture;
    serviceconsumermanagement: typeof serviceconsumermanagement;
    servicecontrol: typeof servicecontrol;
    servicedirectory: typeof servicedirectory;
    servicemanagement: typeof servicemanagement;
    servicenetworking: typeof servicenetworking;
    serviceusage: typeof serviceusage;
    sheets: typeof sheets;
    siteVerification: typeof siteVerification;
    slides: typeof slides;
    smartdevicemanagement: typeof smartdevicemanagement;
    solar: typeof solar;
    sourcerepo: typeof sourcerepo;
    spanner: typeof spanner;
    speech: typeof speech;
    sql: typeof sql;
    sqladmin: typeof sqladmin;
    storage: typeof storage;
    storagebatchoperations: typeof storagebatchoperations;
    storagetransfer: typeof storagetransfer;
    streetviewpublish: typeof streetviewpublish;
    sts: typeof sts;
    tagmanager: typeof tagmanager;
    tasks: typeof tasks;
    testing: typeof testing;
    texttospeech: typeof texttospeech;
    toolresults: typeof toolresults;
    tpu: typeof tpu;
    trafficdirector: typeof trafficdirector;
    transcoder: typeof transcoder;
    translate: typeof translate;
    travelimpactmodel: typeof travelimpactmodel;
    vault: typeof vault;
    vectortile: typeof vectortile;
    verifiedaccess: typeof verifiedaccess;
    versionhistory: typeof versionhistory;
    videointelligence: typeof videointelligence;
    vision: typeof vision;
    vmmigration: typeof vmmigration;
    vmwareengine: typeof vmwareengine;
    vpcaccess: typeof vpcaccess;
    walletobjects: typeof walletobjects;
    webfonts: typeof webfonts;
    webmasters: typeof webmasters;
    webrisk: typeof webrisk;
    websecurityscanner: typeof websecurityscanner;
    workflowexecutions: typeof workflowexecutions;
    workflows: typeof workflows;
    workloadmanager: typeof workloadmanager;
    workspaceevents: typeof workspaceevents;
    workstations: typeof workstations;
    youtube: typeof youtube;
    youtubeAnalytics: typeof youtubeAnalytics;
    youtubereporting: typeof youtubereporting;
}
