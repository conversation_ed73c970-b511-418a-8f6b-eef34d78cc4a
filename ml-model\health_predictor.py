#!/usr/bin/env python3
"""
Health Predictor ML Model
Analyzes fitness tracker data to predict health risks and provide recommendations
"""

import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Mock ML model for demonstration
# In production, you would use trained models like scikit-learn, TensorFlow, etc.

class HealthPredictor:
    def __init__(self):
        self.model_version = "1.0.0"
        self.features = [
            'age', 'gender', 'bmi', 'resting_heart_rate', 'avg_heart_rate',
            'steps', 'active_minutes', 'sleep_duration', 'sleep_efficiency',
            'stress_level', 'mood', 'energy_level'
        ]
        
    def preprocess_data(self, health_data, user_data):
        """Preprocess health data for prediction"""
        try:
            # Calculate averages from recent health data
            recent_data = health_data[-7:] if len(health_data) >= 7 else health_data
            
            if not recent_data:
                return None
                
            # Extract features
            features = {
                'age': user_data.get('age', 30),
                'gender': 1 if user_data.get('gender') == 'male' else 0,
                'bmi': user_data.get('bmi', 25),
                'resting_heart_rate': np.mean([d.get('heartRate', {}).get('resting', 70) for d in recent_data if d.get('heartRate', {}).get('resting')]) or 70,
                'avg_heart_rate': np.mean([d.get('heartRate', {}).get('average', 80) for d in recent_data if d.get('heartRate', {}).get('average')]) or 80,
                'steps': np.mean([d.get('steps', 0) for d in recent_data]),
                'active_minutes': np.mean([d.get('activeMinutes', 0) for d in recent_data]),
                'sleep_duration': np.mean([d.get('sleep', {}).get('totalSleep', 480) for d in recent_data if d.get('sleep', {}).get('totalSleep')]) or 480,
                'sleep_efficiency': np.mean([d.get('sleep', {}).get('sleepEfficiency', 85) for d in recent_data if d.get('sleep', {}).get('sleepEfficiency')]) or 85,
                'stress_level': np.mean([d.get('stress', {}).get('level', 5) for d in recent_data if d.get('stress', {}).get('level')]) or 5,
                'mood': np.mean([d.get('mood', 7) for d in recent_data if d.get('mood')]) or 7,
                'energy_level': np.mean([d.get('energyLevel', 7) for d in recent_data if d.get('energyLevel')]) or 7
            }
            
            return features
        except Exception as e:
            print(f"Error in preprocessing: {str(e)}", file=sys.stderr)
            return None
    
    def predict_cardiovascular_risk(self, features):
        """Predict cardiovascular disease risk"""
        try:
            # Simple rule-based model (replace with trained ML model)
            risk_score = 0
            
            # Age factor
            if features['age'] > 65:
                risk_score += 3
            elif features['age'] > 45:
                risk_score += 2
            elif features['age'] > 35:
                risk_score += 1
                
            # BMI factor
            if features['bmi'] > 30:
                risk_score += 3
            elif features['bmi'] > 25:
                risk_score += 1
                
            # Heart rate factors
            if features['resting_heart_rate'] > 100:
                risk_score += 2
            elif features['resting_heart_rate'] < 50:
                risk_score += 1
                
            # Activity factors
            if features['steps'] < 5000:
                risk_score += 2
            elif features['steps'] < 8000:
                risk_score += 1
                
            if features['active_minutes'] < 30:
                risk_score += 1
                
            # Sleep factors
            if features['sleep_duration'] < 360 or features['sleep_duration'] > 600:  # 6-10 hours
                risk_score += 1
                
            if features['sleep_efficiency'] < 80:
                risk_score += 1
                
            # Stress factor
            if features['stress_level'] > 7:
                risk_score += 2
            elif features['stress_level'] > 5:
                risk_score += 1
                
            # Determine risk level
            if risk_score >= 8:
                risk_level = 'very_high'
                confidence = 85
            elif risk_score >= 6:
                risk_level = 'high'
                confidence = 80
            elif risk_score >= 4:
                risk_level = 'moderate'
                confidence = 75
            elif risk_score >= 2:
                risk_level = 'low'
                confidence = 70
            else:
                risk_level = 'very_low'
                confidence = 65
                
            return {
                'condition': 'Cardiovascular Disease',
                'riskLevel': risk_level,
                'confidence': confidence,
                'probability': min(risk_score / 10.0, 0.9),
                'description': f'Based on your health metrics, your cardiovascular risk is {risk_level}.',
                'recommendations': self.get_cardiovascular_recommendations(risk_level, features),
                'urgency': 'high' if risk_level in ['high', 'very_high'] else 'medium' if risk_level == 'moderate' else 'low'
            }
        except Exception as e:
            print(f"Error in cardiovascular prediction: {str(e)}", file=sys.stderr)
            return None
    
    def predict_diabetes_risk(self, features):
        """Predict diabetes risk"""
        try:
            risk_score = 0
            
            # Age factor
            if features['age'] > 45:
                risk_score += 2
            elif features['age'] > 35:
                risk_score += 1
                
            # BMI factor (major risk factor)
            if features['bmi'] > 30:
                risk_score += 4
            elif features['bmi'] > 25:
                risk_score += 2
                
            # Activity factors
            if features['steps'] < 5000:
                risk_score += 2
            if features['active_minutes'] < 150:  # per week equivalent
                risk_score += 1
                
            # Sleep factors
            if features['sleep_duration'] < 360 or features['sleep_duration'] > 540:
                risk_score += 1
                
            # Stress factor
            if features['stress_level'] > 6:
                risk_score += 1
                
            # Determine risk level
            if risk_score >= 7:
                risk_level = 'very_high'
                confidence = 82
            elif risk_score >= 5:
                risk_level = 'high'
                confidence = 78
            elif risk_score >= 3:
                risk_level = 'moderate'
                confidence = 72
            elif risk_score >= 1:
                risk_level = 'low'
                confidence = 68
            else:
                risk_level = 'very_low'
                confidence = 65
                
            return {
                'condition': 'Type 2 Diabetes',
                'riskLevel': risk_level,
                'confidence': confidence,
                'probability': min(risk_score / 8.0, 0.85),
                'description': f'Your diabetes risk assessment indicates {risk_level} risk.',
                'recommendations': self.get_diabetes_recommendations(risk_level, features),
                'urgency': 'high' if risk_level in ['high', 'very_high'] else 'medium' if risk_level == 'moderate' else 'low'
            }
        except Exception as e:
            print(f"Error in diabetes prediction: {str(e)}", file=sys.stderr)
            return None
    
    def analyze_sleep_quality(self, features):
        """Analyze sleep quality and patterns"""
        try:
            sleep_score = 0
            
            # Sleep duration (7-9 hours optimal)
            if 420 <= features['sleep_duration'] <= 540:  # 7-9 hours
                sleep_score += 3
            elif 360 <= features['sleep_duration'] <= 600:  # 6-10 hours
                sleep_score += 2
            else:
                sleep_score += 0
                
            # Sleep efficiency
            if features['sleep_efficiency'] >= 90:
                sleep_score += 3
            elif features['sleep_efficiency'] >= 80:
                sleep_score += 2
            elif features['sleep_efficiency'] >= 70:
                sleep_score += 1
                
            # Stress impact on sleep
            if features['stress_level'] <= 3:
                sleep_score += 1
            elif features['stress_level'] >= 7:
                sleep_score -= 1
                
            # Energy level correlation
            if features['energy_level'] >= 8:
                sleep_score += 1
            elif features['energy_level'] <= 4:
                sleep_score -= 1
                
            # Determine sleep quality
            if sleep_score >= 6:
                quality = 'excellent'
                risk_level = 'very_low'
            elif sleep_score >= 4:
                quality = 'good'
                risk_level = 'low'
            elif sleep_score >= 2:
                quality = 'fair'
                risk_level = 'moderate'
            else:
                quality = 'poor'
                risk_level = 'high'
                
            return {
                'condition': 'Sleep Quality Issues',
                'riskLevel': risk_level,
                'confidence': 88,
                'probability': max(0, (6 - sleep_score) / 6.0),
                'description': f'Your sleep quality is assessed as {quality}.',
                'recommendations': self.get_sleep_recommendations(quality, features),
                'urgency': 'medium' if risk_level in ['high', 'moderate'] else 'low'
            }
        except Exception as e:
            print(f"Error in sleep analysis: {str(e)}", file=sys.stderr)
            return None
    
    def get_cardiovascular_recommendations(self, risk_level, features):
        """Get cardiovascular health recommendations"""
        recommendations = []
        
        if features['steps'] < 8000:
            recommendations.append("Increase daily steps to at least 8,000-10,000")
        if features['active_minutes'] < 150:
            recommendations.append("Aim for 150 minutes of moderate exercise per week")
        if features['bmi'] > 25:
            recommendations.append("Consider weight management through diet and exercise")
        if features['stress_level'] > 6:
            recommendations.append("Practice stress reduction techniques like meditation")
        if features['sleep_duration'] < 420:
            recommendations.append("Improve sleep quality - aim for 7-9 hours nightly")
            
        if risk_level in ['high', 'very_high']:
            recommendations.append("Consult with a healthcare provider for comprehensive evaluation")
            recommendations.append("Consider regular blood pressure and cholesterol monitoring")
            
        return recommendations
    
    def get_diabetes_recommendations(self, risk_level, features):
        """Get diabetes prevention recommendations"""
        recommendations = []
        
        if features['bmi'] > 25:
            recommendations.append("Focus on healthy weight loss through balanced diet")
        if features['steps'] < 8000:
            recommendations.append("Increase physical activity - aim for 10,000 steps daily")
        if features['active_minutes'] < 150:
            recommendations.append("Include regular aerobic and resistance training")
            
        recommendations.append("Monitor carbohydrate intake and choose complex carbs")
        recommendations.append("Stay hydrated and limit sugary beverages")
        
        if risk_level in ['high', 'very_high']:
            recommendations.append("Schedule regular blood glucose screening")
            recommendations.append("Consult with a healthcare provider or nutritionist")
            
        return recommendations
    
    def get_sleep_recommendations(self, quality, features):
        """Get sleep improvement recommendations"""
        recommendations = []
        
        if features['sleep_duration'] < 420:
            recommendations.append("Extend sleep duration - aim for 7-9 hours nightly")
        if features['sleep_efficiency'] < 85:
            recommendations.append("Improve sleep environment - dark, cool, quiet room")
        if features['stress_level'] > 5:
            recommendations.append("Practice relaxation techniques before bedtime")
            
        recommendations.append("Maintain consistent sleep schedule")
        recommendations.append("Limit screen time 1 hour before bed")
        recommendations.append("Avoid caffeine and large meals close to bedtime")
        
        if quality == 'poor':
            recommendations.append("Consider consulting a sleep specialist")
            
        return recommendations
    
    def predict(self, health_data, user_data, prediction_type='comprehensive'):
        """Main prediction function"""
        try:
            features = self.preprocess_data(health_data, user_data)
            if not features:
                return {'error': 'Insufficient data for prediction'}
            
            predictions = []
            
            if prediction_type in ['comprehensive', 'disease_risk']:
                # Cardiovascular prediction
                cv_pred = self.predict_cardiovascular_risk(features)
                if cv_pred:
                    predictions.append(cv_pred)
                
                # Diabetes prediction
                diabetes_pred = self.predict_diabetes_risk(features)
                if diabetes_pred:
                    predictions.append(diabetes_pred)
            
            if prediction_type in ['comprehensive', 'sleep_analysis']:
                # Sleep analysis
                sleep_pred = self.analyze_sleep_quality(features)
                if sleep_pred:
                    predictions.append(sleep_pred)
            
            # Generate overall recommendations
            recommendations = self.generate_overall_recommendations(features, predictions)
            
            return {
                'predictions': predictions,
                'recommendations': recommendations,
                'model_info': {
                    'modelName': 'HealthConnect Predictor',
                    'modelVersion': self.model_version,
                    'algorithm': 'Rule-based with statistical analysis',
                    'features': self.features
                },
                'input_features': features
            }
            
        except Exception as e:
            print(f"Error in prediction: {str(e)}", file=sys.stderr)
            return {'error': f'Prediction failed: {str(e)}'}
    
    def generate_overall_recommendations(self, features, predictions):
        """Generate overall health recommendations"""
        recommendations = []
        
        # Activity recommendations
        if features['steps'] < 8000:
            recommendations.append({
                'category': 'exercise',
                'priority': 'high',
                'title': 'Increase Daily Activity',
                'description': 'Your daily step count is below recommended levels',
                'actionItems': ['Take stairs instead of elevators', 'Walk during lunch breaks', 'Set hourly movement reminders'],
                'expectedBenefit': 'Improved cardiovascular health and weight management',
                'timeframe': '2-4 weeks'
            })
        
        # Sleep recommendations
        if features['sleep_duration'] < 420 or features['sleep_efficiency'] < 80:
            recommendations.append({
                'category': 'sleep',
                'priority': 'medium',
                'title': 'Optimize Sleep Quality',
                'description': 'Your sleep patterns could be improved for better health',
                'actionItems': ['Maintain consistent bedtime', 'Create relaxing bedtime routine', 'Limit screen time before bed'],
                'expectedBenefit': 'Better energy, mood, and immune function',
                'timeframe': '1-2 weeks'
            })
        
        # Stress management
        if features['stress_level'] > 6:
            recommendations.append({
                'category': 'stress_management',
                'priority': 'high',
                'title': 'Manage Stress Levels',
                'description': 'High stress levels can impact overall health',
                'actionItems': ['Practice daily meditation', 'Try deep breathing exercises', 'Consider yoga or tai chi'],
                'expectedBenefit': 'Reduced risk of chronic diseases and improved mental health',
                'timeframe': '2-6 weeks'
            })
        
        # Check for high-risk predictions
        high_risk_conditions = [p for p in predictions if p.get('riskLevel') in ['high', 'very_high']]
        if high_risk_conditions:
            recommendations.append({
                'category': 'medical_consultation',
                'priority': 'high',
                'title': 'Consult Healthcare Provider',
                'description': 'Your health metrics suggest elevated risk factors',
                'actionItems': ['Schedule comprehensive health checkup', 'Discuss risk factors with doctor', 'Consider preventive screenings'],
                'expectedBenefit': 'Early detection and prevention of health issues',
                'timeframe': '1-2 weeks'
            })
        
        return recommendations

def main():
    """Main function to handle command line execution"""
    try:
        if len(sys.argv) < 2:
            print(json.dumps({'error': 'No input data provided'}))
            return
        
        input_data = json.loads(sys.argv[1])
        health_data = input_data.get('healthData', [])
        user_data = input_data.get('userData', {})
        prediction_type = input_data.get('predictionType', 'comprehensive')
        
        predictor = HealthPredictor()
        result = predictor.predict(health_data, user_data, prediction_type)
        
        print(json.dumps(result))
        
    except Exception as e:
        print(json.dumps({'error': f'Script execution failed: {str(e)}'}), file=sys.stderr)

if __name__ == '__main__':
    main()
