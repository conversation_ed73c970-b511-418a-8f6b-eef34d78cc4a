const mongoose = require('mongoose');

const predictionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  predictionType: {
    type: String,
    enum: ['disease_risk', 'health_trend', 'fitness_recommendation', 'sleep_analysis', 'stress_level'],
    required: true
  },
  inputData: {
    // Health metrics used for prediction
    heartRate: {
      resting: Number,
      average: Number,
      variability: Number
    },
    activity: {
      steps: Number,
      activeMinutes: Number,
      caloriesBurned: Number
    },
    sleep: {
      duration: Number,
      efficiency: Number,
      deepSleepPercentage: Number
    },
    vitals: {
      bloodPressure: {
        systolic: Number,
        diastolic: Number
      },
      bloodOxygen: Number,
      weight: Number,
      bmi: Number
    },
    lifestyle: {
      stressLevel: Number,
      mood: Number,
      energyLevel: Number,
      waterIntake: Number
    },
    demographics: {
      age: Number,
      gender: String,
      activityLevel: String
    },
    timeframe: {
      startDate: Date,
      endDate: Date,
      dataPoints: Number
    }
  },
  predictions: [{
    condition: {
      type: String,
      required: true
    },
    riskLevel: {
      type: String,
      enum: ['very_low', 'low', 'moderate', 'high', 'very_high'],
      required: true
    },
    confidence: {
      type: Number,
      min: 0,
      max: 100,
      required: true
    },
    probability: {
      type: Number,
      min: 0,
      max: 1
    },
    description: String,
    recommendations: [String],
    urgency: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'low'
    }
  }],
  trends: [{
    metric: String,
    direction: {
      type: String,
      enum: ['improving', 'stable', 'declining']
    },
    changePercentage: Number,
    significance: {
      type: String,
      enum: ['low', 'medium', 'high']
    },
    description: String
  }],
  recommendations: [{
    category: {
      type: String,
      enum: ['exercise', 'nutrition', 'sleep', 'stress_management', 'medical_consultation']
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    title: String,
    description: String,
    actionItems: [String],
    expectedBenefit: String,
    timeframe: String
  }],
  modelInfo: {
    modelName: String,
    modelVersion: String,
    algorithm: String,
    trainingDate: Date,
    accuracy: Number,
    features: [String]
  },
  status: {
    type: String,
    enum: ['processing', 'completed', 'failed'],
    default: 'processing'
  },
  processingTime: {
    type: Number, // in milliseconds
    default: 0
  },
  error: {
    message: String,
    code: String,
    timestamp: Date
  },
  feedback: {
    accuracy: {
      type: Number,
      min: 1,
      max: 5
    },
    usefulness: {
      type: Number,
      min: 1,
      max: 5
    },
    comments: String,
    submittedAt: Date
  },
  isArchived: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Indexes
predictionSchema.index({ userId: 1, createdAt: -1 });
predictionSchema.index({ userId: 1, predictionType: 1 });
predictionSchema.index({ status: 1, createdAt: -1 });

// Virtual for overall risk level
predictionSchema.virtual('overallRiskLevel').get(function() {
  if (!this.predictions || this.predictions.length === 0) return 'unknown';
  
  const riskLevels = ['very_low', 'low', 'moderate', 'high', 'very_high'];
  let maxRiskIndex = 0;
  
  this.predictions.forEach(prediction => {
    const riskIndex = riskLevels.indexOf(prediction.riskLevel);
    if (riskIndex > maxRiskIndex) {
      maxRiskIndex = riskIndex;
    }
  });
  
  return riskLevels[maxRiskIndex];
});

// Virtual for high priority recommendations
predictionSchema.virtual('highPriorityRecommendations').get(function() {
  return this.recommendations.filter(rec => rec.priority === 'high');
});

// Method to add feedback
predictionSchema.methods.addFeedback = function(accuracy, usefulness, comments = '') {
  this.feedback = {
    accuracy: accuracy,
    usefulness: usefulness,
    comments: comments,
    submittedAt: new Date()
  };
  return this.save();
};

// Static method to get user's latest predictions
predictionSchema.statics.getLatestPredictions = function(userId, limit = 10) {
  return this.find({
    userId: userId,
    status: 'completed',
    isArchived: false
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .populate('userId', 'name email');
};

// Static method to get prediction statistics
predictionSchema.statics.getPredictionStats = async function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return await this.aggregate([
    {
      $match: {
        userId: mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$predictionType',
        count: { $sum: 1 },
        avgProcessingTime: { $avg: '$processingTime' },
        avgConfidence: { $avg: { $avg: '$predictions.confidence' } }
      }
    }
  ]);
};

// Method to check if prediction needs update
predictionSchema.methods.needsUpdate = function(hours = 24) {
  const hoursAgo = new Date();
  hoursAgo.setHours(hoursAgo.getHours() - hours);
  return this.createdAt < hoursAgo;
};

module.exports = mongoose.model('Prediction', predictionSchema);
