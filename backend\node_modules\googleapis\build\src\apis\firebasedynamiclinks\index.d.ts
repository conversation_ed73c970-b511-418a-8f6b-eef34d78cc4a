/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebasedynamiclinks_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof firebasedynamiclinks_v1.Firebasedynamiclinks;
};
export declare function firebasedynamiclinks(version: 'v1'): firebasedynamiclinks_v1.Firebasedynamiclinks;
export declare function firebasedynamiclinks(options: firebasedynamiclinks_v1.Options): firebasedynamiclinks_v1.Firebasedynamiclinks;
declare const auth: AuthPlus;
export { auth };
export { firebasedynamiclinks_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
