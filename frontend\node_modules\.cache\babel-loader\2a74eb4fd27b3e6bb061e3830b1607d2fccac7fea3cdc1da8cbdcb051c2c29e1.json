{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\GoalProgress.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoalProgress = ({\n  goals = []\n}) => {\n  _s();\n  const {\n    updateGoalProgress\n  } = useHealthData();\n  const handleProgressUpdate = async (goalId, newValue) => {\n    await updateGoalProgress(goalId, newValue);\n  };\n  const getRiskLevelColor = riskLevel => {\n    switch (riskLevel) {\n      case 'low':\n      case 'very_low':\n        return '#27ae60';\n      case 'moderate':\n        return '#f39c12';\n      case 'high':\n      case 'very_high':\n        return '#e74c3c';\n      default:\n        return '#667eea';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return '#e74c3c';\n      case 'medium':\n        return '#f39c12';\n      case 'low':\n        return '#27ae60';\n      default:\n        return '#667eea';\n    }\n  };\n  if (goals.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-goals\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-goals-icon\",\n        children: \"\\uD83C\\uDFAF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No Active Goals\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Set some health goals to track your progress!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary btn-small\",\n        children: \"Create Your First Goal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"goal-progress\",\n    children: [goals.slice(0, 5).map(goal => {\n      var _goal$current, _goal$target, _goal$current2, _goal$current2$value, _goal$target2, _goal$target3, _goal$target3$value, _goal$target4;\n      const progressPercentage = Math.min(((_goal$current = goal.current) === null || _goal$current === void 0 ? void 0 : _goal$current.value) / ((_goal$target = goal.target) === null || _goal$target === void 0 ? void 0 : _goal$target.value) * 100 || 0, 100);\n      const isCompleted = progressPercentage >= 100;\n      const daysRemaining = goal.daysRemaining || 0;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"goal-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"goal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"goal-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"goal-title\",\n              children: goal.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"goal-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"goal-category\",\n                children: goal.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"goal-type\",\n                children: goal.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), daysRemaining > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"goal-deadline\",\n                children: [daysRemaining, \" days left\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"goal-priority\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"priority-badge\",\n              style: {\n                backgroundColor: getPriorityColor(goal.priority),\n                color: 'white'\n              },\n              children: goal.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"goal-progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"progress-current\",\n              children: [((_goal$current2 = goal.current) === null || _goal$current2 === void 0 ? void 0 : (_goal$current2$value = _goal$current2.value) === null || _goal$current2$value === void 0 ? void 0 : _goal$current2$value.toLocaleString()) || 0, \" \", (_goal$target2 = goal.target) === null || _goal$target2 === void 0 ? void 0 : _goal$target2.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"progress-target\",\n              children: [\"/ \", (_goal$target3 = goal.target) === null || _goal$target3 === void 0 ? void 0 : (_goal$target3$value = _goal$target3.value) === null || _goal$target3$value === void 0 ? void 0 : _goal$target3$value.toLocaleString(), \" \", (_goal$target4 = goal.target) === null || _goal$target4 === void 0 ? void 0 : _goal$target4.unit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `progress-percentage ${isCompleted ? 'completed' : ''}`,\n              children: [Math.round(progressPercentage), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${progressPercentage}%`,\n                backgroundColor: isCompleted ? '#27ae60' : '#667eea'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this), goal.milestones && goal.milestones.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"goal-milestones\",\n          children: goal.milestones.map((milestone, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `milestone ${milestone.achieved ? 'achieved' : ''}`,\n            title: milestone.reward,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"milestone-percentage\",\n              children: [milestone.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 21\n            }, this), milestone.achieved && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"milestone-check\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 23\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 15\n        }, this), isCompleted && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"goal-completed\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"completion-icon\",\n            children: \"\\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"completion-text\",\n            children: \"Goal Completed!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this)]\n      }, goal._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .goal-progress {\n          display: flex;\n          flex-direction: column;\n          gap: 20px;\n        }\n\n        .no-goals {\n          text-align: center;\n          padding: 40px 20px;\n          color: #7f8c8d;\n        }\n\n        .no-goals-icon {\n          font-size: 3rem;\n          margin-bottom: 15px;\n        }\n\n        .no-goals h3 {\n          margin: 0 0 10px 0;\n          color: #2c3e50;\n        }\n\n        .no-goals p {\n          margin: 0 0 20px 0;\n        }\n\n        .goal-item {\n          background: rgba(102, 126, 234, 0.05);\n          border: 1px solid rgba(102, 126, 234, 0.1);\n          border-radius: 12px;\n          padding: 20px;\n          transition: all 0.3s ease;\n        }\n\n        .goal-item:hover {\n          background: rgba(102, 126, 234, 0.08);\n          border-color: rgba(102, 126, 234, 0.2);\n        }\n\n        .goal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 15px;\n        }\n\n        .goal-info {\n          flex: 1;\n        }\n\n        .goal-title {\n          margin: 0 0 8px 0;\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .goal-meta {\n          margin: 0;\n          font-size: 0.85rem;\n          color: #7f8c8d;\n          display: flex;\n          gap: 10px;\n          flex-wrap: wrap;\n        }\n\n        .goal-category,\n        .goal-type,\n        .goal-deadline {\n          background: rgba(255, 255, 255, 0.8);\n          padding: 2px 8px;\n          border-radius: 4px;\n          font-size: 0.8rem;\n        }\n\n        .goal-deadline {\n          background: rgba(243, 156, 18, 0.1);\n          color: #f39c12;\n        }\n\n        .priority-badge {\n          padding: 4px 8px;\n          border-radius: 4px;\n          font-size: 0.75rem;\n          font-weight: 600;\n          text-transform: uppercase;\n        }\n\n        .goal-progress-section {\n          margin-bottom: 15px;\n        }\n\n        .progress-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .progress-current {\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .progress-target {\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .progress-percentage {\n          font-weight: 600;\n          color: #667eea;\n        }\n\n        .progress-percentage.completed {\n          color: #27ae60;\n        }\n\n        .progress-bar {\n          width: 100%;\n          height: 8px;\n          background: rgba(0, 0, 0, 0.1);\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .progress-fill {\n          height: 100%;\n          border-radius: 4px;\n          transition: width 0.6s ease;\n        }\n\n        .goal-milestones {\n          display: flex;\n          gap: 8px;\n          margin-bottom: 10px;\n        }\n\n        .milestone {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: rgba(0, 0, 0, 0.1);\n          font-size: 0.7rem;\n          font-weight: 600;\n          color: #7f8c8d;\n          position: relative;\n          transition: all 0.3s ease;\n        }\n\n        .milestone.achieved {\n          background: #27ae60;\n          color: white;\n        }\n\n        .milestone-check {\n          position: absolute;\n          top: -2px;\n          right: -2px;\n          background: #27ae60;\n          color: white;\n          border-radius: 50%;\n          width: 16px;\n          height: 16px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.6rem;\n        }\n\n        .goal-completed {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 8px;\n          padding: 10px;\n          background: rgba(39, 174, 96, 0.1);\n          border: 1px solid rgba(39, 174, 96, 0.2);\n          border-radius: 8px;\n          color: #27ae60;\n          font-weight: 600;\n        }\n\n        .completion-icon {\n          font-size: 1.2rem;\n        }\n\n        @media (max-width: 768px) {\n          .goal-header {\n            flex-direction: column;\n            gap: 10px;\n          }\n\n          .goal-meta {\n            flex-direction: column;\n            gap: 5px;\n          }\n\n          .progress-info {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 5px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(GoalProgress, \"vAePo8VwktnF5d2ocNYnIGwuwrc=\", false, function () {\n  return [useHealthData];\n});\n_c = GoalProgress;\nexport default GoalProgress;\nvar _c;\n$RefreshReg$(_c, \"GoalProgress\");", "map": {"version": 3, "names": ["React", "useHealthData", "jsxDEV", "_jsxDEV", "GoalProgress", "goals", "_s", "updateGoalProgress", "handleProgressUpdate", "goalId", "newValue", "getRiskLevelColor", "riskLevel", "getPriorityColor", "priority", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "map", "goal", "_goal$current", "_goal$target", "_goal$current2", "_goal$current2$value", "_goal$target2", "_goal$target3", "_goal$target3$value", "_goal$target4", "progressPercentage", "Math", "min", "current", "value", "target", "isCompleted", "daysRemaining", "title", "category", "type", "style", "backgroundColor", "color", "toLocaleString", "unit", "round", "width", "milestones", "milestone", "index", "achieved", "reward", "percentage", "_id", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/GoalProgress.js"], "sourcesContent": ["import React from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst GoalProgress = ({ goals = [] }) => {\n  const { updateGoalProgress } = useHealthData();\n\n  const handleProgressUpdate = async (goalId, newValue) => {\n    await updateGoalProgress(goalId, newValue);\n  };\n\n  const getRiskLevelColor = (riskLevel) => {\n    switch (riskLevel) {\n      case 'low':\n      case 'very_low':\n        return '#27ae60';\n      case 'moderate':\n        return '#f39c12';\n      case 'high':\n      case 'very_high':\n        return '#e74c3c';\n      default:\n        return '#667eea';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high':\n        return '#e74c3c';\n      case 'medium':\n        return '#f39c12';\n      case 'low':\n        return '#27ae60';\n      default:\n        return '#667eea';\n    }\n  };\n\n  if (goals.length === 0) {\n    return (\n      <div className=\"no-goals\">\n        <div className=\"no-goals-icon\">🎯</div>\n        <h3>No Active Goals</h3>\n        <p>Set some health goals to track your progress!</p>\n        <button className=\"btn btn-primary btn-small\">\n          Create Your First Goal\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"goal-progress\">\n      {goals.slice(0, 5).map((goal) => {\n        const progressPercentage = Math.min(\n          (goal.current?.value / goal.target?.value) * 100 || 0,\n          100\n        );\n        const isCompleted = progressPercentage >= 100;\n        const daysRemaining = goal.daysRemaining || 0;\n\n        return (\n          <div key={goal._id} className=\"goal-item\">\n            <div className=\"goal-header\">\n              <div className=\"goal-info\">\n                <h4 className=\"goal-title\">{goal.title}</h4>\n                <p className=\"goal-meta\">\n                  <span className=\"goal-category\">{goal.category}</span>\n                  <span className=\"goal-type\">{goal.type}</span>\n                  {daysRemaining > 0 && (\n                    <span className=\"goal-deadline\">\n                      {daysRemaining} days left\n                    </span>\n                  )}\n                </p>\n              </div>\n              <div className=\"goal-priority\">\n                <span \n                  className=\"priority-badge\"\n                  style={{ \n                    backgroundColor: getPriorityColor(goal.priority),\n                    color: 'white'\n                  }}\n                >\n                  {goal.priority}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"goal-progress-section\">\n              <div className=\"progress-info\">\n                <span className=\"progress-current\">\n                  {goal.current?.value?.toLocaleString() || 0} {goal.target?.unit}\n                </span>\n                <span className=\"progress-target\">\n                  / {goal.target?.value?.toLocaleString()} {goal.target?.unit}\n                </span>\n                <span className={`progress-percentage ${isCompleted ? 'completed' : ''}`}>\n                  {Math.round(progressPercentage)}%\n                </span>\n              </div>\n\n              <div className=\"progress-bar\">\n                <div \n                  className=\"progress-fill\"\n                  style={{ \n                    width: `${progressPercentage}%`,\n                    backgroundColor: isCompleted ? '#27ae60' : '#667eea'\n                  }}\n                />\n              </div>\n            </div>\n\n            {goal.milestones && goal.milestones.length > 0 && (\n              <div className=\"goal-milestones\">\n                {goal.milestones.map((milestone, index) => (\n                  <div \n                    key={index}\n                    className={`milestone ${milestone.achieved ? 'achieved' : ''}`}\n                    title={milestone.reward}\n                  >\n                    <span className=\"milestone-percentage\">\n                      {milestone.percentage}%\n                    </span>\n                    {milestone.achieved && (\n                      <span className=\"milestone-check\">✓</span>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {isCompleted && (\n              <div className=\"goal-completed\">\n                <span className=\"completion-icon\">🎉</span>\n                <span className=\"completion-text\">Goal Completed!</span>\n              </div>\n            )}\n          </div>\n        );\n      })}\n\n      <style jsx>{`\n        .goal-progress {\n          display: flex;\n          flex-direction: column;\n          gap: 20px;\n        }\n\n        .no-goals {\n          text-align: center;\n          padding: 40px 20px;\n          color: #7f8c8d;\n        }\n\n        .no-goals-icon {\n          font-size: 3rem;\n          margin-bottom: 15px;\n        }\n\n        .no-goals h3 {\n          margin: 0 0 10px 0;\n          color: #2c3e50;\n        }\n\n        .no-goals p {\n          margin: 0 0 20px 0;\n        }\n\n        .goal-item {\n          background: rgba(102, 126, 234, 0.05);\n          border: 1px solid rgba(102, 126, 234, 0.1);\n          border-radius: 12px;\n          padding: 20px;\n          transition: all 0.3s ease;\n        }\n\n        .goal-item:hover {\n          background: rgba(102, 126, 234, 0.08);\n          border-color: rgba(102, 126, 234, 0.2);\n        }\n\n        .goal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 15px;\n        }\n\n        .goal-info {\n          flex: 1;\n        }\n\n        .goal-title {\n          margin: 0 0 8px 0;\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .goal-meta {\n          margin: 0;\n          font-size: 0.85rem;\n          color: #7f8c8d;\n          display: flex;\n          gap: 10px;\n          flex-wrap: wrap;\n        }\n\n        .goal-category,\n        .goal-type,\n        .goal-deadline {\n          background: rgba(255, 255, 255, 0.8);\n          padding: 2px 8px;\n          border-radius: 4px;\n          font-size: 0.8rem;\n        }\n\n        .goal-deadline {\n          background: rgba(243, 156, 18, 0.1);\n          color: #f39c12;\n        }\n\n        .priority-badge {\n          padding: 4px 8px;\n          border-radius: 4px;\n          font-size: 0.75rem;\n          font-weight: 600;\n          text-transform: uppercase;\n        }\n\n        .goal-progress-section {\n          margin-bottom: 15px;\n        }\n\n        .progress-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .progress-current {\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .progress-target {\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .progress-percentage {\n          font-weight: 600;\n          color: #667eea;\n        }\n\n        .progress-percentage.completed {\n          color: #27ae60;\n        }\n\n        .progress-bar {\n          width: 100%;\n          height: 8px;\n          background: rgba(0, 0, 0, 0.1);\n          border-radius: 4px;\n          overflow: hidden;\n        }\n\n        .progress-fill {\n          height: 100%;\n          border-radius: 4px;\n          transition: width 0.6s ease;\n        }\n\n        .goal-milestones {\n          display: flex;\n          gap: 8px;\n          margin-bottom: 10px;\n        }\n\n        .milestone {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 40px;\n          height: 40px;\n          border-radius: 50%;\n          background: rgba(0, 0, 0, 0.1);\n          font-size: 0.7rem;\n          font-weight: 600;\n          color: #7f8c8d;\n          position: relative;\n          transition: all 0.3s ease;\n        }\n\n        .milestone.achieved {\n          background: #27ae60;\n          color: white;\n        }\n\n        .milestone-check {\n          position: absolute;\n          top: -2px;\n          right: -2px;\n          background: #27ae60;\n          color: white;\n          border-radius: 50%;\n          width: 16px;\n          height: 16px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.6rem;\n        }\n\n        .goal-completed {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 8px;\n          padding: 10px;\n          background: rgba(39, 174, 96, 0.1);\n          border: 1px solid rgba(39, 174, 96, 0.2);\n          border-radius: 8px;\n          color: #27ae60;\n          font-weight: 600;\n        }\n\n        .completion-icon {\n          font-size: 1.2rem;\n        }\n\n        @media (max-width: 768px) {\n          .goal-header {\n            flex-direction: column;\n            gap: 10px;\n          }\n\n          .goal-meta {\n            flex-direction: column;\n            gap: 5px;\n          }\n\n          .progress-info {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 5px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default GoalProgress;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAGA,CAAC;EAAEC,KAAK,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAmB,CAAC,GAAGN,aAAa,CAAC,CAAC;EAE9C,MAAMO,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,QAAQ,KAAK;IACvD,MAAMH,kBAAkB,CAACE,MAAM,EAAEC,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMC,iBAAiB,GAAIC,SAAS,IAAK;IACvC,QAAQA,SAAS;MACf,KAAK,KAAK;MACV,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,MAAM;MACX,KAAK,WAAW;QACd,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,KAAK;QACR,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAIT,KAAK,CAACU,MAAM,KAAK,CAAC,EAAE;IACtB,oBACEZ,OAAA;MAAKa,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBd,OAAA;QAAKa,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvClB,OAAA;QAAAc,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBlB,OAAA;QAAAc,QAAA,EAAG;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpDlB,OAAA;QAAQa,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACElB,OAAA;IAAKa,SAAS,EAAC,eAAe;IAAAC,QAAA,GAC3BZ,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAI,IAAK;MAAA,IAAAC,aAAA,EAAAC,YAAA,EAAAC,cAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,mBAAA,EAAAC,aAAA;MAC/B,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAChC,EAAAV,aAAA,GAAAD,IAAI,CAACY,OAAO,cAAAX,aAAA,uBAAZA,aAAA,CAAcY,KAAK,MAAAX,YAAA,GAAGF,IAAI,CAACc,MAAM,cAAAZ,YAAA,uBAAXA,YAAA,CAAaW,KAAK,IAAI,GAAG,IAAI,CAAC,EACrD,GACF,CAAC;MACD,MAAME,WAAW,GAAGN,kBAAkB,IAAI,GAAG;MAC7C,MAAMO,aAAa,GAAGhB,IAAI,CAACgB,aAAa,IAAI,CAAC;MAE7C,oBACErC,OAAA;QAAoBa,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACvCd,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1Bd,OAAA;YAAKa,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBd,OAAA;cAAIa,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEO,IAAI,CAACiB;YAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5ClB,OAAA;cAAGa,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBd,OAAA;gBAAMa,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEO,IAAI,CAACkB;cAAQ;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDlB,OAAA;gBAAMa,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEO,IAAI,CAACmB;cAAI;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC7CmB,aAAa,GAAG,CAAC,iBAChBrC,OAAA;gBAAMa,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAC5BuB,aAAa,EAAC,YACjB;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlB,OAAA;YAAKa,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5Bd,OAAA;cACEa,SAAS,EAAC,gBAAgB;cAC1B4B,KAAK,EAAE;gBACLC,eAAe,EAAEhC,gBAAgB,CAACW,IAAI,CAACV,QAAQ,CAAC;gBAChDgC,KAAK,EAAE;cACT,CAAE;cAAA7B,QAAA,EAEDO,IAAI,CAACV;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlB,OAAA;UAAKa,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCd,OAAA;YAAKa,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5Bd,OAAA;cAAMa,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAC/B,EAAAU,cAAA,GAAAH,IAAI,CAACY,OAAO,cAAAT,cAAA,wBAAAC,oBAAA,GAAZD,cAAA,CAAcU,KAAK,cAAAT,oBAAA,uBAAnBA,oBAAA,CAAqBmB,cAAc,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC,GAAAlB,aAAA,GAACL,IAAI,CAACc,MAAM,cAAAT,aAAA,uBAAXA,aAAA,CAAamB,IAAI;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACPlB,OAAA;cAAMa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,IAC9B,GAAAa,aAAA,GAACN,IAAI,CAACc,MAAM,cAAAR,aAAA,wBAAAC,mBAAA,GAAXD,aAAA,CAAaO,KAAK,cAAAN,mBAAA,uBAAlBA,mBAAA,CAAoBgB,cAAc,CAAC,CAAC,EAAC,GAAC,GAAAf,aAAA,GAACR,IAAI,CAACc,MAAM,cAAAN,aAAA,uBAAXA,aAAA,CAAagB,IAAI;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACPlB,OAAA;cAAMa,SAAS,EAAE,uBAAuBuB,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;cAAAtB,QAAA,GACtEiB,IAAI,CAACe,KAAK,CAAChB,kBAAkB,CAAC,EAAC,GAClC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENlB,OAAA;YAAKa,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3Bd,OAAA;cACEa,SAAS,EAAC,eAAe;cACzB4B,KAAK,EAAE;gBACLM,KAAK,EAAE,GAAGjB,kBAAkB,GAAG;gBAC/BY,eAAe,EAAEN,WAAW,GAAG,SAAS,GAAG;cAC7C;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELG,IAAI,CAAC2B,UAAU,IAAI3B,IAAI,CAAC2B,UAAU,CAACpC,MAAM,GAAG,CAAC,iBAC5CZ,OAAA;UAAKa,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BO,IAAI,CAAC2B,UAAU,CAAC5B,GAAG,CAAC,CAAC6B,SAAS,EAAEC,KAAK,kBACpClD,OAAA;YAEEa,SAAS,EAAE,aAAaoC,SAAS,CAACE,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/Db,KAAK,EAAEW,SAAS,CAACG,MAAO;YAAAtC,QAAA,gBAExBd,OAAA;cAAMa,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GACnCmC,SAAS,CAACI,UAAU,EAAC,GACxB;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACN+B,SAAS,CAACE,QAAQ,iBACjBnD,OAAA;cAAMa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC1C;UAAA,GATIgC,KAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAkB,WAAW,iBACVpC,OAAA;UAAKa,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7Bd,OAAA;YAAMa,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3ClB,OAAA;YAAMa,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CACN;MAAA,GA3EOG,IAAI,CAACiC,GAAG;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4Eb,CAAC;IAEV,CAAC,CAAC,eAEFlB,OAAA;MAAOuD,GAAG;MAAAzC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACf,EAAA,CA9VIF,YAAY;EAAA,QACeH,aAAa;AAAA;AAAA0D,EAAA,GADxCvD,YAAY;AAgWlB,eAAeA,YAAY;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}