const path = require('path');
const fs = require('fs');

// Check if we're running from the correct directory
const backendPath = path.join(__dirname, '../backend');
if (!fs.existsSync(path.join(backendPath, 'node_modules'))) {
  console.log('❌ Please run this script from the backend directory:');
  console.log('   cd backend && node ../database/seedDatabase.js');
  process.exit(1);
}

const mongoose = require(path.join(backendPath, 'node_modules/mongoose'));
const bcrypt = require(path.join(backendPath, 'node_modules/bcryptjs'));
require('dotenv').config({ path: path.join(backendPath, '.env') });

// Import models
const User = require('../backend/models/User');
const HealthData = require('../backend/models/HealthData');
const Goal = require('../backend/models/Goal');
const Prediction = require('../backend/models/Prediction');

// Import sample data
const sampleData = require('./sampleData');

async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/healthconnect', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await User.deleteMany({});
    await HealthData.deleteMany({});
    await Goal.deleteMany({});
    await Prediction.deleteMany({});

    // Create users
    console.log('👥 Creating users...');
    const users = [];
    
    for (const userData of sampleData.users) {
      // Hash password
      const salt = await bcrypt.genSalt(12);
      userData.password = await bcrypt.hash(userData.password, salt);
      
      const user = await User.create(userData);
      users.push(user);
      console.log(`   Created user: ${user.name} (${user.email})`);
    }

    // Create health data for each user
    console.log('💓 Creating health data...');
    for (const user of users) {
      const healthDataArray = sampleData.generateHealthData(user._id);
      await HealthData.insertMany(healthDataArray);
      console.log(`   Created ${healthDataArray.length} health data entries for ${user.name}`);
    }

    // Create goals for each user
    console.log('🎯 Creating goals...');
    for (const user of users) {
      const goalsArray = sampleData.generateGoals(user._id);
      await Goal.insertMany(goalsArray);
      console.log(`   Created ${goalsArray.length} goals for ${user.name}`);
    }

    // Create predictions for each user
    console.log('🔮 Creating predictions...');
    for (const user of users) {
      const predictionsArray = sampleData.generatePredictions(user._id);
      await Prediction.insertMany(predictionsArray);
      console.log(`   Created ${predictionsArray.length} predictions for ${user.name}`);
    }

    // Display summary
    console.log('\n📊 Database seeding completed successfully!');
    console.log('='.repeat(50));
    
    const userCount = await User.countDocuments();
    const healthDataCount = await HealthData.countDocuments();
    const goalCount = await Goal.countDocuments();
    const predictionCount = await Prediction.countDocuments();
    
    console.log(`👥 Users: ${userCount}`);
    console.log(`💓 Health Data entries: ${healthDataCount}`);
    console.log(`🎯 Goals: ${goalCount}`);
    console.log(`🔮 Predictions: ${predictionCount}`);
    
    console.log('\n🔑 Demo Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: demo123');
    
    console.log('\n🚀 You can now start the application!');
    console.log('Backend: cd backend && npm run dev');
    console.log('Frontend: cd frontend && npm start');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the seeding function
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
