<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Test HealthConnect Login</h1>
    <div id="result"></div>
    
    <script>
        async function testLogin() {
            try {
                console.log('Testing login...');
                
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });
                
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                document.getElementById('result').innerHTML = `
                    <h2>Login Test Result:</h2>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Success:</strong> ${data.success}</p>
                    <p><strong>Message:</strong> ${data.message || 'No message'}</p>
                    <p><strong>User:</strong> ${data.data?.user?.name || 'No user'}</p>
                    <p><strong>Token:</strong> ${data.data?.token ? 'Present' : 'Missing'}</p>
                `;
                
                if (data.success) {
                    // Test dashboard
                    const dashboardResponse = await fetch('http://localhost:5000/api/users/dashboard', {
                        headers: {
                            'Authorization': `Bearer ${data.data.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const dashboardData = await dashboardResponse.json();
                    console.log('Dashboard data:', dashboardData);
                    
                    document.getElementById('result').innerHTML += `
                        <h3>Dashboard Test:</h3>
                        <p><strong>Dashboard Status:</strong> ${dashboardResponse.status}</p>
                        <p><strong>Dashboard Success:</strong> ${dashboardData.success}</p>
                        <p><strong>Today's Steps:</strong> ${dashboardData.data?.today?.steps || 'No data'}</p>
                    `;
                }
                
            } catch (error) {
                console.error('Login test error:', error);
                document.getElementById('result').innerHTML = `
                    <h2>Login Test Error:</h2>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Run test when page loads
        testLogin();
    </script>
</body>
</html>
