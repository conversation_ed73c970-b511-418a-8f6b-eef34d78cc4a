{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\GoogleFitIntegration.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GoogleFitIntegration = () => {\n  _s();\n  const [status, setStatus] = useState({\n    connected: false,\n    loading: true,\n    lastSync: null\n  });\n  const [syncing, setSyncing] = useState(false);\n  const [syncResult, setSyncResult] = useState(null);\n  useEffect(() => {\n    checkConnectionStatus();\n  }, []);\n  const checkConnectionStatus = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/status');\n      setStatus({\n        connected: response.data.data.connected,\n        loading: false,\n        lastSync: response.data.data.lastSync,\n        connectedAt: response.data.data.connectedAt\n      });\n    } catch (error) {\n      console.error('Error checking Google Fit status:', error);\n      setStatus(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n  const connectGoogleFit = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/auth-url');\n      const authUrl = response.data.data.authUrl;\n\n      // Open Google authorization in new window\n      window.open(authUrl, 'google-fit-auth', 'width=500,height=600');\n\n      // Listen for authorization completion\n      window.addEventListener('message', handleAuthMessage);\n    } catch (error) {\n      console.error('Error getting auth URL:', error);\n      alert('Failed to connect to Google Fit. Please try again.');\n    }\n  };\n  const handleAuthMessage = async event => {\n    if (event.data.type === 'GOOGLE_FIT_AUTH_SUCCESS') {\n      const {\n        code\n      } = event.data;\n      try {\n        await axios.post('/api/google-fit/callback', {\n          code\n        });\n        await checkConnectionStatus();\n        alert('Google Fit connected successfully!');\n      } catch (error) {\n        console.error('Error handling auth callback:', error);\n        alert('Failed to complete Google Fit connection.');\n      }\n      window.removeEventListener('message', handleAuthMessage);\n    }\n  };\n  const syncData = async () => {\n    setSyncing(true);\n    setSyncResult(null);\n    try {\n      const endDate = new Date();\n      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Last 30 days\n\n      const response = await axios.post('/api/google-fit/sync', {\n        startDate: startDate.toISOString(),\n        endDate: endDate.toISOString()\n      });\n      setSyncResult({\n        success: true,\n        message: response.data.message,\n        syncedDays: response.data.data.syncedDays\n      });\n      await checkConnectionStatus();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error syncing data:', error);\n      setSyncResult({\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to sync data'\n      });\n    } finally {\n      setSyncing(false);\n    }\n  };\n  const disconnectGoogleFit = async () => {\n    if (window.confirm('Are you sure you want to disconnect Google Fit?')) {\n      try {\n        await axios.delete('/api/google-fit/disconnect');\n        await checkConnectionStatus();\n        setSyncResult(null);\n        alert('Google Fit disconnected successfully.');\n      } catch (error) {\n        console.error('Error disconnecting Google Fit:', error);\n        alert('Failed to disconnect Google Fit.');\n      }\n    }\n  };\n  if (status.loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83C\\uDFC3\\u200D\\u2642\\uFE0F Google Fit Integration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Checking connection status...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDFC3\\u200D\\u2642\\uFE0F Google Fit Integration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `status-badge ${status.connected ? 'connected' : 'disconnected'}`,\n        children: status.connected ? '✅ Connected' : '❌ Not Connected'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: !status.connected ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"integration-setup\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"integration-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Connect Your Smartwatch Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Automatically sync health data from your smartwatch including:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"feature-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\uD83D\\uDCF1 Step count and distance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2764\\uFE0F Heart rate monitoring\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\uD83D\\uDD25 Calories burned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u23F1\\uFE0F Active minutes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\uD83D\\uDE34 Sleep tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2696\\uFE0F Weight measurements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\uD83E\\uDE7A Blood pressure & oxygen levels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"integration-note\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Note:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), \" Your data is securely encrypted and only used for health analysis.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-large\",\n          onClick: connectGoogleFit,\n          children: \"\\uD83D\\uDD17 Connect Google Fit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"integration-connected\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"connection-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Connected:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(status.connectedAt).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Last Sync:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: status.lastSync ? new Date(status.lastSync).toLocaleString() : 'Never'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sync-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\uD83D\\uDCCA Sync Health Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Pull the latest health data from your connected devices.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sync-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-success\",\n              onClick: syncData,\n              disabled: syncing,\n              children: syncing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this), \"Syncing...\"]\n              }, void 0, true) : '🔄 Sync Data (Last 30 Days)'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline btn-danger\",\n              onClick: disconnectGoogleFit,\n              children: \"\\uD83D\\uDD0C Disconnect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), syncResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `sync-result ${syncResult.success ? 'success' : 'error'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: syncResult.success ? '✅ Sync Successful' : '❌ Sync Failed'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: syncResult.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 17\n          }, this), syncResult.success && syncResult.syncedDays && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Synced \", syncResult.syncedDays, \" days of health data.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .status-badge {\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 0.9rem;\n          font-weight: 500;\n        }\n        \n        .status-badge.connected {\n          background: #d4edda;\n          color: #155724;\n          border: 1px solid #c3e6cb;\n        }\n        \n        .status-badge.disconnected {\n          background: #f8d7da;\n          color: #721c24;\n          border: 1px solid #f5c6cb;\n        }\n        \n        .integration-setup {\n          text-align: center;\n          padding: 20px;\n        }\n        \n        .integration-info {\n          margin-bottom: 30px;\n        }\n        \n        .feature-list {\n          text-align: left;\n          max-width: 400px;\n          margin: 20px auto;\n          padding: 0;\n          list-style: none;\n        }\n        \n        .feature-list li {\n          padding: 8px 0;\n          border-bottom: 1px solid #eee;\n        }\n        \n        .integration-note {\n          background: #e7f3ff;\n          padding: 15px;\n          border-radius: 8px;\n          border-left: 4px solid #007bff;\n          margin: 20px 0;\n        }\n        \n        .btn-large {\n          padding: 15px 30px;\n          font-size: 1.1rem;\n        }\n        \n        .connection-info {\n          background: #f8f9fa;\n          padding: 20px;\n          border-radius: 8px;\n          margin-bottom: 20px;\n        }\n        \n        .info-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n        }\n        \n        .label {\n          font-weight: 600;\n          color: #666;\n        }\n        \n        .value {\n          color: #333;\n        }\n        \n        .sync-section {\n          margin-top: 20px;\n        }\n        \n        .sync-controls {\n          display: flex;\n          gap: 15px;\n          margin-top: 15px;\n        }\n        \n        .sync-result {\n          margin-top: 20px;\n          padding: 15px;\n          border-radius: 8px;\n        }\n        \n        .sync-result.success {\n          background: #d4edda;\n          border: 1px solid #c3e6cb;\n          color: #155724;\n        }\n        \n        .sync-result.error {\n          background: #f8d7da;\n          border: 1px solid #f5c6cb;\n          color: #721c24;\n        }\n        \n        .loading-spinner.small {\n          width: 16px;\n          height: 16px;\n          margin-right: 8px;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(GoogleFitIntegration, \"zYFr7mSVNxrHp3mVAXlXkITBtgc=\");\n_c = GoogleFitIntegration;\nexport default GoogleFitIntegration;\nvar _c;\n$RefreshReg$(_c, \"GoogleFitIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GoogleFitIntegration", "_s", "status", "setStatus", "connected", "loading", "lastSync", "syncing", "setSyncing", "syncResult", "setSyncResult", "checkConnectionStatus", "response", "get", "data", "connectedAt", "error", "console", "prev", "connectGoogleFit", "authUrl", "window", "open", "addEventListener", "handleAuthMessage", "alert", "event", "type", "code", "post", "removeEventListener", "syncData", "endDate", "Date", "startDate", "now", "toISOString", "success", "message", "syncedDays", "_error$response", "_error$response$data", "disconnectGoogleFit", "confirm", "delete", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "toLocaleDateString", "toLocaleString", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/GoogleFitIntegration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst GoogleFitIntegration = () => {\n  const [status, setStatus] = useState({\n    connected: false,\n    loading: true,\n    lastSync: null\n  });\n  const [syncing, setSyncing] = useState(false);\n  const [syncResult, setSyncResult] = useState(null);\n\n  useEffect(() => {\n    checkConnectionStatus();\n  }, []);\n\n  const checkConnectionStatus = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/status');\n      setStatus({\n        connected: response.data.data.connected,\n        loading: false,\n        lastSync: response.data.data.lastSync,\n        connectedAt: response.data.data.connectedAt\n      });\n    } catch (error) {\n      console.error('Error checking Google Fit status:', error);\n      setStatus(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  const connectGoogleFit = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/auth-url');\n      const authUrl = response.data.data.authUrl;\n      \n      // Open Google authorization in new window\n      window.open(authUrl, 'google-fit-auth', 'width=500,height=600');\n      \n      // Listen for authorization completion\n      window.addEventListener('message', handleAuthMessage);\n    } catch (error) {\n      console.error('Error getting auth URL:', error);\n      alert('Failed to connect to Google Fit. Please try again.');\n    }\n  };\n\n  const handleAuthMessage = async (event) => {\n    if (event.data.type === 'GOOGLE_FIT_AUTH_SUCCESS') {\n      const { code } = event.data;\n      \n      try {\n        await axios.post('/api/google-fit/callback', { code });\n        await checkConnectionStatus();\n        alert('Google Fit connected successfully!');\n      } catch (error) {\n        console.error('Error handling auth callback:', error);\n        alert('Failed to complete Google Fit connection.');\n      }\n      \n      window.removeEventListener('message', handleAuthMessage);\n    }\n  };\n\n  const syncData = async () => {\n    setSyncing(true);\n    setSyncResult(null);\n    \n    try {\n      const endDate = new Date();\n      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Last 30 days\n      \n      const response = await axios.post('/api/google-fit/sync', {\n        startDate: startDate.toISOString(),\n        endDate: endDate.toISOString()\n      });\n      \n      setSyncResult({\n        success: true,\n        message: response.data.message,\n        syncedDays: response.data.data.syncedDays\n      });\n      \n      await checkConnectionStatus();\n    } catch (error) {\n      console.error('Error syncing data:', error);\n      setSyncResult({\n        success: false,\n        message: error.response?.data?.message || 'Failed to sync data'\n      });\n    } finally {\n      setSyncing(false);\n    }\n  };\n\n  const disconnectGoogleFit = async () => {\n    if (window.confirm('Are you sure you want to disconnect Google Fit?')) {\n      try {\n        await axios.delete('/api/google-fit/disconnect');\n        await checkConnectionStatus();\n        setSyncResult(null);\n        alert('Google Fit disconnected successfully.');\n      } catch (error) {\n        console.error('Error disconnecting Google Fit:', error);\n        alert('Failed to disconnect Google Fit.');\n      }\n    }\n  };\n\n  if (status.loading) {\n    return (\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3>🏃‍♂️ Google Fit Integration</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"loading-spinner\"></div>\n          <p>Checking connection status...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3>🏃‍♂️ Google Fit Integration</h3>\n        <div className={`status-badge ${status.connected ? 'connected' : 'disconnected'}`}>\n          {status.connected ? '✅ Connected' : '❌ Not Connected'}\n        </div>\n      </div>\n      \n      <div className=\"card-body\">\n        {!status.connected ? (\n          <div className=\"integration-setup\">\n            <div className=\"integration-info\">\n              <h4>Connect Your Smartwatch Data</h4>\n              <p>\n                Automatically sync health data from your smartwatch including:\n              </p>\n              <ul className=\"feature-list\">\n                <li>📱 Step count and distance</li>\n                <li>❤️ Heart rate monitoring</li>\n                <li>🔥 Calories burned</li>\n                <li>⏱️ Active minutes</li>\n                <li>😴 Sleep tracking</li>\n                <li>⚖️ Weight measurements</li>\n                <li>🩺 Blood pressure & oxygen levels</li>\n              </ul>\n              <p className=\"integration-note\">\n                <strong>Note:</strong> Your data is securely encrypted and only used for health analysis.\n              </p>\n            </div>\n            \n            <button \n              className=\"btn btn-primary btn-large\"\n              onClick={connectGoogleFit}\n            >\n              🔗 Connect Google Fit\n            </button>\n          </div>\n        ) : (\n          <div className=\"integration-connected\">\n            <div className=\"connection-info\">\n              <div className=\"info-row\">\n                <span className=\"label\">Connected:</span>\n                <span className=\"value\">\n                  {new Date(status.connectedAt).toLocaleDateString()}\n                </span>\n              </div>\n              <div className=\"info-row\">\n                <span className=\"label\">Last Sync:</span>\n                <span className=\"value\">\n                  {status.lastSync \n                    ? new Date(status.lastSync).toLocaleString()\n                    : 'Never'\n                  }\n                </span>\n              </div>\n            </div>\n            \n            <div className=\"sync-section\">\n              <h4>📊 Sync Health Data</h4>\n              <p>Pull the latest health data from your connected devices.</p>\n              \n              <div className=\"sync-controls\">\n                <button \n                  className=\"btn btn-success\"\n                  onClick={syncData}\n                  disabled={syncing}\n                >\n                  {syncing ? (\n                    <>\n                      <div className=\"loading-spinner small\"></div>\n                      Syncing...\n                    </>\n                  ) : (\n                    '🔄 Sync Data (Last 30 Days)'\n                  )}\n                </button>\n                \n                <button \n                  className=\"btn btn-outline btn-danger\"\n                  onClick={disconnectGoogleFit}\n                >\n                  🔌 Disconnect\n                </button>\n              </div>\n            </div>\n            \n            {syncResult && (\n              <div className={`sync-result ${syncResult.success ? 'success' : 'error'}`}>\n                <h5>{syncResult.success ? '✅ Sync Successful' : '❌ Sync Failed'}</h5>\n                <p>{syncResult.message}</p>\n                {syncResult.success && syncResult.syncedDays && (\n                  <p>Synced {syncResult.syncedDays} days of health data.</p>\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      <style jsx>{`\n        .status-badge {\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 0.9rem;\n          font-weight: 500;\n        }\n        \n        .status-badge.connected {\n          background: #d4edda;\n          color: #155724;\n          border: 1px solid #c3e6cb;\n        }\n        \n        .status-badge.disconnected {\n          background: #f8d7da;\n          color: #721c24;\n          border: 1px solid #f5c6cb;\n        }\n        \n        .integration-setup {\n          text-align: center;\n          padding: 20px;\n        }\n        \n        .integration-info {\n          margin-bottom: 30px;\n        }\n        \n        .feature-list {\n          text-align: left;\n          max-width: 400px;\n          margin: 20px auto;\n          padding: 0;\n          list-style: none;\n        }\n        \n        .feature-list li {\n          padding: 8px 0;\n          border-bottom: 1px solid #eee;\n        }\n        \n        .integration-note {\n          background: #e7f3ff;\n          padding: 15px;\n          border-radius: 8px;\n          border-left: 4px solid #007bff;\n          margin: 20px 0;\n        }\n        \n        .btn-large {\n          padding: 15px 30px;\n          font-size: 1.1rem;\n        }\n        \n        .connection-info {\n          background: #f8f9fa;\n          padding: 20px;\n          border-radius: 8px;\n          margin-bottom: 20px;\n        }\n        \n        .info-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 10px;\n        }\n        \n        .label {\n          font-weight: 600;\n          color: #666;\n        }\n        \n        .value {\n          color: #333;\n        }\n        \n        .sync-section {\n          margin-top: 20px;\n        }\n        \n        .sync-controls {\n          display: flex;\n          gap: 15px;\n          margin-top: 15px;\n        }\n        \n        .sync-result {\n          margin-top: 20px;\n          padding: 15px;\n          border-radius: 8px;\n        }\n        \n        .sync-result.success {\n          background: #d4edda;\n          border: 1px solid #c3e6cb;\n          color: #155724;\n        }\n        \n        .sync-result.error {\n          background: #f8d7da;\n          border: 1px solid #f5c6cb;\n          color: #721c24;\n        }\n        \n        .loading-spinner.small {\n          width: 16px;\n          height: 16px;\n          margin-right: 8px;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default GoogleFitIntegration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC;IACnCW,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdiB,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAAC,wBAAwB,CAAC;MAC1DV,SAAS,CAAC;QACRC,SAAS,EAAEQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACV,SAAS;QACvCC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAEM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACR,QAAQ;QACrCS,WAAW,EAAEH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDb,SAAS,CAACe,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEb,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAAC,0BAA0B,CAAC;MAC5D,MAAMO,OAAO,GAAGR,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACM,OAAO;;MAE1C;MACAC,MAAM,CAACC,IAAI,CAACF,OAAO,EAAE,iBAAiB,EAAE,sBAAsB,CAAC;;MAE/D;MACAC,MAAM,CAACE,gBAAgB,CAAC,SAAS,EAAEC,iBAAiB,CAAC;IACvD,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CS,KAAK,CAAC,oDAAoD,CAAC;IAC7D;EACF,CAAC;EAED,MAAMD,iBAAiB,GAAG,MAAOE,KAAK,IAAK;IACzC,IAAIA,KAAK,CAACZ,IAAI,CAACa,IAAI,KAAK,yBAAyB,EAAE;MACjD,MAAM;QAAEC;MAAK,CAAC,GAAGF,KAAK,CAACZ,IAAI;MAE3B,IAAI;QACF,MAAMnB,KAAK,CAACkC,IAAI,CAAC,0BAA0B,EAAE;UAAED;QAAK,CAAC,CAAC;QACtD,MAAMjB,qBAAqB,CAAC,CAAC;QAC7Bc,KAAK,CAAC,oCAAoC,CAAC;MAC7C,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDS,KAAK,CAAC,2CAA2C,CAAC;MACpD;MAEAJ,MAAM,CAACS,mBAAmB,CAAC,SAAS,EAAEN,iBAAiB,CAAC;IAC1D;EACF,CAAC;EAED,MAAMO,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BvB,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAMsB,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;MAC1B,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;MAEnE,MAAMvB,QAAQ,GAAG,MAAMjB,KAAK,CAACkC,IAAI,CAAC,sBAAsB,EAAE;QACxDK,SAAS,EAAEA,SAAS,CAACE,WAAW,CAAC,CAAC;QAClCJ,OAAO,EAAEA,OAAO,CAACI,WAAW,CAAC;MAC/B,CAAC,CAAC;MAEF1B,aAAa,CAAC;QACZ2B,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE1B,QAAQ,CAACE,IAAI,CAACwB,OAAO;QAC9BC,UAAU,EAAE3B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACyB;MACjC,CAAC,CAAC;MAEF,MAAM5B,qBAAqB,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAwB,eAAA,EAAAC,oBAAA;MACdxB,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CN,aAAa,CAAC;QACZ2B,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAAE,eAAA,GAAAxB,KAAK,CAACJ,QAAQ,cAAA4B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,oBAAA,uBAApBA,oBAAA,CAAsBH,OAAO,KAAI;MAC5C,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIrB,MAAM,CAACsB,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAMhD,KAAK,CAACiD,MAAM,CAAC,4BAA4B,CAAC;QAChD,MAAMjC,qBAAqB,CAAC,CAAC;QAC7BD,aAAa,CAAC,IAAI,CAAC;QACnBe,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDS,KAAK,CAAC,kCAAkC,CAAC;MAC3C;IACF;EACF,CAAC;EAED,IAAIvB,MAAM,CAACG,OAAO,EAAE;IAClB,oBACER,OAAA;MAAKgD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BjD,OAAA;UAAAiD,QAAA,EAAI;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNrD,OAAA;QAAKgD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjD,OAAA;UAAKgD,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrD,OAAA;UAAAiD,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKgD,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBjD,OAAA;MAAKgD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjD,OAAA;QAAAiD,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCrD,OAAA;QAAKgD,SAAS,EAAE,gBAAgB3C,MAAM,CAACE,SAAS,GAAG,WAAW,GAAG,cAAc,EAAG;QAAA0C,QAAA,EAC/E5C,MAAM,CAACE,SAAS,GAAG,aAAa,GAAG;MAAiB;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrD,OAAA;MAAKgD,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB,CAAC5C,MAAM,CAACE,SAAS,gBAChBP,OAAA;QAAKgD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCjD,OAAA;UAAKgD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BjD,OAAA;YAAAiD,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCrD,OAAA;YAAAiD,QAAA,EAAG;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJrD,OAAA;YAAIgD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BjD,OAAA;cAAAiD,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnCrD,OAAA;cAAAiD,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCrD,OAAA;cAAAiD,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BrD,OAAA;cAAAiD,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BrD,OAAA;cAAAiD,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BrD,OAAA;cAAAiD,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BrD,OAAA;cAAAiD,QAAA,EAAI;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACLrD,OAAA;YAAGgD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7BjD,OAAA;cAAAiD,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uEACxB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrD,OAAA;UACEgD,SAAS,EAAC,2BAA2B;UACrCM,OAAO,EAAEhC,gBAAiB;UAAA2B,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENrD,OAAA;QAAKgD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCjD,OAAA;UAAKgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BjD,OAAA;YAAKgD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBjD,OAAA;cAAMgD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrD,OAAA;cAAMgD,SAAS,EAAC,OAAO;cAAAC,QAAA,EACpB,IAAIb,IAAI,CAAC/B,MAAM,CAACa,WAAW,CAAC,CAACqC,kBAAkB,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNrD,OAAA;YAAKgD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBjD,OAAA;cAAMgD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrD,OAAA;cAAMgD,SAAS,EAAC,OAAO;cAAAC,QAAA,EACpB5C,MAAM,CAACI,QAAQ,GACZ,IAAI2B,IAAI,CAAC/B,MAAM,CAACI,QAAQ,CAAC,CAAC+C,cAAc,CAAC,CAAC,GAC1C;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrD,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjD,OAAA;YAAAiD,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BrD,OAAA;YAAAiD,QAAA,EAAG;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/DrD,OAAA;YAAKgD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BjD,OAAA;cACEgD,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEpB,QAAS;cAClBuB,QAAQ,EAAE/C,OAAQ;cAAAuC,QAAA,EAEjBvC,OAAO,gBACNV,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,gBACEjD,OAAA;kBAAKgD,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,cAE/C;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAETrD,OAAA;cACEgD,SAAS,EAAC,4BAA4B;cACtCM,OAAO,EAAET,mBAAoB;cAAAI,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzC,UAAU,iBACTZ,OAAA;UAAKgD,SAAS,EAAE,eAAepC,UAAU,CAAC4B,OAAO,GAAG,SAAS,GAAG,OAAO,EAAG;UAAAS,QAAA,gBACxEjD,OAAA;YAAAiD,QAAA,EAAKrC,UAAU,CAAC4B,OAAO,GAAG,mBAAmB,GAAG;UAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrErD,OAAA;YAAAiD,QAAA,EAAIrC,UAAU,CAAC6B;UAAO;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC1BzC,UAAU,CAAC4B,OAAO,IAAI5B,UAAU,CAAC8B,UAAU,iBAC1C1C,OAAA;YAAAiD,QAAA,GAAG,SAAO,EAACrC,UAAU,CAAC8B,UAAU,EAAC,uBAAqB;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAC1D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrD,OAAA;MAAO0D,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjD,EAAA,CA7UID,oBAAoB;AAAAwD,EAAA,GAApBxD,oBAAoB;AA+U1B,eAAeA,oBAAoB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}