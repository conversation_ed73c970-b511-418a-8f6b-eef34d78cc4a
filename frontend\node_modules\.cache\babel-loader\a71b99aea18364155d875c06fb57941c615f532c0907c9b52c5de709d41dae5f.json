{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\AuthDebug.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthDebug = () => {\n  _s();\n  const {\n    user,\n    token,\n    loading,\n    isAuthenticated\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '10px',\n      right: '10px',\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      style: {\n        margin: '0 0 10px 0',\n        color: '#4CAF50'\n      },\n      children: \"\\uD83D\\uDD0D Auth Debug\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Loading:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 12\n      }, this), \" \", loading ? '⏳ Yes' : '✅ No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Authenticated:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 12\n      }, this), \" \", isAuthenticated ? '✅ Yes' : '❌ No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"User:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 12\n      }, this), \" \", user ? `👤 ${user.name}` : '❌ None']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Token:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 12\n      }, this), \" \", token ? `🔑 ${token.substring(0, 10)}...` : '❌ None']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"LocalStorage:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 12\n      }, this), \" \", localStorage.getItem('token') ? '💾 Present' : '❌ Missing']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthDebug, \"jYz0XstyfEGaovsirQrdwAh1p+0=\", false, function () {\n  return [useAuth];\n});\n_c = AuthDebug;\nexport default AuthDebug;\nvar _c;\n$RefreshReg$(_c, \"AuthDebug\");", "map": {"version": 3, "names": ["React", "useAuth", "jsxDEV", "_jsxDEV", "AuthDebug", "_s", "user", "token", "loading", "isAuthenticated", "style", "position", "top", "right", "background", "color", "padding", "borderRadius", "fontSize", "zIndex", "max<PERSON><PERSON><PERSON>", "children", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "substring", "localStorage", "getItem", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/AuthDebug.js"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst AuthDebug = () => {\n  const { user, token, loading, isAuthenticated } = useAuth();\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '10px',\n      right: '10px',\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '10px',\n      borderRadius: '5px',\n      fontSize: '12px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    }}>\n      <h4 style={{ margin: '0 0 10px 0', color: '#4CAF50' }}>🔍 Auth Debug</h4>\n      <div><strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}</div>\n      <div><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</div>\n      <div><strong>User:</strong> {user ? `👤 ${user.name}` : '❌ None'}</div>\n      <div><strong>Token:</strong> {token ? `🔑 ${token.substring(0, 10)}...` : '❌ None'}</div>\n      <div><strong>LocalStorage:</strong> {localStorage.getItem('token') ? '💾 Present' : '❌ Missing'}</div>\n    </div>\n  );\n};\n\nexport default AuthDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EAE3D,oBACEE,OAAA;IAAKO,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE,oBAAoB;MAChCC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAlB,OAAA;MAAIO,KAAK,EAAE;QAAEY,MAAM,EAAE,YAAY;QAAEP,KAAK,EAAE;MAAU,CAAE;MAAAM,QAAA,EAAC;IAAa;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzEvB,OAAA;MAAAkB,QAAA,gBAAKlB,OAAA;QAAAkB,QAAA,EAAQ;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAAClB,OAAO,GAAG,OAAO,GAAG,MAAM;IAAA;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACjEvB,OAAA;MAAAkB,QAAA,gBAAKlB,OAAA;QAAAkB,QAAA,EAAQ;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACjB,eAAe,GAAG,OAAO,GAAG,MAAM;IAAA;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/EvB,OAAA;MAAAkB,QAAA,gBAAKlB,OAAA;QAAAkB,QAAA,EAAQ;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACpB,IAAI,GAAG,MAAMA,IAAI,CAACqB,IAAI,EAAE,GAAG,QAAQ;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvEvB,OAAA;MAAAkB,QAAA,gBAAKlB,OAAA;QAAAkB,QAAA,EAAQ;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACnB,KAAK,GAAG,MAAMA,KAAK,CAACqB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,QAAQ;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACzFvB,OAAA;MAAAkB,QAAA,gBAAKlB,OAAA;QAAAkB,QAAA,EAAQ;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,YAAY,GAAG,WAAW;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnG,CAAC;AAEV,CAAC;AAACrB,EAAA,CAxBID,SAAS;EAAA,QACqCH,OAAO;AAAA;AAAA8B,EAAA,GADrD3B,SAAS;AA0Bf,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}