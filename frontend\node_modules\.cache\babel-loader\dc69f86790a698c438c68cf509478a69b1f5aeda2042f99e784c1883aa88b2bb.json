{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\QuickActions.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickActions = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    generatePrediction,\n    addHealthData\n  } = useHealthData();\n  const handleGeneratePrediction = async () => {\n    const result = await generatePrediction('comprehensive');\n    if (result.success) {\n      navigate('/predictions');\n    }\n  };\n  const handleAddTodayData = () => {\n    navigate('/health-data');\n  };\n  const handleSetGoal = () => {\n    navigate('/goals');\n  };\n  const handleChatWithBot = () => {\n    navigate('/chatbot');\n  };\n  const quickActions = [{\n    id: 'add-data',\n    title: 'Log Health Data',\n    description: 'Add today\\'s health metrics',\n    icon: '📊',\n    color: '#667eea',\n    action: handleAddTodayData\n  }, {\n    id: 'generate-prediction',\n    title: 'Health Analysis',\n    description: 'Get AI health insights',\n    icon: '🔮',\n    color: '#9b59b6',\n    action: handleGeneratePrediction\n  }, {\n    id: 'set-goal',\n    title: 'Set New Goal',\n    description: 'Create a fitness goal',\n    icon: '🎯',\n    color: '#27ae60',\n    action: handleSetGoal\n  }, {\n    id: 'chat-bot',\n    title: 'Ask Health Assistant',\n    description: 'Get health advice',\n    icon: '🤖',\n    color: '#f39c12',\n    action: handleChatWithBot\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quick-actions\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"actions-grid\",\n      children: quickActions.map(action => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"action-button\",\n        onClick: action.action,\n        style: {\n          '--action-color': action.color\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-icon\",\n          children: action.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"action-title\",\n            children: action.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"action-description\",\n            children: action.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-arrow\",\n          children: \"\\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, action.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quick-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Quick Stats\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-icon\",\n            children: \"\\uD83C\\uDFC3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"This Week\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"5 workouts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-icon\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Goals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"3 active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-icon\",\n            children: \"\\uD83D\\uDCC8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Streak\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-value\",\n              children: \"7 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"health-reminders\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Reminders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reminder-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reminder-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-icon\",\n            children: \"\\uD83D\\uDCA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-text\",\n            children: \"Drink water (2L today)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reminder-check\",\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reminder-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-icon\",\n            children: \"\\uD83D\\uDEB6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-text\",\n            children: \"Take a 10-min walk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reminder-check\",\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reminder-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-icon\",\n            children: \"\\uD83D\\uDE34\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reminder-text\",\n            children: \"Bedtime in 2 hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reminder-snooze\",\n            children: \"\\u23F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .quick-actions {\n          display: flex;\n          flex-direction: column;\n          gap: 25px;\n        }\n\n        .actions-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 15px;\n        }\n\n        .action-button {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.9);\n          border: 2px solid rgba(0, 0, 0, 0.1);\n          border-radius: 12px;\n          padding: 15px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          text-align: left;\n          width: 100%;\n        }\n\n        .action-button:hover {\n          background: rgba(255, 255, 255, 1);\n          border-color: var(--action-color);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n        }\n\n        .action-icon {\n          font-size: 2rem;\n          margin-right: 12px;\n          flex-shrink: 0;\n        }\n\n        .action-content {\n          flex: 1;\n        }\n\n        .action-title {\n          margin: 0 0 4px 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .action-description {\n          margin: 0;\n          font-size: 0.85rem;\n          color: #7f8c8d;\n          line-height: 1.3;\n        }\n\n        .action-arrow {\n          font-size: 1.2rem;\n          color: var(--action-color);\n          font-weight: bold;\n          margin-left: 10px;\n          transition: transform 0.3s ease;\n        }\n\n        .action-button:hover .action-arrow {\n          transform: translateX(3px);\n        }\n\n        .quick-stats h4,\n        .health-reminders h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .stats-row {\n          display: flex;\n          gap: 15px;\n          flex-wrap: wrap;\n        }\n\n        .stat-item {\n          display: flex;\n          align-items: center;\n          background: rgba(102, 126, 234, 0.1);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 8px;\n          padding: 12px;\n          flex: 1;\n          min-width: 120px;\n        }\n\n        .stat-icon {\n          font-size: 1.3rem;\n          margin-right: 10px;\n        }\n\n        .stat-info {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .stat-label {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          margin-bottom: 2px;\n        }\n\n        .stat-value {\n          font-size: 0.9rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .reminder-list {\n          display: flex;\n          flex-direction: column;\n          gap: 10px;\n        }\n\n        .reminder-item {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.8);\n          border: 1px solid rgba(0, 0, 0, 0.1);\n          border-radius: 8px;\n          padding: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .reminder-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n        }\n\n        .reminder-icon {\n          font-size: 1.2rem;\n          margin-right: 12px;\n        }\n\n        .reminder-text {\n          flex: 1;\n          font-size: 0.9rem;\n          color: #2c3e50;\n        }\n\n        .reminder-check,\n        .reminder-snooze {\n          background: none;\n          border: 2px solid #27ae60;\n          border-radius: 50%;\n          width: 30px;\n          height: 30px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          font-size: 0.8rem;\n        }\n\n        .reminder-check {\n          color: #27ae60;\n        }\n\n        .reminder-check:hover {\n          background: #27ae60;\n          color: white;\n        }\n\n        .reminder-snooze {\n          border-color: #f39c12;\n          color: #f39c12;\n        }\n\n        .reminder-snooze:hover {\n          background: #f39c12;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .actions-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .action-button {\n            padding: 12px;\n          }\n\n          .action-title {\n            font-size: 0.95rem;\n          }\n\n          .action-description {\n            font-size: 0.8rem;\n          }\n\n          .stats-row {\n            flex-direction: column;\n          }\n\n          .stat-item {\n            min-width: auto;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"JAaQlEj6yOznXJ73VoVpzJrj+tY=\", false, function () {\n  return [useNavigate, useHealthData];\n});\n_c = QuickActions;\nexport default QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");", "map": {"version": 3, "names": ["React", "useNavigate", "useHealthData", "jsxDEV", "_jsxDEV", "QuickActions", "_s", "navigate", "generatePrediction", "addHealthData", "handleGeneratePrediction", "result", "success", "handleAddTodayData", "handleSetGoal", "handleChatWithBot", "quickActions", "id", "title", "description", "icon", "color", "action", "className", "children", "map", "onClick", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/QuickActions.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst QuickActions = () => {\n  const navigate = useNavigate();\n  const { generatePrediction, addHealthData } = useHealthData();\n\n  const handleGeneratePrediction = async () => {\n    const result = await generatePrediction('comprehensive');\n    if (result.success) {\n      navigate('/predictions');\n    }\n  };\n\n  const handleAddTodayData = () => {\n    navigate('/health-data');\n  };\n\n  const handleSetGoal = () => {\n    navigate('/goals');\n  };\n\n  const handleChatWithBot = () => {\n    navigate('/chatbot');\n  };\n\n  const quickActions = [\n    {\n      id: 'add-data',\n      title: 'Log Health Data',\n      description: 'Add today\\'s health metrics',\n      icon: '📊',\n      color: '#667eea',\n      action: handleAddTodayData\n    },\n    {\n      id: 'generate-prediction',\n      title: 'Health Analysis',\n      description: 'Get AI health insights',\n      icon: '🔮',\n      color: '#9b59b6',\n      action: handleGeneratePrediction\n    },\n    {\n      id: 'set-goal',\n      title: 'Set New Goal',\n      description: 'Create a fitness goal',\n      icon: '🎯',\n      color: '#27ae60',\n      action: handleSetGoal\n    },\n    {\n      id: 'chat-bot',\n      title: 'Ask Health Assistant',\n      description: 'Get health advice',\n      icon: '🤖',\n      color: '#f39c12',\n      action: handleChatWithBot\n    }\n  ];\n\n  return (\n    <div className=\"quick-actions\">\n      <div className=\"actions-grid\">\n        {quickActions.map((action) => (\n          <button\n            key={action.id}\n            className=\"action-button\"\n            onClick={action.action}\n            style={{ '--action-color': action.color }}\n          >\n            <div className=\"action-icon\">{action.icon}</div>\n            <div className=\"action-content\">\n              <h4 className=\"action-title\">{action.title}</h4>\n              <p className=\"action-description\">{action.description}</p>\n            </div>\n            <div className=\"action-arrow\">→</div>\n          </button>\n        ))}\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"quick-stats\">\n        <h4>Quick Stats</h4>\n        <div className=\"stats-row\">\n          <div className=\"stat-item\">\n            <span className=\"stat-icon\">🏃</span>\n            <div className=\"stat-info\">\n              <span className=\"stat-label\">This Week</span>\n              <span className=\"stat-value\">5 workouts</span>\n            </div>\n          </div>\n          \n          <div className=\"stat-item\">\n            <span className=\"stat-icon\">🎯</span>\n            <div className=\"stat-info\">\n              <span className=\"stat-label\">Goals</span>\n              <span className=\"stat-value\">3 active</span>\n            </div>\n          </div>\n          \n          <div className=\"stat-item\">\n            <span className=\"stat-icon\">📈</span>\n            <div className=\"stat-info\">\n              <span className=\"stat-label\">Streak</span>\n              <span className=\"stat-value\">7 days</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Health Reminders */}\n      <div className=\"health-reminders\">\n        <h4>Reminders</h4>\n        <div className=\"reminder-list\">\n          <div className=\"reminder-item\">\n            <span className=\"reminder-icon\">💧</span>\n            <span className=\"reminder-text\">Drink water (2L today)</span>\n            <button className=\"reminder-check\">✓</button>\n          </div>\n          \n          <div className=\"reminder-item\">\n            <span className=\"reminder-icon\">🚶</span>\n            <span className=\"reminder-text\">Take a 10-min walk</span>\n            <button className=\"reminder-check\">✓</button>\n          </div>\n          \n          <div className=\"reminder-item\">\n            <span className=\"reminder-icon\">😴</span>\n            <span className=\"reminder-text\">Bedtime in 2 hours</span>\n            <button className=\"reminder-snooze\">⏰</button>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .quick-actions {\n          display: flex;\n          flex-direction: column;\n          gap: 25px;\n        }\n\n        .actions-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 15px;\n        }\n\n        .action-button {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.9);\n          border: 2px solid rgba(0, 0, 0, 0.1);\n          border-radius: 12px;\n          padding: 15px;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          text-align: left;\n          width: 100%;\n        }\n\n        .action-button:hover {\n          background: rgba(255, 255, 255, 1);\n          border-color: var(--action-color);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n        }\n\n        .action-icon {\n          font-size: 2rem;\n          margin-right: 12px;\n          flex-shrink: 0;\n        }\n\n        .action-content {\n          flex: 1;\n        }\n\n        .action-title {\n          margin: 0 0 4px 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .action-description {\n          margin: 0;\n          font-size: 0.85rem;\n          color: #7f8c8d;\n          line-height: 1.3;\n        }\n\n        .action-arrow {\n          font-size: 1.2rem;\n          color: var(--action-color);\n          font-weight: bold;\n          margin-left: 10px;\n          transition: transform 0.3s ease;\n        }\n\n        .action-button:hover .action-arrow {\n          transform: translateX(3px);\n        }\n\n        .quick-stats h4,\n        .health-reminders h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .stats-row {\n          display: flex;\n          gap: 15px;\n          flex-wrap: wrap;\n        }\n\n        .stat-item {\n          display: flex;\n          align-items: center;\n          background: rgba(102, 126, 234, 0.1);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 8px;\n          padding: 12px;\n          flex: 1;\n          min-width: 120px;\n        }\n\n        .stat-icon {\n          font-size: 1.3rem;\n          margin-right: 10px;\n        }\n\n        .stat-info {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .stat-label {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          margin-bottom: 2px;\n        }\n\n        .stat-value {\n          font-size: 0.9rem;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        .reminder-list {\n          display: flex;\n          flex-direction: column;\n          gap: 10px;\n        }\n\n        .reminder-item {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.8);\n          border: 1px solid rgba(0, 0, 0, 0.1);\n          border-radius: 8px;\n          padding: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .reminder-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n        }\n\n        .reminder-icon {\n          font-size: 1.2rem;\n          margin-right: 12px;\n        }\n\n        .reminder-text {\n          flex: 1;\n          font-size: 0.9rem;\n          color: #2c3e50;\n        }\n\n        .reminder-check,\n        .reminder-snooze {\n          background: none;\n          border: 2px solid #27ae60;\n          border-radius: 50%;\n          width: 30px;\n          height: 30px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          font-size: 0.8rem;\n        }\n\n        .reminder-check {\n          color: #27ae60;\n        }\n\n        .reminder-check:hover {\n          background: #27ae60;\n          color: white;\n        }\n\n        .reminder-snooze {\n          border-color: #f39c12;\n          color: #f39c12;\n        }\n\n        .reminder-snooze:hover {\n          background: #f39c12;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .actions-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .action-button {\n            padding: 12px;\n          }\n\n          .action-title {\n            font-size: 0.95rem;\n          }\n\n          .action-description {\n            font-size: 0.8rem;\n          }\n\n          .stats-row {\n            flex-direction: column;\n          }\n\n          .stat-item {\n            min-width: auto;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default QuickActions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO,kBAAkB;IAAEC;EAAc,CAAC,GAAGP,aAAa,CAAC,CAAC;EAE7D,MAAMQ,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,MAAMC,MAAM,GAAG,MAAMH,kBAAkB,CAAC,eAAe,CAAC;IACxD,IAAIG,MAAM,CAACC,OAAO,EAAE;MAClBL,QAAQ,CAAC,cAAc,CAAC;IAC1B;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/BN,QAAQ,CAAC,cAAc,CAAC;EAC1B,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BP,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BR,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMS,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAET;EACV,CAAC,EACD;IACEI,EAAE,EAAE,qBAAqB;IACzBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAEZ;EACV,CAAC,EACD;IACEO,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAER;EACV,CAAC,EACD;IACEG,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAEP;EACV,CAAC,CACF;EAED,oBACEX,OAAA;IAAKmB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BpB,OAAA;MAAKmB,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BR,YAAY,CAACS,GAAG,CAAEH,MAAM,iBACvBlB,OAAA;QAEEmB,SAAS,EAAC,eAAe;QACzBG,OAAO,EAAEJ,MAAM,CAACA,MAAO;QACvBK,KAAK,EAAE;UAAE,gBAAgB,EAAEL,MAAM,CAACD;QAAM,CAAE;QAAAG,QAAA,gBAE1CpB,OAAA;UAAKmB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEF,MAAM,CAACF;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChD3B,OAAA;UAAKmB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpB,OAAA;YAAImB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEF,MAAM,CAACJ;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD3B,OAAA;YAAGmB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEF,MAAM,CAACH;UAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACN3B,OAAA;UAAKmB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA,GAVhCT,MAAM,CAACL,EAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWR,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA;MAAKmB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BpB,OAAA;QAAAoB,QAAA,EAAI;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpB3B,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpB,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpB,OAAA;YAAMmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrC3B,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpB,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C3B,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3B,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpB,OAAA;YAAMmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrC3B,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpB,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC3B,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3B,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpB,OAAA;YAAMmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrC3B,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpB,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C3B,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKmB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpB,OAAA;QAAAoB,QAAA,EAAI;MAAS;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClB3B,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpB,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpB,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC3B,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAsB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7D3B,OAAA;YAAQmB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEN3B,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpB,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC3B,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzD3B,OAAA;YAAQmB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEN3B,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpB,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC3B,OAAA;YAAMmB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzD3B,OAAA;YAAQmB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAO4B,GAAG;MAAAR,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACzB,EAAA,CApVID,YAAY;EAAA,QACCJ,WAAW,EACkBC,aAAa;AAAA;AAAA+B,EAAA,GAFvD5B,YAAY;AAsVlB,eAAeA,YAAY;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}