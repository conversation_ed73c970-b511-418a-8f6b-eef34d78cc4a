import React, { useState, useEffect } from 'react';
import axios from 'axios';

const GoogleFitIntegration = () => {
  const [status, setStatus] = useState({
    connected: false,
    loading: true,
    lastSync: null
  });
  const [syncing, setSyncing] = useState(false);
  const [syncResult, setSyncResult] = useState(null);

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const response = await axios.get('/api/google-fit/status');
      setStatus({
        connected: response.data.data.connected,
        loading: false,
        lastSync: response.data.data.lastSync,
        connectedAt: response.data.data.connectedAt
      });
    } catch (error) {
      console.error('Error checking Google Fit status:', error);
      setStatus(prev => ({ ...prev, loading: false }));
    }
  };

  const connectGoogleFit = async () => {
    try {
      const response = await axios.get('/api/google-fit/auth-url');
      const authUrl = response.data.data.authUrl;
      
      // Open Google authorization in new window
      window.open(authUrl, 'google-fit-auth', 'width=500,height=600');
      
      // Listen for authorization completion
      window.addEventListener('message', handleAuthMessage);
    } catch (error) {
      console.error('Error getting auth URL:', error);
      alert('Failed to connect to Google Fit. Please try again.');
    }
  };

  const handleAuthMessage = async (event) => {
    if (event.data.type === 'GOOGLE_FIT_AUTH_SUCCESS') {
      const { code } = event.data;
      
      try {
        await axios.post('/api/google-fit/callback', { code });
        await checkConnectionStatus();
        alert('Google Fit connected successfully!');
      } catch (error) {
        console.error('Error handling auth callback:', error);
        alert('Failed to complete Google Fit connection.');
      }
      
      window.removeEventListener('message', handleAuthMessage);
    }
  };

  const syncData = async () => {
    setSyncing(true);
    setSyncResult(null);
    
    try {
      const endDate = new Date();
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Last 30 days
      
      const response = await axios.post('/api/google-fit/sync', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });
      
      setSyncResult({
        success: true,
        message: response.data.message,
        syncedDays: response.data.data.syncedDays
      });
      
      await checkConnectionStatus();
    } catch (error) {
      console.error('Error syncing data:', error);
      setSyncResult({
        success: false,
        message: error.response?.data?.message || 'Failed to sync data'
      });
    } finally {
      setSyncing(false);
    }
  };

  const disconnectGoogleFit = async () => {
    if (window.confirm('Are you sure you want to disconnect Google Fit?')) {
      try {
        await axios.delete('/api/google-fit/disconnect');
        await checkConnectionStatus();
        setSyncResult(null);
        alert('Google Fit disconnected successfully.');
      } catch (error) {
        console.error('Error disconnecting Google Fit:', error);
        alert('Failed to disconnect Google Fit.');
      }
    }
  };

  if (status.loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3>🏃‍♂️ Google Fit Integration</h3>
        </div>
        <div className="card-body">
          <div className="loading-spinner"></div>
          <p>Checking connection status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3>🏃‍♂️ Google Fit Integration</h3>
        <div className={`status-badge ${status.connected ? 'connected' : 'disconnected'}`}>
          {status.connected ? '✅ Connected' : '❌ Not Connected'}
        </div>
      </div>
      
      <div className="card-body">
        {!status.connected ? (
          <div className="integration-setup">
            <div className="integration-info">
              <h4>Connect Your Smartwatch Data</h4>
              <p>
                Automatically sync health data from your smartwatch including:
              </p>
              <ul className="feature-list">
                <li>📱 Step count and distance</li>
                <li>❤️ Heart rate monitoring</li>
                <li>🔥 Calories burned</li>
                <li>⏱️ Active minutes</li>
                <li>😴 Sleep tracking</li>
                <li>⚖️ Weight measurements</li>
                <li>🩺 Blood pressure & oxygen levels</li>
              </ul>
              <p className="integration-note">
                <strong>Note:</strong> Your data is securely encrypted and only used for health analysis.
              </p>
            </div>
            
            <button 
              className="btn btn-primary btn-large"
              onClick={connectGoogleFit}
            >
              🔗 Connect Google Fit
            </button>
          </div>
        ) : (
          <div className="integration-connected">
            <div className="connection-info">
              <div className="info-row">
                <span className="label">Connected:</span>
                <span className="value">
                  {new Date(status.connectedAt).toLocaleDateString()}
                </span>
              </div>
              <div className="info-row">
                <span className="label">Last Sync:</span>
                <span className="value">
                  {status.lastSync 
                    ? new Date(status.lastSync).toLocaleString()
                    : 'Never'
                  }
                </span>
              </div>
            </div>
            
            <div className="sync-section">
              <h4>📊 Sync Health Data</h4>
              <p>Pull the latest health data from your connected devices.</p>
              
              <div className="sync-controls">
                <button 
                  className="btn btn-success"
                  onClick={syncData}
                  disabled={syncing}
                >
                  {syncing ? (
                    <>
                      <div className="loading-spinner small"></div>
                      Syncing...
                    </>
                  ) : (
                    '🔄 Sync Data (Last 30 Days)'
                  )}
                </button>
                
                <button 
                  className="btn btn-outline btn-danger"
                  onClick={disconnectGoogleFit}
                >
                  🔌 Disconnect
                </button>
              </div>
            </div>
            
            {syncResult && (
              <div className={`sync-result ${syncResult.success ? 'success' : 'error'}`}>
                <h5>{syncResult.success ? '✅ Sync Successful' : '❌ Sync Failed'}</h5>
                <p>{syncResult.message}</p>
                {syncResult.success && syncResult.syncedDays && (
                  <p>Synced {syncResult.syncedDays} days of health data.</p>
                )}
              </div>
            )}
          </div>
        )}
      </div>
      
      <style jsx>{`
        .status-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        .status-badge.connected {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
        
        .status-badge.disconnected {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
        
        .integration-setup {
          text-align: center;
          padding: 20px;
        }
        
        .integration-info {
          margin-bottom: 30px;
        }
        
        .feature-list {
          text-align: left;
          max-width: 400px;
          margin: 20px auto;
          padding: 0;
          list-style: none;
        }
        
        .feature-list li {
          padding: 8px 0;
          border-bottom: 1px solid #eee;
        }
        
        .integration-note {
          background: #e7f3ff;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #007bff;
          margin: 20px 0;
        }
        
        .btn-large {
          padding: 15px 30px;
          font-size: 1.1rem;
        }
        
        .connection-info {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        
        .label {
          font-weight: 600;
          color: #666;
        }
        
        .value {
          color: #333;
        }
        
        .sync-section {
          margin-top: 20px;
        }
        
        .sync-controls {
          display: flex;
          gap: 15px;
          margin-top: 15px;
        }
        
        .sync-result {
          margin-top: 20px;
          padding: 15px;
          border-radius: 8px;
        }
        
        .sync-result.success {
          background: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }
        
        .sync-result.error {
          background: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        
        .loading-spinner.small {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      `}</style>
    </div>
  );
};

export default GoogleFitIntegration;
