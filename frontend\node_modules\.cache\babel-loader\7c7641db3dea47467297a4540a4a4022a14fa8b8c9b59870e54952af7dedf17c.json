{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\components\\\\HealthInsights.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HealthInsights = ({\n  insights = [],\n  prediction = null\n}) => {\n  var _prediction$overallRi;\n  const getInsightIcon = type => {\n    switch (type) {\n      case 'success':\n        return '✅';\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      case 'suggestion':\n        return '💡';\n      default:\n        return '📊';\n    }\n  };\n  const getInsightColor = type => {\n    switch (type) {\n      case 'success':\n        return '#27ae60';\n      case 'warning':\n        return '#f39c12';\n      case 'info':\n        return '#667eea';\n      case 'suggestion':\n        return '#9b59b6';\n      default:\n        return '#2c3e50';\n    }\n  };\n  const getRiskColor = riskLevel => {\n    switch (riskLevel) {\n      case 'very_low':\n      case 'low':\n        return '#27ae60';\n      case 'moderate':\n        return '#f39c12';\n      case 'high':\n      case 'very_high':\n        return '#e74c3c';\n      default:\n        return '#667eea';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"health-insights\",\n    children: [prediction && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"prediction-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prediction-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"\\uD83D\\uDD2E AI Health Assessment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"risk-badge\",\n          style: {\n            backgroundColor: getRiskColor(prediction.overallRisk),\n            color: 'white'\n          },\n          children: [((_prediction$overallRi = prediction.overallRisk) === null || _prediction$overallRi === void 0 ? void 0 : _prediction$overallRi.replace('_', ' ').toUpperCase()) || 'UNKNOWN', \" RISK\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), prediction.recommendations && prediction.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prediction-recommendations\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Priority Recommendations:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: prediction.recommendations.slice(0, 3).map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [rec.title, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 21\n            }, this), \" \", rec.description]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prediction-date\",\n        children: [\"Last updated: \", new Date(prediction.createdAt).toLocaleDateString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"insights-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDCC8 Today's Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), insights.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-insights\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Great job! No health concerns detected today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Keep up the good work with your healthy habits! \\uD83C\\uDF1F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this) : insights.map((insight, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"insight-item\",\n        style: {\n          borderLeftColor: getInsightColor(insight.type)\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"insight-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"insight-icon\",\n            children: getInsightIcon(insight.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"insight-title\",\n            children: insight.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"insight-message\",\n          children: insight.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 15\n        }, this), insight.action && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"insight-action\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Recommended Action:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 19\n          }, this), \" \", insight.action]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 17\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"health-tips\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\uD83D\\uDCA1 Health Tips\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tips-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tip-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tip-icon\",\n            children: \"\\uD83D\\uDCA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tip-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Stay Hydrated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Aim for 8 glasses of water daily\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tip-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tip-icon\",\n            children: \"\\uD83D\\uDEB6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tip-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Move Regularly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Take breaks every hour to walk\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tip-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tip-icon\",\n            children: \"\\uD83D\\uDE34\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tip-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Quality Sleep\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Maintain consistent sleep schedule\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tip-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tip-icon\",\n            children: \"\\uD83E\\uDDD8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tip-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Manage Stress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Practice mindfulness daily\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .health-insights {\n          display: flex;\n          flex-direction: column;\n          gap: 25px;\n        }\n\n        .prediction-summary {\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 12px;\n          padding: 20px;\n        }\n\n        .prediction-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n        }\n\n        .prediction-header h4 {\n          margin: 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .risk-badge {\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 0.75rem;\n          font-weight: 700;\n          letter-spacing: 0.5px;\n        }\n\n        .prediction-recommendations {\n          margin-bottom: 15px;\n        }\n\n        .prediction-recommendations h5 {\n          margin: 0 0 10px 0;\n          color: #2c3e50;\n          font-size: 0.95rem;\n        }\n\n        .prediction-recommendations ul {\n          margin: 0;\n          padding-left: 20px;\n        }\n\n        .prediction-recommendations li {\n          margin-bottom: 8px;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          line-height: 1.4;\n        }\n\n        .prediction-date {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          font-style: italic;\n        }\n\n        .insights-list h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .no-insights {\n          text-align: center;\n          padding: 20px;\n          background: rgba(39, 174, 96, 0.1);\n          border: 1px solid rgba(39, 174, 96, 0.2);\n          border-radius: 8px;\n          color: #27ae60;\n        }\n\n        .no-insights p {\n          margin: 5px 0;\n        }\n\n        .insight-item {\n          background: rgba(255, 255, 255, 0.8);\n          border-left: 4px solid;\n          border-radius: 8px;\n          padding: 15px;\n          margin-bottom: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .insight-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n          transform: translateX(2px);\n        }\n\n        .insight-header {\n          display: flex;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .insight-icon {\n          font-size: 1.2rem;\n          margin-right: 10px;\n        }\n\n        .insight-title {\n          margin: 0;\n          color: #2c3e50;\n          font-size: 1rem;\n          font-weight: 600;\n        }\n\n        .insight-message {\n          margin: 0 0 8px 0;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          line-height: 1.4;\n        }\n\n        .insight-action {\n          font-size: 0.85rem;\n          color: #667eea;\n          background: rgba(102, 126, 234, 0.1);\n          padding: 8px 12px;\n          border-radius: 6px;\n          margin-top: 8px;\n        }\n\n        .health-tips h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .tips-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 12px;\n        }\n\n        .tip-item {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.8);\n          border: 1px solid rgba(0, 0, 0, 0.1);\n          border-radius: 8px;\n          padding: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .tip-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n        }\n\n        .tip-icon {\n          font-size: 1.5rem;\n          margin-right: 12px;\n        }\n\n        .tip-content h6 {\n          margin: 0 0 4px 0;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          font-weight: 600;\n        }\n\n        .tip-content p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .prediction-header {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 10px;\n          }\n\n          .tips-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .tip-item {\n            flex-direction: column;\n            text-align: center;\n          }\n\n          .tip-icon {\n            margin-right: 0;\n            margin-bottom: 8px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = HealthInsights;\nexport default HealthInsights;\nvar _c;\n$RefreshReg$(_c, \"HealthInsights\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "HealthInsights", "insights", "prediction", "_prediction$overallRi", "getInsightIcon", "type", "getInsightColor", "getRiskColor", "riskLevel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "overallRisk", "color", "replace", "toUpperCase", "recommendations", "length", "slice", "map", "rec", "index", "title", "description", "Date", "createdAt", "toLocaleDateString", "insight", "borderLeftColor", "message", "action", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/components/HealthInsights.js"], "sourcesContent": ["import React from 'react';\n\nconst HealthInsights = ({ insights = [], prediction = null }) => {\n  const getInsightIcon = (type) => {\n    switch (type) {\n      case 'success':\n        return '✅';\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      case 'suggestion':\n        return '💡';\n      default:\n        return '📊';\n    }\n  };\n\n  const getInsightColor = (type) => {\n    switch (type) {\n      case 'success':\n        return '#27ae60';\n      case 'warning':\n        return '#f39c12';\n      case 'info':\n        return '#667eea';\n      case 'suggestion':\n        return '#9b59b6';\n      default:\n        return '#2c3e50';\n    }\n  };\n\n  const getRiskColor = (riskLevel) => {\n    switch (riskLevel) {\n      case 'very_low':\n      case 'low':\n        return '#27ae60';\n      case 'moderate':\n        return '#f39c12';\n      case 'high':\n      case 'very_high':\n        return '#e74c3c';\n      default:\n        return '#667eea';\n    }\n  };\n\n  return (\n    <div className=\"health-insights\">\n      {/* AI Prediction Summary */}\n      {prediction && (\n        <div className=\"prediction-summary\">\n          <div className=\"prediction-header\">\n            <h4>🔮 AI Health Assessment</h4>\n            <span \n              className=\"risk-badge\"\n              style={{ \n                backgroundColor: getRiskColor(prediction.overallRisk),\n                color: 'white'\n              }}\n            >\n              {prediction.overallRisk?.replace('_', ' ').toUpperCase() || 'UNKNOWN'} RISK\n            </span>\n          </div>\n          \n          {prediction.recommendations && prediction.recommendations.length > 0 && (\n            <div className=\"prediction-recommendations\">\n              <h5>Priority Recommendations:</h5>\n              <ul>\n                {prediction.recommendations.slice(0, 3).map((rec, index) => (\n                  <li key={index}>\n                    <strong>{rec.title}:</strong> {rec.description}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n          \n          <div className=\"prediction-date\">\n            Last updated: {new Date(prediction.createdAt).toLocaleDateString()}\n          </div>\n        </div>\n      )}\n\n      {/* Daily Insights */}\n      <div className=\"insights-list\">\n        <h4>📈 Today's Insights</h4>\n        \n        {insights.length === 0 ? (\n          <div className=\"no-insights\">\n            <p>Great job! No health concerns detected today.</p>\n            <p>Keep up the good work with your healthy habits! 🌟</p>\n          </div>\n        ) : (\n          insights.map((insight, index) => (\n            <div \n              key={index}\n              className=\"insight-item\"\n              style={{ borderLeftColor: getInsightColor(insight.type) }}\n            >\n              <div className=\"insight-header\">\n                <span className=\"insight-icon\">\n                  {getInsightIcon(insight.type)}\n                </span>\n                <h5 className=\"insight-title\">{insight.title}</h5>\n              </div>\n              \n              <p className=\"insight-message\">{insight.message}</p>\n              \n              {insight.action && (\n                <div className=\"insight-action\">\n                  <strong>Recommended Action:</strong> {insight.action}\n                </div>\n              )}\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Health Tips */}\n      <div className=\"health-tips\">\n        <h4>💡 Health Tips</h4>\n        <div className=\"tips-grid\">\n          <div className=\"tip-item\">\n            <span className=\"tip-icon\">💧</span>\n            <div className=\"tip-content\">\n              <h6>Stay Hydrated</h6>\n              <p>Aim for 8 glasses of water daily</p>\n            </div>\n          </div>\n          \n          <div className=\"tip-item\">\n            <span className=\"tip-icon\">🚶</span>\n            <div className=\"tip-content\">\n              <h6>Move Regularly</h6>\n              <p>Take breaks every hour to walk</p>\n            </div>\n          </div>\n          \n          <div className=\"tip-item\">\n            <span className=\"tip-icon\">😴</span>\n            <div className=\"tip-content\">\n              <h6>Quality Sleep</h6>\n              <p>Maintain consistent sleep schedule</p>\n            </div>\n          </div>\n          \n          <div className=\"tip-item\">\n            <span className=\"tip-icon\">🧘</span>\n            <div className=\"tip-content\">\n              <h6>Manage Stress</h6>\n              <p>Practice mindfulness daily</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .health-insights {\n          display: flex;\n          flex-direction: column;\n          gap: 25px;\n        }\n\n        .prediction-summary {\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 12px;\n          padding: 20px;\n        }\n\n        .prediction-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n        }\n\n        .prediction-header h4 {\n          margin: 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .risk-badge {\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 0.75rem;\n          font-weight: 700;\n          letter-spacing: 0.5px;\n        }\n\n        .prediction-recommendations {\n          margin-bottom: 15px;\n        }\n\n        .prediction-recommendations h5 {\n          margin: 0 0 10px 0;\n          color: #2c3e50;\n          font-size: 0.95rem;\n        }\n\n        .prediction-recommendations ul {\n          margin: 0;\n          padding-left: 20px;\n        }\n\n        .prediction-recommendations li {\n          margin-bottom: 8px;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          line-height: 1.4;\n        }\n\n        .prediction-date {\n          font-size: 0.8rem;\n          color: #7f8c8d;\n          font-style: italic;\n        }\n\n        .insights-list h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .no-insights {\n          text-align: center;\n          padding: 20px;\n          background: rgba(39, 174, 96, 0.1);\n          border: 1px solid rgba(39, 174, 96, 0.2);\n          border-radius: 8px;\n          color: #27ae60;\n        }\n\n        .no-insights p {\n          margin: 5px 0;\n        }\n\n        .insight-item {\n          background: rgba(255, 255, 255, 0.8);\n          border-left: 4px solid;\n          border-radius: 8px;\n          padding: 15px;\n          margin-bottom: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .insight-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n          transform: translateX(2px);\n        }\n\n        .insight-header {\n          display: flex;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .insight-icon {\n          font-size: 1.2rem;\n          margin-right: 10px;\n        }\n\n        .insight-title {\n          margin: 0;\n          color: #2c3e50;\n          font-size: 1rem;\n          font-weight: 600;\n        }\n\n        .insight-message {\n          margin: 0 0 8px 0;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          line-height: 1.4;\n        }\n\n        .insight-action {\n          font-size: 0.85rem;\n          color: #667eea;\n          background: rgba(102, 126, 234, 0.1);\n          padding: 8px 12px;\n          border-radius: 6px;\n          margin-top: 8px;\n        }\n\n        .health-tips h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        .tips-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 12px;\n        }\n\n        .tip-item {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.8);\n          border: 1px solid rgba(0, 0, 0, 0.1);\n          border-radius: 8px;\n          padding: 12px;\n          transition: all 0.3s ease;\n        }\n\n        .tip-item:hover {\n          background: rgba(255, 255, 255, 0.95);\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n        }\n\n        .tip-icon {\n          font-size: 1.5rem;\n          margin-right: 12px;\n        }\n\n        .tip-content h6 {\n          margin: 0 0 4px 0;\n          color: #2c3e50;\n          font-size: 0.9rem;\n          font-weight: 600;\n        }\n\n        .tip-content p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .prediction-header {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 10px;\n          }\n\n          .tips-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .tip-item {\n            flex-direction: column;\n            text-align: center;\n          }\n\n          .tip-icon {\n            margin-right: 0;\n            margin-bottom: 8px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HealthInsights;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ,GAAG,EAAE;EAAEC,UAAU,GAAG;AAAK,CAAC,KAAK;EAAA,IAAAC,qBAAA;EAC/D,MAAMC,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,GAAG;MACZ,KAAK,SAAS;QACZ,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,IAAI,IAAK;IAChC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,SAAS,IAAK;IAClC,QAAQA,SAAS;MACf,KAAK,UAAU;MACf,KAAK,KAAK;QACR,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,MAAM;MACX,KAAK,WAAW;QACd,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,iBAAiB;IAAAC,QAAA,GAE7BR,UAAU,iBACTH,OAAA;MAAKU,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCX,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCX,OAAA;UAAAW,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCf,OAAA;UACEU,SAAS,EAAC,YAAY;UACtBM,KAAK,EAAE;YACLC,eAAe,EAAET,YAAY,CAACL,UAAU,CAACe,WAAW,CAAC;YACrDC,KAAK,EAAE;UACT,CAAE;UAAAR,QAAA,GAED,EAAAP,qBAAA,GAAAD,UAAU,CAACe,WAAW,cAAAd,qBAAA,uBAAtBA,qBAAA,CAAwBgB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,SAAS,EAAC,OACxE;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELZ,UAAU,CAACmB,eAAe,IAAInB,UAAU,CAACmB,eAAe,CAACC,MAAM,GAAG,CAAC,iBAClEvB,OAAA;QAAKU,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCX,OAAA;UAAAW,QAAA,EAAI;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCf,OAAA;UAAAW,QAAA,EACGR,UAAU,CAACmB,eAAe,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACrD3B,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAAW,QAAA,GAASe,GAAG,CAACE,KAAK,EAAC,GAAC;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACW,GAAG,CAACG,WAAW;UAAA,GADvCF,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eAEDf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,gBACjB,EAAC,IAAImB,IAAI,CAAC3B,UAAU,CAAC4B,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;MAAA;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDf,OAAA;MAAKU,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BX,OAAA;QAAAW,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE3Bb,QAAQ,CAACqB,MAAM,KAAK,CAAC,gBACpBvB,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BX,OAAA;UAAAW,QAAA,EAAG;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpDf,OAAA;UAAAW,QAAA,EAAG;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,GAENb,QAAQ,CAACuB,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAC1B3B,OAAA;QAEEU,SAAS,EAAC,cAAc;QACxBM,KAAK,EAAE;UAAEkB,eAAe,EAAE3B,eAAe,CAAC0B,OAAO,CAAC3B,IAAI;QAAE,CAAE;QAAAK,QAAA,gBAE1DX,OAAA;UAAKU,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BX,OAAA;YAAMU,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3BN,cAAc,CAAC4B,OAAO,CAAC3B,IAAI;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACPf,OAAA;YAAIU,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEsB,OAAO,CAACL;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAENf,OAAA;UAAGU,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEsB,OAAO,CAACE;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEnDkB,OAAO,CAACG,MAAM,iBACbpC,OAAA;UAAKU,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BX,OAAA;YAAAW,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACkB,OAAO,CAACG,MAAM;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CACN;MAAA,GAjBIY,KAAK;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBP,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNf,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BX,OAAA;QAAAW,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBf,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBX,OAAA;UAAKU,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBX,OAAA;YAAMU,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAAW,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBf,OAAA;cAAAW,QAAA,EAAG;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBX,OAAA;YAAMU,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAAW,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBf,OAAA;cAAAW,QAAA,EAAG;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBX,OAAA;YAAMU,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAAW,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBf,OAAA;cAAAW,QAAA,EAAG;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBX,OAAA;YAAMU,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCf,OAAA;YAAKU,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BX,OAAA;cAAAW,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBf,OAAA;cAAAW,QAAA,EAAG;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENf,OAAA;MAAOqC,GAAG;MAAA1B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACuB,EAAA,GApWIrC,cAAc;AAsWpB,eAAeA,cAAc;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}