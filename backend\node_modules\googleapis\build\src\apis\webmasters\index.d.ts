/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { webmasters_v3 } from './v3';
export declare const VERSIONS: {
    v3: typeof webmasters_v3.Webmasters;
};
export declare function webmasters(version: 'v3'): webmasters_v3.Webmasters;
export declare function webmasters(options: webmasters_v3.Options): webmasters_v3.Webmasters;
declare const auth: AuthPlus;
export { auth };
export { webmasters_v3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
