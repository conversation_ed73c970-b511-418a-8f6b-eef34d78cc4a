{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          console.log('🔍 Checking authentication with token:', token.substring(0, 20) + '...');\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          console.log('✅ Auth check successful:', response.data.data.user);\n          setUser(response.data.data.user);\n        } catch (error) {\n          var _error$response, _error$response2;\n          console.error('❌ Auth check failed:', error);\n          console.log('🧹 Token is invalid, clearing auth data');\n          // Only clear auth if the error is actually an auth error (401/403)\n          if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n            setUser(null);\n            setToken(null);\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n          } else {\n            console.log('⚠️ Network error, keeping auth data for retry');\n          }\n        }\n      } else {\n        console.log('ℹ️ No token found, user not authenticated');\n      }\n      setLoading(false);\n    };\n\n    // Only run auth check on initial load, not every time token changes\n    if (loading) {\n      checkAuth();\n    }\n  }, [loading]); // Changed dependency from [token] to [loading]\n\n  const login = async (email, password) => {\n    try {\n      console.log('🔐 Attempting login with:', {\n        email,\n        password\n      });\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n      console.log('✅ Login response received:', response.data);\n      if (!response.data.success) {\n        console.error('❌ Login failed - API returned success: false');\n        return {\n          success: false,\n          message: response.data.message || 'Login failed'\n        };\n      }\n      const {\n        user,\n        token\n      } = response.data.data;\n      if (!user || !token) {\n        console.error('❌ Login failed - Missing user or token in response');\n        return {\n          success: false,\n          message: 'Invalid response from server'\n        };\n      }\n      console.log('👤 Setting user:', user);\n      console.log('🔑 Setting token:', token.substring(0, 20) + '...');\n\n      // Set token first and configure axios\n      setToken(token);\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      // Then set user\n      setUser(user);\n      console.log('✅ Login successful! User and token set.');\n      console.log('🔍 isAuthenticated should now be:', !!(user && token));\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response3, _error$response4, _error$response4$data;\n      console.error('❌ Login error:', error);\n      console.error('❌ Error response:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data);\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message || 'Login failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Registration failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return {\n        success: true,\n        user: response.data.data.user\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      const message = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Profile update failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      const message = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Password change failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user && !!token\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "console", "log", "substring", "response", "get", "data", "error", "_error$response", "_error$response2", "status", "removeItem", "login", "email", "password", "post", "success", "message", "setItem", "_error$response3", "_error$response4", "_error$response4$data", "register", "userData", "_error$response5", "_error$response5$data", "logout", "updateProfile", "profileData", "put", "_error$response6", "_error$response6$data", "changePassword", "currentPassword", "newPassword", "_error$response7", "_error$response7$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          console.log('🔍 Checking authentication with token:', token.substring(0, 20) + '...');\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          console.log('✅ Auth check successful:', response.data.data.user);\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('❌ Auth check failed:', error);\n          console.log('🧹 Token is invalid, clearing auth data');\n          // Only clear auth if the error is actually an auth error (401/403)\n          if (error.response?.status === 401 || error.response?.status === 403) {\n            setUser(null);\n            setToken(null);\n            localStorage.removeItem('token');\n            delete axios.defaults.headers.common['Authorization'];\n          } else {\n            console.log('⚠️ Network error, keeping auth data for retry');\n          }\n        }\n      } else {\n        console.log('ℹ️ No token found, user not authenticated');\n      }\n      setLoading(false);\n    };\n\n    // Only run auth check on initial load, not every time token changes\n    if (loading) {\n      checkAuth();\n    }\n  }, [loading]); // Changed dependency from [token] to [loading]\n\n  const login = async (email, password) => {\n    try {\n      console.log('🔐 Attempting login with:', { email, password });\n\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n\n      console.log('✅ Login response received:', response.data);\n\n      if (!response.data.success) {\n        console.error('❌ Login failed - API returned success: false');\n        return { success: false, message: response.data.message || 'Login failed' };\n      }\n\n      const { user, token } = response.data.data;\n\n      if (!user || !token) {\n        console.error('❌ Login failed - Missing user or token in response');\n        return { success: false, message: 'Invalid response from server' };\n      }\n\n      console.log('👤 Setting user:', user);\n      console.log('🔑 Setting token:', token.substring(0, 20) + '...');\n\n      // Set token first and configure axios\n      setToken(token);\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      // Then set user\n      setUser(user);\n\n      console.log('✅ Login successful! User and token set.');\n      console.log('🔍 isAuthenticated should now be:', !!(user && token));\n\n      return { success: true, user };\n    } catch (error) {\n      console.error('❌ Login error:', error);\n      console.error('❌ Error response:', error.response?.data);\n      const message = error.response?.data?.message || error.message || 'Login failed';\n      return { success: false, message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      \n      const { user, token } = response.data.data;\n      \n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      \n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      return { success: false, message };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return { success: true, user: response.data.data.user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      return { success: false, message };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      return { success: false, message };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user && !!token\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACFQ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAET,KAAK,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UACrF,MAAMC,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,mCAAmC,CAAC;UACrEJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;UAChEC,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;QAClC,CAAC,CAAC,OAAOkB,KAAK,EAAE;UAAA,IAAAC,eAAA,EAAAC,gBAAA;UACdR,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CN,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD;UACA,IAAI,EAAAM,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,EAAAD,gBAAA,GAAAF,KAAK,CAACH,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;YACpEpB,OAAO,CAAC,IAAI,CAAC;YACbI,QAAQ,CAAC,IAAI,CAAC;YACdC,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC;YAChC,OAAOjC,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;UACvD,CAAC,MAAM;YACLE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC9D;QACF;MACF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;MACAV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;;IAED;IACA,IAAID,OAAO,EAAE;MACXS,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,MAAMqB,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QAAEW,KAAK;QAAEC;MAAS,CAAC,CAAC;MAE7D,MAAMV,QAAQ,GAAG,MAAM1B,KAAK,CAACqC,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEE,QAAQ,CAACE,IAAI,CAAC;MAExD,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QAC1Bf,OAAO,CAACM,KAAK,CAAC,8CAA8C,CAAC;QAC7D,OAAO;UAAES,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAEb,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAI;QAAe,CAAC;MAC7E;MAEA,MAAM;QAAE5B,IAAI;QAAEI;MAAM,CAAC,GAAGW,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1C,IAAI,CAACjB,IAAI,IAAI,CAACI,KAAK,EAAE;QACnBQ,OAAO,CAACM,KAAK,CAAC,oDAAoD,CAAC;QACnE,OAAO;UAAES,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAA+B,CAAC;MACpE;MAEAhB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEb,IAAI,CAAC;MACrCY,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAET,KAAK,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;MAEhE;MACAT,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACuB,OAAO,CAAC,OAAO,EAAEzB,KAAK,CAAC;MACpCf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;;MAElE;MACAH,OAAO,CAACD,IAAI,CAAC;MAEbY,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,CAAC,EAAEb,IAAI,IAAII,KAAK,CAAC,CAAC;MAEnE,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAE3B;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAY,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdpB,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCN,OAAO,CAACM,KAAK,CAAC,mBAAmB,GAAAY,gBAAA,GAAEZ,KAAK,CAACH,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBb,IAAI,CAAC;MACxD,MAAMW,OAAO,GAAG,EAAAG,gBAAA,GAAAb,KAAK,CAACH,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAIV,KAAK,CAACU,OAAO,IAAI,cAAc;MAChF,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMK,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM1B,KAAK,CAACqC,IAAI,CAAC,yCAAyC,EAAEQ,QAAQ,CAAC;MAEtF,MAAM;QAAElC,IAAI;QAAEI;MAAM,CAAC,GAAGW,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1ChB,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACuB,OAAO,CAAC,OAAO,EAAEzB,KAAK,CAAC;MAEpC,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAE3B;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACd,MAAMR,OAAO,GAAG,EAAAO,gBAAA,GAAAjB,KAAK,CAACH,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,qBAAqB;MACtE,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMS,MAAM,GAAGA,CAAA,KAAM;IACnBpC,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOjC,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAM4B,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM1B,KAAK,CAACmD,GAAG,CAAC,wCAAwC,EAAED,WAAW,CAAC;MACvFtC,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;MAChC,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAE3B,IAAI,EAAEe,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB;MAAK,CAAC;IACzD,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA;MACd,MAAMd,OAAO,GAAG,EAAAa,gBAAA,GAAAvB,KAAK,CAACH,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,uBAAuB;MACxE,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMe,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMxD,KAAK,CAACmD,GAAG,CAAC,gDAAgD,EAAE;QAChEI,eAAe;QACfC;MACF,CAAC,CAAC;MACF,OAAO;QAAElB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACd,MAAMnB,OAAO,GAAG,EAAAkB,gBAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,wBAAwB;MACzE,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMoB,KAAK,GAAG;IACZhD,IAAI;IACJI,KAAK;IACLF,OAAO;IACPqB,KAAK;IACLU,QAAQ;IACRI,MAAM;IACNC,aAAa;IACbK,cAAc;IACdM,eAAe,EAAE,CAAC,CAACjD,IAAI,IAAI,CAAC,CAACI;EAC/B,CAAC;EAED,oBACEb,OAAA,CAACC,WAAW,CAAC0D,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAlD,QAAA,EAChCA;EAAQ;IAAAqD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACvD,GAAA,CA/JWF,YAAY;AAAA0D,EAAA,GAAZ1D,YAAY;AAAA,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}