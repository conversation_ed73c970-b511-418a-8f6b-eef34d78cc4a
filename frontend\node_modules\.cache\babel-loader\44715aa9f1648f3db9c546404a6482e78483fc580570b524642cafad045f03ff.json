{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport StatsCard from '../components/StatsCard';\nimport ActivityChart from '../components/ActivityChart';\nimport GoalProgress from '../components/GoalProgress';\nimport HealthInsights from '../components/HealthInsights';\nimport QuickActions from '../components/QuickActions';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _today$steps, _today$calories, _goals$active, _weekly$totalSteps;\n  const {\n    user\n  } = useAuth();\n  const {\n    dashboardData,\n    loading,\n    fetchDashboardData\n  } = useHealthData();\n  const [selectedPeriod, setSelectedPeriod] = useState('7d');\n  const [googleFitStatus, setGoogleFitStatus] = useState({\n    connected: false,\n    loading: true,\n    lastSync: null\n  });\n  const [autoSyncing, setAutoSyncing] = useState(false);\n  useEffect(() => {\n    fetchDashboardData();\n    checkGoogleFitStatus();\n  }, []);\n\n  // Check Google Fit connection status\n  const checkGoogleFitStatus = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/status');\n      setGoogleFitStatus({\n        connected: response.data.data.connected,\n        loading: false,\n        lastSync: response.data.data.lastSync,\n        connectedAt: response.data.data.connectedAt\n      });\n\n      // If connected and no recent sync, auto-sync\n      if (response.data.data.connected) {\n        const lastSync = response.data.data.lastSync;\n        const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);\n        if (!lastSync || new Date(lastSync) < sixHoursAgo) {\n          console.log('🔄 Auto-syncing Google Fit data...');\n          autoSyncGoogleFit();\n        }\n      }\n    } catch (error) {\n      console.error('Error checking Google Fit status:', error);\n      setGoogleFitStatus(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Auto-sync Google Fit data\n  const autoSyncGoogleFit = async () => {\n    setAutoSyncing(true);\n    try {\n      const endDate = new Date();\n      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Last 7 days\n\n      await axios.post('/api/google-fit/sync', {\n        startDate: startDate.toISOString(),\n        endDate: endDate.toISOString()\n      });\n      console.log('✅ Google Fit data synced successfully');\n      // Refresh dashboard data after sync\n      await fetchDashboardData();\n      await checkGoogleFitStatus();\n    } catch (error) {\n      console.error('❌ Auto-sync failed:', error);\n    } finally {\n      setAutoSyncing(false);\n    }\n  };\n  if (loading && !dashboardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-center\",\n      style: {\n        minHeight: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this);\n  }\n  const today = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.today) || {};\n  const weekly = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.weekly) || {};\n  const goals = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.goals) || {};\n  const prediction = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.prediction;\n  const badges = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.badges) || [];\n  const insights = (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.insights) || [];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [!googleFitStatus.loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `google-fit-banner ${googleFitStatus.connected ? 'connected' : 'disconnected'}`,\n      children: googleFitStatus.connected ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"banner-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"banner-icon\",\n            children: \"\\uD83C\\uDFC3\\u200D\\u2642\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Google Fit Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: autoSyncing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"\\uD83D\\uDD04 Syncing your latest health data...\"\n              }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\"Last sync: \", googleFitStatus.lastSync ? new Date(googleFitStatus.lastSync).toLocaleString() : 'Never']\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline btn-small\",\n          onClick: autoSyncGoogleFit,\n          disabled: autoSyncing,\n          children: autoSyncing ? 'Syncing...' : '🔄 Sync Now'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"banner-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"banner-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Connect Your Smartwatch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get automatic health data from your fitness tracker\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/profile\",\n          className: \"btn btn-primary btn-small\",\n          children: \"\\uD83D\\uDD17 Connect Google Fit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"card-title\",\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"card-subtitle\",\n            children: \"Here's your health overview for today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          style: {\n            gap: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPeriod,\n            onChange: e => setSelectedPeriod(e.target.value),\n            className: \"form-select\",\n            style: {\n              width: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"7d\",\n              children: \"Last 7 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"30d\",\n              children: \"Last 30 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"90d\",\n              children: \"Last 90 days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-4\",\n        children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Steps\",\n          value: ((_today$steps = today.steps) === null || _today$steps === void 0 ? void 0 : _today$steps.toLocaleString()) || '0',\n          target: \"10,000\",\n          icon: \"\\uD83D\\uDC5F\",\n          color: \"#667eea\",\n          progress: today.steps / 10000 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Calories\",\n          value: ((_today$calories = today.calories) === null || _today$calories === void 0 ? void 0 : _today$calories.toLocaleString()) || '0',\n          target: \"2,000\",\n          icon: \"\\uD83D\\uDD25\",\n          color: \"#e74c3c\",\n          progress: today.calories / 2000 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Active Minutes\",\n          value: today.activeMinutes || '0',\n          target: \"60\",\n          icon: \"\\u23F1\\uFE0F\",\n          color: \"#f39c12\",\n          progress: today.activeMinutes / 60 * 100\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Activity Score\",\n          value: today.activityScore || '0',\n          target: \"100\",\n          icon: \"\\u2B50\",\n          color: \"#27ae60\",\n          progress: today.activityScore || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Activity Trends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivityChart, {\n          period: selectedPeriod\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Active Goals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-muted\",\n            children: [((_goals$active = goals.active) === null || _goals$active === void 0 ? void 0 : _goals$active.length) || 0, \" active goals\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GoalProgress, {\n          goals: goals.active || []\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Health Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HealthInsights, {\n          insights: insights,\n          prediction: prediction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"card-title\",\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(QuickActions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), badges.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Recent Achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"achievements-grid\",\n        children: badges.map((badge, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"achievement-badge\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-icon\",\n            children: badge.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: badge.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: badge.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"card-title\",\n          children: \"Weekly Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: ((_weekly$totalSteps = weekly.totalSteps) === null || _weekly$totalSteps === void 0 ? void 0 : _weekly$totalSteps.toLocaleString()) || '0'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Steps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Avg: \", Math.round(weekly.avgSteps || 0).toLocaleString(), \"/day\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.totalDistance || 0), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Distance Covered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Avg: \", Math.round((weekly.totalDistance || 0) / 7), \" km/day\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.avgSleep || 0), \" min\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Avg Sleep\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [Math.round((weekly.avgSleep || 0) / 60), \" hours/night\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [Math.round(weekly.avgHeartRate || 0), \" bpm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Avg Heart Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: \"Resting rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .dashboard {\n          padding: 0;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 15px;\n        }\n\n        .achievement-badge {\n          display: flex;\n          align-items: center;\n          padding: 15px;\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.2);\n        }\n\n        .badge-icon {\n          font-size: 2rem;\n          margin-right: 15px;\n        }\n\n        .badge-info h4 {\n          margin: 0 0 5px 0;\n          color: #2c3e50;\n          font-size: 1rem;\n        }\n\n        .badge-info p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .summary-stat {\n          text-align: center;\n          padding: 20px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .summary-stat h3 {\n          margin: 0 0 8px 0;\n          font-size: 1.8rem;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        .summary-stat p {\n          margin: 0 0 5px 0;\n          font-weight: 500;\n          color: #2c3e50;\n        }\n\n        .summary-stat small {\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .grid-4 {\n            grid-template-columns: repeat(2, 1fr);\n          }\n          \n          .grid-2 {\n            grid-template-columns: 1fr;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .grid-4 {\n            grid-template-columns: 1fr;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"6EttjUpuoQJpUyVCon00JmqiuUw=\", false, function () {\n  return [useAuth, useHealthData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useHealthData", "useAuth", "StatsCard", "ActivityChart", "GoalProgress", "HealthInsights", "QuickActions", "GoogleFitIntegration", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "_today$steps", "_today$calories", "_goals$active", "_weekly$totalSteps", "user", "dashboardData", "loading", "fetchDashboardData", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "googleFitStatus", "setGoogleFitStatus", "connected", "lastSync", "autoSyncing", "setAutoSyncing", "checkGoogleFitStatus", "response", "get", "data", "connectedAt", "sixHoursAgo", "Date", "now", "console", "log", "autoSyncGoogleFit", "error", "prev", "endDate", "startDate", "post", "toISOString", "className", "style", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "today", "weekly", "goals", "prediction", "badges", "insights", "toLocaleString", "onClick", "disabled", "href", "name", "gap", "value", "onChange", "e", "target", "width", "title", "steps", "icon", "color", "progress", "calories", "activeMinutes", "activityScore", "period", "active", "length", "map", "badge", "index", "description", "totalSteps", "Math", "round", "avgSteps", "totalDistance", "avgSleep", "avgHeartRate", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport StatsCard from '../components/StatsCard';\nimport ActivityChart from '../components/ActivityChart';\nimport GoalProgress from '../components/GoalProgress';\nimport HealthInsights from '../components/HealthInsights';\nimport QuickActions from '../components/QuickActions';\nimport GoogleFitIntegration from '../components/GoogleFitIntegration';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const { dashboardData, loading, fetchDashboardData } = useHealthData();\n  const [selectedPeriod, setSelectedPeriod] = useState('7d');\n  const [googleFitStatus, setGoogleFitStatus] = useState({\n    connected: false,\n    loading: true,\n    lastSync: null\n  });\n  const [autoSyncing, setAutoSyncing] = useState(false);\n\n  useEffect(() => {\n    fetchDashboardData();\n    checkGoogleFitStatus();\n  }, []);\n\n  // Check Google Fit connection status\n  const checkGoogleFitStatus = async () => {\n    try {\n      const response = await axios.get('/api/google-fit/status');\n      setGoogleFitStatus({\n        connected: response.data.data.connected,\n        loading: false,\n        lastSync: response.data.data.lastSync,\n        connectedAt: response.data.data.connectedAt\n      });\n\n      // If connected and no recent sync, auto-sync\n      if (response.data.data.connected) {\n        const lastSync = response.data.data.lastSync;\n        const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);\n\n        if (!lastSync || new Date(lastSync) < sixHoursAgo) {\n          console.log('🔄 Auto-syncing Google Fit data...');\n          autoSyncGoogleFit();\n        }\n      }\n    } catch (error) {\n      console.error('Error checking Google Fit status:', error);\n      setGoogleFitStatus(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Auto-sync Google Fit data\n  const autoSyncGoogleFit = async () => {\n    setAutoSyncing(true);\n    try {\n      const endDate = new Date();\n      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Last 7 days\n\n      await axios.post('/api/google-fit/sync', {\n        startDate: startDate.toISOString(),\n        endDate: endDate.toISOString()\n      });\n\n      console.log('✅ Google Fit data synced successfully');\n      // Refresh dashboard data after sync\n      await fetchDashboardData();\n      await checkGoogleFitStatus();\n    } catch (error) {\n      console.error('❌ Auto-sync failed:', error);\n    } finally {\n      setAutoSyncing(false);\n    }\n  };\n\n  if (loading && !dashboardData) {\n    return (\n      <div className=\"flex-center\" style={{ minHeight: '50vh' }}>\n        <div className=\"loading-spinner\"></div>\n      </div>\n    );\n  }\n\n  const today = dashboardData?.today || {};\n  const weekly = dashboardData?.weekly || {};\n  const goals = dashboardData?.goals || {};\n  const prediction = dashboardData?.prediction;\n  const badges = dashboardData?.badges || [];\n  const insights = dashboardData?.insights || [];\n\n  return (\n    <div className=\"dashboard\">\n      {/* Google Fit Status Banner */}\n      {!googleFitStatus.loading && (\n        <div className={`google-fit-banner ${googleFitStatus.connected ? 'connected' : 'disconnected'}`}>\n          {googleFitStatus.connected ? (\n            <div className=\"banner-content\">\n              <div className=\"banner-info\">\n                <span className=\"banner-icon\">🏃‍♂️</span>\n                <div>\n                  <strong>Google Fit Connected</strong>\n                  <p>\n                    {autoSyncing ? (\n                      <>🔄 Syncing your latest health data...</>\n                    ) : (\n                      <>Last sync: {googleFitStatus.lastSync ? new Date(googleFitStatus.lastSync).toLocaleString() : 'Never'}</>\n                    )}\n                  </p>\n                </div>\n              </div>\n              <button\n                className=\"btn btn-outline btn-small\"\n                onClick={autoSyncGoogleFit}\n                disabled={autoSyncing}\n              >\n                {autoSyncing ? 'Syncing...' : '🔄 Sync Now'}\n              </button>\n            </div>\n          ) : (\n            <div className=\"banner-content\">\n              <div className=\"banner-info\">\n                <span className=\"banner-icon\">⚠️</span>\n                <div>\n                  <strong>Connect Your Smartwatch</strong>\n                  <p>Get automatic health data from your fitness tracker</p>\n                </div>\n              </div>\n              <a href=\"/profile\" className=\"btn btn-primary btn-small\">\n                🔗 Connect Google Fit\n              </a>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Welcome Section */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div>\n            <h1 className=\"card-title\">\n              Welcome back, {user?.name}! 👋\n            </h1>\n            <p className=\"card-subtitle\">\n              Here's your health overview for today\n            </p>\n          </div>\n          <div className=\"flex\" style={{ gap: '10px' }}>\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"form-select\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"7d\">Last 7 days</option>\n              <option value=\"30d\">Last 30 days</option>\n              <option value=\"90d\">Last 90 days</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Today's Stats */}\n        <div className=\"grid grid-4\">\n          <StatsCard\n            title=\"Steps\"\n            value={today.steps?.toLocaleString() || '0'}\n            target=\"10,000\"\n            icon=\"👟\"\n            color=\"#667eea\"\n            progress={(today.steps / 10000) * 100}\n          />\n          <StatsCard\n            title=\"Calories\"\n            value={today.calories?.toLocaleString() || '0'}\n            target=\"2,000\"\n            icon=\"🔥\"\n            color=\"#e74c3c\"\n            progress={(today.calories / 2000) * 100}\n          />\n          <StatsCard\n            title=\"Active Minutes\"\n            value={today.activeMinutes || '0'}\n            target=\"60\"\n            icon=\"⏱️\"\n            color=\"#f39c12\"\n            progress={(today.activeMinutes / 60) * 100}\n          />\n          <StatsCard\n            title=\"Activity Score\"\n            value={today.activityScore || '0'}\n            target=\"100\"\n            icon=\"⭐\"\n            color=\"#27ae60\"\n            progress={today.activityScore || 0}\n          />\n        </div>\n      </div>\n\n      <div className=\"grid grid-2\">\n        {/* Activity Chart */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Activity Trends</h2>\n          </div>\n          <ActivityChart period={selectedPeriod} />\n        </div>\n\n        {/* Goals Progress */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Active Goals</h2>\n            <span className=\"text-muted\">\n              {goals.active?.length || 0} active goals\n            </span>\n          </div>\n          <GoalProgress goals={goals.active || []} />\n        </div>\n      </div>\n\n      <div className=\"grid grid-2\">\n        {/* Health Insights */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Health Insights</h2>\n          </div>\n          <HealthInsights insights={insights} prediction={prediction} />\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Quick Actions</h2>\n          </div>\n          <QuickActions />\n        </div>\n      </div>\n\n      {/* Achievements */}\n      {badges.length > 0 && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"card-title\">Recent Achievements</h2>\n          </div>\n          <div className=\"achievements-grid\">\n            {badges.map((badge, index) => (\n              <div key={index} className=\"achievement-badge\">\n                <span className=\"badge-icon\">{badge.icon}</span>\n                <div className=\"badge-info\">\n                  <h4>{badge.name}</h4>\n                  <p>{badge.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Weekly Summary */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h2 className=\"card-title\">Weekly Summary</h2>\n        </div>\n        <div className=\"grid grid-4\">\n          <div className=\"summary-stat\">\n            <h3>{weekly.totalSteps?.toLocaleString() || '0'}</h3>\n            <p>Total Steps</p>\n            <small className=\"text-muted\">\n              Avg: {Math.round(weekly.avgSteps || 0).toLocaleString()}/day\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.totalDistance || 0)} km</h3>\n            <p>Distance Covered</p>\n            <small className=\"text-muted\">\n              Avg: {Math.round((weekly.totalDistance || 0) / 7)} km/day\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.avgSleep || 0)} min</h3>\n            <p>Avg Sleep</p>\n            <small className=\"text-muted\">\n              {Math.round((weekly.avgSleep || 0) / 60)} hours/night\n            </small>\n          </div>\n          <div className=\"summary-stat\">\n            <h3>{Math.round(weekly.avgHeartRate || 0)} bpm</h3>\n            <p>Avg Heart Rate</p>\n            <small className=\"text-muted\">\n              Resting rate\n            </small>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .dashboard {\n          padding: 0;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 15px;\n        }\n\n        .achievement-badge {\n          display: flex;\n          align-items: center;\n          padding: 15px;\n          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.2);\n        }\n\n        .badge-icon {\n          font-size: 2rem;\n          margin-right: 15px;\n        }\n\n        .badge-info h4 {\n          margin: 0 0 5px 0;\n          color: #2c3e50;\n          font-size: 1rem;\n        }\n\n        .badge-info p {\n          margin: 0;\n          color: #7f8c8d;\n          font-size: 0.9rem;\n        }\n\n        .summary-stat {\n          text-align: center;\n          padding: 20px;\n          background: rgba(102, 126, 234, 0.05);\n          border-radius: 12px;\n          border: 1px solid rgba(102, 126, 234, 0.1);\n        }\n\n        .summary-stat h3 {\n          margin: 0 0 8px 0;\n          font-size: 1.8rem;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        .summary-stat p {\n          margin: 0 0 5px 0;\n          font-weight: 500;\n          color: #2c3e50;\n        }\n\n        .summary-stat small {\n          font-size: 0.8rem;\n        }\n\n        @media (max-width: 768px) {\n          .grid-4 {\n            grid-template-columns: repeat(2, 1fr);\n          }\n          \n          .grid-2 {\n            grid-template-columns: 1fr;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .grid-4 {\n            grid-template-columns: 1fr;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,kBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEkB,aAAa;IAAEC,OAAO;IAAEC;EAAmB,CAAC,GAAGrB,aAAa,CAAC,CAAC;EACtE,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC;IACrD4B,SAAS,EAAE,KAAK;IAChBN,OAAO,EAAE,IAAI;IACbO,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAErDD,SAAS,CAAC,MAAM;IACdwB,kBAAkB,CAAC,CAAC;IACpBS,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,wBAAwB,CAAC;MAC1DP,kBAAkB,CAAC;QACjBC,SAAS,EAAEK,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACP,SAAS;QACvCN,OAAO,EAAE,KAAK;QACdO,QAAQ,EAAEI,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACN,QAAQ;QACrCO,WAAW,EAAEH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC;MAClC,CAAC,CAAC;;MAEF;MACA,IAAIH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACP,SAAS,EAAE;QAChC,MAAMC,QAAQ,GAAGI,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACN,QAAQ;QAC5C,MAAMQ,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAE7D,IAAI,CAACV,QAAQ,IAAI,IAAIS,IAAI,CAACT,QAAQ,CAAC,GAAGQ,WAAW,EAAE;UACjDG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjDC,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDhB,kBAAkB,CAACiB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAMoB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCX,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMc,OAAO,GAAG,IAAIP,IAAI,CAAC,CAAC;MAC1B,MAAMQ,SAAS,GAAG,IAAIR,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;MAElE,MAAMtC,KAAK,CAAC8C,IAAI,CAAC,sBAAsB,EAAE;QACvCD,SAAS,EAAEA,SAAS,CAACE,WAAW,CAAC,CAAC;QAClCH,OAAO,EAAEA,OAAO,CAACG,WAAW,CAAC;MAC/B,CAAC,CAAC;MAEFR,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;MACA,MAAMlB,kBAAkB,CAAC,CAAC;MAC1B,MAAMS,oBAAoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C,CAAC,SAAS;MACRZ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAIT,OAAO,IAAI,CAACD,aAAa,EAAE;IAC7B,oBACEV,OAAA;MAAKsC,SAAS,EAAC,aAAa;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eACxDzC,OAAA;QAAKsC,SAAS,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,MAAMC,KAAK,GAAG,CAAApC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoC,KAAK,KAAI,CAAC,CAAC;EACxC,MAAMC,MAAM,GAAG,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,MAAM,KAAI,CAAC,CAAC;EAC1C,MAAMC,KAAK,GAAG,CAAAtC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,KAAK,KAAI,CAAC,CAAC;EACxC,MAAMC,UAAU,GAAGvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,UAAU;EAC5C,MAAMC,MAAM,GAAG,CAAAxC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,MAAM,KAAI,EAAE;EAC1C,MAAMC,QAAQ,GAAG,CAAAzC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyC,QAAQ,KAAI,EAAE;EAE9C,oBACEnD,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAG,QAAA,GAEvB,CAAC1B,eAAe,CAACJ,OAAO,iBACvBX,OAAA;MAAKsC,SAAS,EAAE,qBAAqBvB,eAAe,CAACE,SAAS,GAAG,WAAW,GAAG,cAAc,EAAG;MAAAwB,QAAA,EAC7F1B,eAAe,CAACE,SAAS,gBACxBjB,OAAA;QAAKsC,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7BzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1BzC,OAAA;YAAMsC,SAAS,EAAC,aAAa;YAAAG,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1C7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAAyC,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC7C,OAAA;cAAAyC,QAAA,EACGtB,WAAW,gBACVnB,OAAA,CAAAE,SAAA;gBAAAuC,QAAA,EAAE;cAAqC,gBAAE,CAAC,gBAE1CzC,OAAA,CAAAE,SAAA;gBAAAuC,QAAA,GAAE,aAAW,EAAC1B,eAAe,CAACG,QAAQ,GAAG,IAAIS,IAAI,CAACZ,eAAe,CAACG,QAAQ,CAAC,CAACkC,cAAc,CAAC,CAAC,GAAG,OAAO;cAAA,eAAG;YAC1G;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7C,OAAA;UACEsC,SAAS,EAAC,2BAA2B;UACrCe,OAAO,EAAEtB,iBAAkB;UAC3BuB,QAAQ,EAAEnC,WAAY;UAAAsB,QAAA,EAErBtB,WAAW,GAAG,YAAY,GAAG;QAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAEN7C,OAAA;QAAKsC,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7BzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1BzC,OAAA;YAAMsC,SAAS,EAAC,aAAa;YAAAG,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAAyC,QAAA,EAAQ;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7C,OAAA;cAAAyC,QAAA,EAAG;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7C,OAAA;UAAGuD,IAAI,EAAC,UAAU;UAACjB,SAAS,EAAC,2BAA2B;UAAAG,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD7C,OAAA;MAAKsC,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBzC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BzC,OAAA;UAAAyC,QAAA,gBACEzC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,gBACX,EAAChC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,IAAI,EAAC,gBAC5B;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7C,OAAA;YAAGsC,SAAS,EAAC,eAAe;YAAAG,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN7C,OAAA;UAAKsC,SAAS,EAAC,MAAM;UAACC,KAAK,EAAE;YAAEkB,GAAG,EAAE;UAAO,CAAE;UAAAhB,QAAA,eAC3CzC,OAAA;YACE0D,KAAK,EAAE7C,cAAe;YACtB8C,QAAQ,EAAGC,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDpB,SAAS,EAAC,aAAa;YACvBC,KAAK,EAAE;cAAEuB,KAAK,EAAE;YAAO,CAAE;YAAArB,QAAA,gBAEzBzC,OAAA;cAAQ0D,KAAK,EAAC,IAAI;cAAAjB,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC7C,OAAA;cAAQ0D,KAAK,EAAC,KAAK;cAAAjB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzC7C,OAAA;cAAQ0D,KAAK,EAAC,KAAK;cAAAjB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7C,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BzC,OAAA,CAACP,SAAS;UACRsE,KAAK,EAAC,OAAO;UACbL,KAAK,EAAE,EAAArD,YAAA,GAAAyC,KAAK,CAACkB,KAAK,cAAA3D,YAAA,uBAAXA,YAAA,CAAa+C,cAAc,CAAC,CAAC,KAAI,GAAI;UAC5CS,MAAM,EAAC,QAAQ;UACfI,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGrB,KAAK,CAACkB,KAAK,GAAG,KAAK,GAAI;QAAI;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACF7C,OAAA,CAACP,SAAS;UACRsE,KAAK,EAAC,UAAU;UAChBL,KAAK,EAAE,EAAApD,eAAA,GAAAwC,KAAK,CAACsB,QAAQ,cAAA9D,eAAA,uBAAdA,eAAA,CAAgB8C,cAAc,CAAC,CAAC,KAAI,GAAI;UAC/CS,MAAM,EAAC,OAAO;UACdI,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGrB,KAAK,CAACsB,QAAQ,GAAG,IAAI,GAAI;QAAI;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACF7C,OAAA,CAACP,SAAS;UACRsE,KAAK,EAAC,gBAAgB;UACtBL,KAAK,EAAEZ,KAAK,CAACuB,aAAa,IAAI,GAAI;UAClCR,MAAM,EAAC,IAAI;UACXI,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAGrB,KAAK,CAACuB,aAAa,GAAG,EAAE,GAAI;QAAI;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACF7C,OAAA,CAACP,SAAS;UACRsE,KAAK,EAAC,gBAAgB;UACtBL,KAAK,EAAEZ,KAAK,CAACwB,aAAa,IAAI,GAAI;UAClCT,MAAM,EAAC,KAAK;UACZI,IAAI,EAAC,QAAG;UACRC,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAErB,KAAK,CAACwB,aAAa,IAAI;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAKsC,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAE1BzC,OAAA;QAAKsC,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BzC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACN7C,OAAA,CAACN,aAAa;UAAC6E,MAAM,EAAE1D;QAAe;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGN7C,OAAA;QAAKsC,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1BzC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C7C,OAAA;YAAMsC,SAAS,EAAC,YAAY;YAAAG,QAAA,GACzB,EAAAlC,aAAA,GAAAyC,KAAK,CAACwB,MAAM,cAAAjE,aAAA,uBAAZA,aAAA,CAAckE,MAAM,KAAI,CAAC,EAAC,eAC7B;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA,CAACL,YAAY;UAACqD,KAAK,EAAEA,KAAK,CAACwB,MAAM,IAAI;QAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAKsC,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAE1BzC,OAAA;QAAKsC,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BzC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACN7C,OAAA,CAACJ,cAAc;UAACuD,QAAQ,EAAEA,QAAS;UAACF,UAAU,EAAEA;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAGN7C,OAAA;QAAKsC,SAAS,EAAC,MAAM;QAAAG,QAAA,gBACnBzC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BzC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN7C,OAAA,CAACH,YAAY;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLK,MAAM,CAACuB,MAAM,GAAG,CAAC,iBAChBzE,OAAA;MAAKsC,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBzC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAG,QAAA,eAC1BzC,OAAA;UAAIsC,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACN7C,OAAA;QAAKsC,SAAS,EAAC,mBAAmB;QAAAG,QAAA,EAC/BS,MAAM,CAACwB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvB5E,OAAA;UAAiBsC,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAC5CzC,OAAA;YAAMsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAEkC,KAAK,CAACV;UAAI;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD7C,OAAA;YAAKsC,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBzC,OAAA;cAAAyC,QAAA,EAAKkC,KAAK,CAACnB;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrB7C,OAAA;cAAAyC,QAAA,EAAIkC,KAAK,CAACE;YAAW;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA,GALE+B,KAAK;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7C,OAAA;MAAKsC,SAAS,EAAC,MAAM;MAAAG,QAAA,gBACnBzC,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAG,QAAA,eAC1BzC,OAAA;UAAIsC,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACN7C,OAAA;QAAKsC,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BzC,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BzC,OAAA;YAAAyC,QAAA,EAAK,EAAAjC,kBAAA,GAAAuC,MAAM,CAAC+B,UAAU,cAAAtE,kBAAA,uBAAjBA,kBAAA,CAAmB4C,cAAc,CAAC,CAAC,KAAI;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrD7C,OAAA;YAAAyC,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClB7C,OAAA;YAAOsC,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,OACvB,EAACsC,IAAI,CAACC,KAAK,CAACjC,MAAM,CAACkC,QAAQ,IAAI,CAAC,CAAC,CAAC7B,cAAc,CAAC,CAAC,EAAC,MAC1D;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BzC,OAAA;YAAAyC,QAAA,GAAKsC,IAAI,CAACC,KAAK,CAACjC,MAAM,CAACmC,aAAa,IAAI,CAAC,CAAC,EAAC,KAAG;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnD7C,OAAA;YAAAyC,QAAA,EAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvB7C,OAAA;YAAOsC,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,OACvB,EAACsC,IAAI,CAACC,KAAK,CAAC,CAACjC,MAAM,CAACmC,aAAa,IAAI,CAAC,IAAI,CAAC,CAAC,EAAC,SACpD;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BzC,OAAA;YAAAyC,QAAA,GAAKsC,IAAI,CAACC,KAAK,CAACjC,MAAM,CAACoC,QAAQ,IAAI,CAAC,CAAC,EAAC,MAAI;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/C7C,OAAA;YAAAyC,QAAA,EAAG;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChB7C,OAAA;YAAOsC,SAAS,EAAC,YAAY;YAAAG,QAAA,GAC1BsC,IAAI,CAACC,KAAK,CAAC,CAACjC,MAAM,CAACoC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,cAC3C;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3BzC,OAAA;YAAAyC,QAAA,GAAKsC,IAAI,CAACC,KAAK,CAACjC,MAAM,CAACqC,YAAY,IAAI,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnD7C,OAAA;YAAAyC,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrB7C,OAAA;YAAOsC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAOqF,GAAG;MAAA5C,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACzC,EAAA,CA5WID,SAAS;EAAA,QACIX,OAAO,EAC+BD,aAAa;AAAA;AAAA+F,EAAA,GAFhEnF,SAAS;AA8Wf,eAAeA,SAAS;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}