/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { dataplex_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof dataplex_v1.Dataplex;
};
export declare function dataplex(version: 'v1'): dataplex_v1.Dataplex;
export declare function dataplex(options: dataplex_v1.Options): dataplex_v1.Dataplex;
declare const auth: AuthPlus;
export { auth };
export { dataplex_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
