.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  text-decoration: none;
  transition: color 0.3s ease;
}

.navbar-brand:hover {
  color: #667eea;
}

.brand-icon {
  font-size: 1.8rem;
  margin-right: 10px;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.navbar-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: #2c3e50;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.navbar-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-2px);
}

.navbar-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 8px;
}

.navbar-user {
  position: relative;
}

.user-dropdown {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #2c3e50;
}

.user-button:hover {
  background: rgba(102, 126, 234, 0.1);
}

.user-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 10px;
  font-size: 0.9rem;
}

.user-name {
  font-weight: 500;
  margin-right: 8px;
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.user-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
}

.user-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  color: #2c3e50;
  text-decoration: none;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.dropdown-item:first-child {
  border-radius: 12px 12px 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 12px 12px;
}

.logout-btn:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.dropdown-icon {
  font-size: 1.1rem;
  margin-right: 10px;
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background: #2c3e50;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 15px;
  }

  .navbar-menu {
    position: fixed;
    top: 70px;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    flex-direction: column;
    padding: 20px;
    gap: 15px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .navbar-menu.active {
    transform: translateX(0);
  }

  .navbar-item {
    width: 100%;
    justify-content: flex-start;
    padding: 15px 20px;
    font-size: 1.1rem;
  }

  .navbar-toggle {
    display: flex;
  }

  .navbar-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  .navbar-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .navbar-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
  }

  .user-name {
    display: none;
  }

  .dropdown-menu {
    right: -10px;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    height: 60px;
  }

  .navbar-brand {
    font-size: 1.3rem;
  }

  .brand-icon {
    font-size: 1.5rem;
  }

  .navbar-menu {
    top: 60px;
  }
}
