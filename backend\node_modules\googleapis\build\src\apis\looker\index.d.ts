/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { looker_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof looker_v1.Looker;
};
export declare function looker(version: 'v1'): looker_v1.Looker;
export declare function looker(options: looker_v1.Options): looker_v1.Looker;
declare const auth: AuthPlus;
export { auth };
export { looker_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
