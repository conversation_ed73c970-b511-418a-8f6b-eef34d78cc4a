import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading, user, token } = useAuth();

  console.log('🛡️ ProtectedRoute check:', {
    isAuthenticated,
    loading,
    hasUser: !!user,
    hasToken: !!token,
    userName: user?.name
  });

  if (loading) {
    console.log('⏳ ProtectedRoute: Still loading, showing spinner');
    return (
      <div className="flex-center" style={{ minHeight: '50vh' }}>
        <div className="loading-spinner"></div>
        <p style={{ marginTop: '20px', color: '#666' }}>Loading...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('❌ ProtectedRoute: Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('✅ ProtectedRoute: Authenticated, rendering children');
  return children;
};

export default ProtectedRoute;
