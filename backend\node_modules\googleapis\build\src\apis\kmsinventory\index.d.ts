/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { kmsinventory_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof kmsinventory_v1.Kmsinventory;
};
export declare function kmsinventory(version: 'v1'): kmsinventory_v1.Kmsinventory;
export declare function kmsinventory(options: kmsinventory_v1.Options): kmsinventory_v1.Kmsinventory;
declare const auth: AuthPlus;
export { auth };
export { kmsinventory_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
