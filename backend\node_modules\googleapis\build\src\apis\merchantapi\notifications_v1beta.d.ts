import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace merchantapi_notifications_v1beta {
    export interface Options extends GlobalOptions {
        version: 'notifications_v1beta';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Merchant API
     *
     * Programmatically manage your Merchant Center Accounts.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const merchantapi = google.merchantapi('notifications_v1beta');
     * ```
     */
    export class Merchantapi {
        context: APIRequestContext;
        accounts: Resource$Accounts;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Response message for the ListNotificationSubscription method.
     */
    export interface Schema$ListNotificationSubscriptionsResponse {
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The list of notification subscriptions requested by the merchant.
         */
        notificationSubscriptions?: Schema$NotificationSubscription[];
    }
    /**
     * Represents a notification subscription owned by a Merchant account.
     */
    export interface Schema$NotificationSubscription {
        /**
         * If this value is true, the requesting account is notified of the specified event for all managed accounts (can be subaccounts or other linked accounts) including newly added accounts on a daily basis.
         */
        allManagedAccounts?: boolean | null;
        /**
         * URL to be used to push the notification to the merchant.
         */
        callBackUri?: string | null;
        /**
         * Output only. The `name` of the notification configuration. Generated by the Content API upon creation of a new `NotificationSubscription`. The `account` represents the merchant ID of the merchant that owns the configuration. Format: `accounts/{account\}/notificationsubscriptions/{notification_subscription\}`
         */
        name?: string | null;
        /**
         * The event that the merchant wants to be notified about.
         */
        registeredEvent?: string | null;
        /**
         * The `name` of the account you want to receive notifications for. Format: `accounts/{account\}`
         */
        targetAccount?: string | null;
    }
    /**
     * The change that happened to the product including old value, new value, country code as the region code and reporting context.
     */
    export interface Schema$ProductChange {
        /**
         * The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)
         */
        newValue?: string | null;
        /**
         * The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)
         */
        oldValue?: string | null;
        /**
         * Countries that have the change (if applicable). Represented in the ISO 3166 format.
         */
        regionCode?: string | null;
        /**
         * Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)
         */
        reportingContext?: string | null;
    }
    /**
     * The message that the merchant will receive to notify about product status change event
     */
    export interface Schema$ProductStatusChangeMessage {
        /**
         * The target account that owns the entity that changed. Format : `accounts/{merchant_id\}`
         */
        account?: string | null;
        /**
         * The attribute in the resource that changed, in this case it will be always `Status`.
         */
        attribute?: string | null;
        /**
         * A message to describe the change that happened to the product
         */
        changes?: Schema$ProductChange[];
        /**
         * The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.
         */
        eventTime?: string | null;
        /**
         * Optional. The product expiration time. This field will not bet set if the notification is sent for a product deletion event.
         */
        expirationTime?: string | null;
        /**
         * The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id\}`
         */
        managingAccount?: string | null;
        /**
         * The product name. Format: `accounts/{account\}/products/{product\}`
         */
        resource?: string | null;
        /**
         * The product id.
         */
        resourceId?: string | null;
        /**
         * The resource that changed, in this case it will always be `Product`.
         */
        resourceType?: string | null;
    }
    export class Resource$Accounts {
        context: APIRequestContext;
        notificationsubscriptions: Resource$Accounts$Notificationsubscriptions;
        constructor(context: APIRequestContext);
    }
    export class Resource$Accounts$Notificationsubscriptions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a notification subscription for a business. For standalone or subaccounts accounts, the business can create a subscription for self. For MCAs, the business can create a subscription for all managed accounts or for a specific subaccount. We will allow the following types of notification subscriptions to exist together (per business as a subscriber per event type): 1. Subscription for all managed accounts + subscription for self. 2. Multiple "partial" subscriptions for managed accounts + subscription for self. we will not allow (per business as a subscriber per event type): 1. Multiple self subscriptions. 2. Multiple "all managed accounts" subscriptions. 3. "All managed accounts" subscription and partial subscriptions at the same time. 4. Multiple partial subscriptions for the same target account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Accounts$Notificationsubscriptions$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Accounts$Notificationsubscriptions$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$NotificationSubscription>>;
        create(params: Params$Resource$Accounts$Notificationsubscriptions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Accounts$Notificationsubscriptions$Create, options: MethodOptions | BodyResponseCallback<Schema$NotificationSubscription>, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        create(params: Params$Resource$Accounts$Notificationsubscriptions$Create, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        create(callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        /**
         * Deletes a notification subscription for a merchant.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Accounts$Notificationsubscriptions$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Accounts$Notificationsubscriptions$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Accounts$Notificationsubscriptions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Accounts$Notificationsubscriptions$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Accounts$Notificationsubscriptions$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets notification subscriptions for an account.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Notificationsubscriptions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Accounts$Notificationsubscriptions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$NotificationSubscription>>;
        get(params: Params$Resource$Accounts$Notificationsubscriptions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Notificationsubscriptions$Get, options: MethodOptions | BodyResponseCallback<Schema$NotificationSubscription>, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        get(params: Params$Resource$Accounts$Notificationsubscriptions$Get, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        get(callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        /**
         * Gets all the notification subscriptions for a merchant.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Notificationsubscriptions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Accounts$Notificationsubscriptions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListNotificationSubscriptionsResponse>>;
        list(params: Params$Resource$Accounts$Notificationsubscriptions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Notificationsubscriptions$List, options: MethodOptions | BodyResponseCallback<Schema$ListNotificationSubscriptionsResponse>, callback: BodyResponseCallback<Schema$ListNotificationSubscriptionsResponse>): void;
        list(params: Params$Resource$Accounts$Notificationsubscriptions$List, callback: BodyResponseCallback<Schema$ListNotificationSubscriptionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListNotificationSubscriptionsResponse>): void;
        /**
         * Updates an existing notification subscription for a merchant.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Accounts$Notificationsubscriptions$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Accounts$Notificationsubscriptions$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$NotificationSubscription>>;
        patch(params: Params$Resource$Accounts$Notificationsubscriptions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Accounts$Notificationsubscriptions$Patch, options: MethodOptions | BodyResponseCallback<Schema$NotificationSubscription>, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        patch(params: Params$Resource$Accounts$Notificationsubscriptions$Patch, callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
        patch(callback: BodyResponseCallback<Schema$NotificationSubscription>): void;
    }
    export interface Params$Resource$Accounts$Notificationsubscriptions$Create extends StandardParameters {
        /**
         * Required. The merchant account that owns the new notification subscription. Format: `accounts/{account\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$NotificationSubscription;
    }
    export interface Params$Resource$Accounts$Notificationsubscriptions$Delete extends StandardParameters {
        /**
         * Required. The name of the notification subscription to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Notificationsubscriptions$Get extends StandardParameters {
        /**
         * Required. The `name` of the notification subscription.
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Notificationsubscriptions$List extends StandardParameters {
        /**
         * The maximum number of notification subscriptions to return in a page. The default value for `page_size` is 100. The maximum value is `200`. Values above `200` will be coerced to `200`.
         */
        pageSize?: number;
        /**
         * Token (if provided) to retrieve the subsequent page. All other parameters must match the original call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The merchant account who owns the notification subscriptions. Format: `accounts/{account\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Accounts$Notificationsubscriptions$Patch extends StandardParameters {
        /**
         * Output only. The `name` of the notification configuration. Generated by the Content API upon creation of a new `NotificationSubscription`. The `account` represents the merchant ID of the merchant that owns the configuration. Format: `accounts/{account\}/notificationsubscriptions/{notification_subscription\}`
         */
        name?: string;
        /**
         * List of fields being updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$NotificationSubscription;
    }
    export {};
}
