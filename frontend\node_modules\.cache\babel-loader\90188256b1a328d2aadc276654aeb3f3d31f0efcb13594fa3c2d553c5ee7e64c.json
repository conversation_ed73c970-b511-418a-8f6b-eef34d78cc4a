{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _user$name, _user$activityLevel;\n  const {\n    user,\n    updateProfile,\n    changePassword\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profileData, setProfileData] = useState({\n    name: (user === null || user === void 0 ? void 0 : user.name) || '',\n    height: (user === null || user === void 0 ? void 0 : user.height) || '',\n    weight: (user === null || user === void 0 ? void 0 : user.weight) || '',\n    activityLevel: (user === null || user === void 0 ? void 0 : user.activityLevel) || 'moderately_active'\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [message, setMessage] = useState('');\n  const handleProfileUpdate = async e => {\n    e.preventDefault();\n    const result = await updateProfile(profileData);\n    setMessage(result.success ? 'Profile updated successfully!' : result.message);\n  };\n  const handlePasswordChange = async e => {\n    e.preventDefault();\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setMessage('New passwords do not match');\n      return;\n    }\n    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);\n    setMessage(result.success ? 'Password changed successfully!' : result.message);\n    if (result.success) {\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()) || '👤'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: user === null || user === void 0 ? void 0 : user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: user === null || user === void 0 ? void 0 : user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Age: \", (user === null || user === void 0 ? void 0 : user.age) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"BMI: \", (user === null || user === void 0 ? void 0 : user.bmi) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Activity: \", (user === null || user === void 0 ? void 0 : (_user$activityLevel = user.activityLevel) === null || _user$activityLevel === void 0 ? void 0 : _user$activityLevel.replace('_', ' ')) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'profile' ? 'active' : ''}`,\n          onClick: () => setActiveTab('profile'),\n          children: \"Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === 'password' ? 'active' : ''}`,\n          onClick: () => setActiveTab('password'),\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `alert ${message.includes('success') ? 'alert-success' : 'alert-danger'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this), activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleProfileUpdate,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                value: profileData.name,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  name: e.target.value\n                }),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                className: \"form-input\",\n                value: (user === null || user === void 0 ? void 0 : user.email) || '',\n                disabled: true,\n                style: {\n                  background: '#f8f9fa',\n                  color: '#6c757d'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Height (cm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-input\",\n                value: profileData.height,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  height: e.target.value\n                }),\n                min: \"50\",\n                max: \"300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Weight (kg)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"form-input\",\n                value: profileData.weight,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  weight: e.target.value\n                }),\n                min: \"20\",\n                max: \"500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Activity Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select\",\n              value: profileData.activityLevel,\n              onChange: e => setProfileData({\n                ...profileData,\n                activityLevel: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"sedentary\",\n                children: \"Sedentary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"lightly_active\",\n                children: \"Lightly Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"moderately_active\",\n                children: \"Moderately Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"very_active\",\n                children: \"Very Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"extremely_active\",\n                children: \"Extremely Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Update Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), activeTab === 'password' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handlePasswordChange,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Current Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.currentPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                currentPassword: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.newPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                newPassword: e.target.value\n              }),\n              required: true,\n              minLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Confirm New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              className: \"form-input\",\n              value: passwordData.confirmPassword,\n              onChange: e => setPasswordData({\n                ...passwordData,\n                confirmPassword: e.target.value\n              }),\n              required: true,\n              minLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"Change Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .profile-page { padding: 0; }\n        .profile-container { max-width: 800px; margin: 0 auto; }\n        .profile-header { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; margin-bottom: 30px; display: flex; align-items: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .user-avatar { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700; margin-right: 25px; }\n        .user-info h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .user-info p { margin: 0 0 10px 0; color: #7f8c8d; }\n        .user-stats { display: flex; gap: 20px; font-size: 0.9rem; color: #667eea; }\n        .profile-tabs { display: flex; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 5px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-button { flex: 1; padding: 15px 20px; border: none; background: none; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; color: #7f8c8d; }\n        .tab-button.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n        .tab-content { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-content h2 { margin: 0 0 25px 0; color: #2c3e50; }\n        @media (max-width: 768px) {\n          .profile-header { flex-direction: column; text-align: center; }\n          .user-avatar { margin-right: 0; margin-bottom: 20px; }\n          .user-stats { justify-content: center; flex-wrap: wrap; }\n          .profile-tabs { flex-direction: column; }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"B/jj/9b5Ob7Bk7TnOpej7siblFE=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "Profile", "_s", "_user$name", "_user$activityLevel", "user", "updateProfile", "changePassword", "activeTab", "setActiveTab", "profileData", "setProfileData", "name", "height", "weight", "activityLevel", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "message", "setMessage", "handleProfileUpdate", "e", "preventDefault", "result", "success", "handlePasswordChange", "className", "children", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "age", "bmi", "replace", "onClick", "includes", "onSubmit", "type", "value", "onChange", "target", "required", "disabled", "style", "background", "color", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Profile.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Profile = () => {\n  const { user, updateProfile, changePassword } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profileData, setProfileData] = useState({\n    name: user?.name || '',\n    height: user?.height || '',\n    weight: user?.weight || '',\n    activityLevel: user?.activityLevel || 'moderately_active'\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [message, setMessage] = useState('');\n\n  const handleProfileUpdate = async (e) => {\n    e.preventDefault();\n    const result = await updateProfile(profileData);\n    setMessage(result.success ? 'Profile updated successfully!' : result.message);\n  };\n\n  const handlePasswordChange = async (e) => {\n    e.preventDefault();\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setMessage('New passwords do not match');\n      return;\n    }\n    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);\n    setMessage(result.success ? 'Password changed successfully!' : result.message);\n    if (result.success) {\n      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n    }\n  };\n\n  return (\n    <div className=\"profile-page\">\n      <div className=\"profile-container\">\n        <div className=\"profile-header\">\n          <div className=\"user-avatar\">\n            {user?.name?.charAt(0).toUpperCase() || '👤'}\n          </div>\n          <div className=\"user-info\">\n            <h1>{user?.name}</h1>\n            <p>{user?.email}</p>\n            <div className=\"user-stats\">\n              <span>Age: {user?.age || 'N/A'}</span>\n              <span>BMI: {user?.bmi || 'N/A'}</span>\n              <span>Activity: {user?.activityLevel?.replace('_', ' ') || 'N/A'}</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"profile-tabs\">\n          <button \n            className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}\n            onClick={() => setActiveTab('profile')}\n          >\n            Profile Settings\n          </button>\n          <button \n            className={`tab-button ${activeTab === 'password' ? 'active' : ''}`}\n            onClick={() => setActiveTab('password')}\n          >\n            Change Password\n          </button>\n        </div>\n\n        {message && (\n          <div className={`alert ${message.includes('success') ? 'alert-success' : 'alert-danger'}`}>\n            {message}\n          </div>\n        )}\n\n        {activeTab === 'profile' && (\n          <div className=\"tab-content\">\n            <h2>Profile Information</h2>\n            <form onSubmit={handleProfileUpdate}>\n              <div className=\"grid grid-2\">\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"form-input\"\n                    value={profileData.name}\n                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}\n                    required\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    className=\"form-input\"\n                    value={user?.email || ''}\n                    disabled\n                    style={{ background: '#f8f9fa', color: '#6c757d' }}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Height (cm)</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-input\"\n                    value={profileData.height}\n                    onChange={(e) => setProfileData({...profileData, height: e.target.value})}\n                    min=\"50\"\n                    max=\"300\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"form-label\">Weight (kg)</label>\n                  <input\n                    type=\"number\"\n                    className=\"form-input\"\n                    value={profileData.weight}\n                    onChange={(e) => setProfileData({...profileData, weight: e.target.value})}\n                    min=\"20\"\n                    max=\"500\"\n                  />\n                </div>\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Activity Level</label>\n                <select\n                  className=\"form-select\"\n                  value={profileData.activityLevel}\n                  onChange={(e) => setProfileData({...profileData, activityLevel: e.target.value})}\n                >\n                  <option value=\"sedentary\">Sedentary</option>\n                  <option value=\"lightly_active\">Lightly Active</option>\n                  <option value=\"moderately_active\">Moderately Active</option>\n                  <option value=\"very_active\">Very Active</option>\n                  <option value=\"extremely_active\">Extremely Active</option>\n                </select>\n              </div>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Update Profile\n              </button>\n            </form>\n          </div>\n        )}\n\n        {activeTab === 'password' && (\n          <div className=\"tab-content\">\n            <h2>Change Password</h2>\n            <form onSubmit={handlePasswordChange}>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Current Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.currentPassword}\n                  onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">New Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.newPassword}\n                  onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}\n                  required\n                  minLength=\"6\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label className=\"form-label\">Confirm New Password</label>\n                <input\n                  type=\"password\"\n                  className=\"form-input\"\n                  value={passwordData.confirmPassword}\n                  onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}\n                  required\n                  minLength=\"6\"\n                />\n              </div>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                Change Password\n              </button>\n            </form>\n          </div>\n        )}\n      </div>\n\n      <style jsx>{`\n        .profile-page { padding: 0; }\n        .profile-container { max-width: 800px; margin: 0 auto; }\n        .profile-header { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; margin-bottom: 30px; display: flex; align-items: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .user-avatar { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700; margin-right: 25px; }\n        .user-info h1 { margin: 0 0 5px 0; color: #2c3e50; }\n        .user-info p { margin: 0 0 10px 0; color: #7f8c8d; }\n        .user-stats { display: flex; gap: 20px; font-size: 0.9rem; color: #667eea; }\n        .profile-tabs { display: flex; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 5px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-button { flex: 1; padding: 15px 20px; border: none; background: none; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; color: #7f8c8d; }\n        .tab-button.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n        .tab-content { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }\n        .tab-content h2 { margin: 0 0 25px 0; color: #2c3e50; }\n        @media (max-width: 768px) {\n          .profile-header { flex-direction: column; text-align: center; }\n          .user-avatar { margin-right: 0; margin-bottom: 20px; }\n          .user-stats { justify-content: center; flex-wrap: wrap; }\n          .profile-tabs { flex-direction: column; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,mBAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAGT,OAAO,CAAC,CAAC;EACzD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC;IAC7Ce,IAAI,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,KAAI,EAAE;IACtBC,MAAM,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,MAAM,KAAI,EAAE;IAC1BC,MAAM,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,KAAI,EAAE;IAC1BC,aAAa,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,aAAa,KAAI;EACxC,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC;IAC/CqB,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM0B,mBAAmB,GAAG,MAAOC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,MAAMpB,aAAa,CAACI,WAAW,CAAC;IAC/CY,UAAU,CAACI,MAAM,CAACC,OAAO,GAAG,+BAA+B,GAAGD,MAAM,CAACL,OAAO,CAAC;EAC/E,CAAC;EAED,MAAMO,oBAAoB,GAAG,MAAOJ,CAAC,IAAK;IACxCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIT,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC7DE,UAAU,CAAC,4BAA4B,CAAC;MACxC;IACF;IACA,MAAMI,MAAM,GAAG,MAAMnB,cAAc,CAACS,YAAY,CAACE,eAAe,EAAEF,YAAY,CAACG,WAAW,CAAC;IAC3FG,UAAU,CAACI,MAAM,CAACC,OAAO,GAAG,gCAAgC,GAAGD,MAAM,CAACL,OAAO,CAAC;IAC9E,IAAIK,MAAM,CAACC,OAAO,EAAE;MAClBV,eAAe,CAAC;QAAEC,eAAe,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,eAAe,EAAE;MAAG,CAAC,CAAC;IAChF;EACF,CAAC;EAED,oBACEpB,OAAA;IAAK6B,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B9B,OAAA;MAAK6B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9B,OAAA;QAAK6B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9B,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB,CAAAzB,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEO,IAAI,cAAAT,UAAA,uBAAVA,UAAA,CAAY4B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNpC,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9B,OAAA;YAAA8B,QAAA,EAAKzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;UAAI;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBpC,OAAA;YAAA8B,QAAA,EAAIzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAA8B,QAAA,GAAM,OAAK,EAAC,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,GAAG,KAAI,KAAK;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpC,OAAA;cAAA8B,QAAA,GAAM,OAAK,EAAC,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,GAAG,KAAI,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpC,OAAA;cAAA8B,QAAA,GAAM,YAAU,EAAC,CAAAzB,IAAI,aAAJA,IAAI,wBAAAD,mBAAA,GAAJC,IAAI,CAAEU,aAAa,cAAAX,mBAAA,uBAAnBA,mBAAA,CAAqBoC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI,KAAK;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAK6B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9B,OAAA;UACE6B,SAAS,EAAE,cAAcrB,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;UACnEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,SAAS,CAAE;UAAAqB,QAAA,EACxC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpC,OAAA;UACE6B,SAAS,EAAE,cAAcrB,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACpEiC,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC,UAAU,CAAE;UAAAqB,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELf,OAAO,iBACNrB,OAAA;QAAK6B,SAAS,EAAE,SAASR,OAAO,CAACqB,QAAQ,CAAC,SAAS,CAAC,GAAG,eAAe,GAAG,cAAc,EAAG;QAAAZ,QAAA,EACvFT;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAEA5B,SAAS,KAAK,SAAS,iBACtBR,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAA8B,QAAA,EAAI;QAAmB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BpC,OAAA;UAAM2C,QAAQ,EAAEpB,mBAAoB;UAAAO,QAAA,gBAClC9B,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/CpC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACE,IAAK;gBACxBkC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEE,IAAI,EAAEY,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBACxEG,QAAQ;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CpC,OAAA;gBACE4C,IAAI,EAAC,OAAO;gBACZf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,KAAK,KAAI,EAAG;gBACzBY,QAAQ;gBACRC,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDpC,OAAA;gBACE4C,IAAI,EAAC,QAAQ;gBACbf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACG,MAAO;gBAC1BiC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEG,MAAM,EAAEW,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC1EQ,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAO6B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDpC,OAAA;gBACE4C,IAAI,EAAC,QAAQ;gBACbf,SAAS,EAAC,YAAY;gBACtBgB,KAAK,EAAEnC,WAAW,CAACI,MAAO;gBAC1BgC,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEI,MAAM,EAAEU,CAAC,CAACuB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC1EQ,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpDpC,OAAA;cACE6B,SAAS,EAAC,aAAa;cACvBgB,KAAK,EAAEnC,WAAW,CAACK,aAAc;cACjC+B,QAAQ,EAAGtB,CAAC,IAAKb,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,aAAa,EAAES,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cAAAf,QAAA,gBAEjF9B,OAAA;gBAAQ6C,KAAK,EAAC,WAAW;gBAAAf,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CpC,OAAA;gBAAQ6C,KAAK,EAAC,gBAAgB;gBAAAf,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDpC,OAAA;gBAAQ6C,KAAK,EAAC,mBAAmB;gBAAAf,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5DpC,OAAA;gBAAQ6C,KAAK,EAAC,aAAa;gBAAAf,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDpC,OAAA;gBAAQ6C,KAAK,EAAC,kBAAkB;gBAAAf,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpC,OAAA;YAAQ4C,IAAI,EAAC,QAAQ;YAACf,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEA5B,SAAS,KAAK,UAAU,iBACvBR,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAA8B,QAAA,EAAI;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBpC,OAAA;UAAM2C,QAAQ,EAAEf,oBAAqB;UAAAE,QAAA,gBACnC9B,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACE,eAAgB;cACpC4B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEE,eAAe,EAAEM,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrFG,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACG,WAAY;cAChC2B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEG,WAAW,EAAEK,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACjFG,QAAQ;cACRO,SAAS,EAAC;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAK6B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9B,OAAA;cAAO6B,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1DpC,OAAA;cACE4C,IAAI,EAAC,UAAU;cACff,SAAS,EAAC,YAAY;cACtBgB,KAAK,EAAE7B,YAAY,CAACI,eAAgB;cACpC0B,QAAQ,EAAGtB,CAAC,IAAKP,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEI,eAAe,EAAEI,CAAC,CAACuB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrFG,QAAQ;cACRO,SAAS,EAAC;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAQ4C,IAAI,EAAC,QAAQ;YAACf,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENpC,OAAA;MAAOwD,GAAG;MAAA1B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClC,EAAA,CAjNID,OAAO;EAAA,QACqCH,OAAO;AAAA;AAAA2D,EAAA,GADnDxD,OAAO;AAmNb,eAAeA,OAAO;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}