{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\pages\\\\Chatbot.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Chatbot = () => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [suggestions, setSuggestions] = useState([]);\n  const messagesEndRef = useRef(null);\n  const {\n    chatWithBot\n  } = useHealthData();\n  useEffect(() => {\n    // Initial welcome message\n    setMessages([{\n      id: 1,\n      text: \"Hello! I'm your health assistant. I can help you with questions about fitness, nutrition, sleep, and understanding your health data. How can I assist you today?\",\n      sender: 'bot',\n      timestamp: new Date()\n    }]);\n\n    // Load initial suggestions\n    setSuggestions(['How can I improve my sleep quality?', 'What\\'s a healthy heart rate?', 'How many steps should I take daily?', 'Tips for staying motivated to exercise', 'How does stress affect my health?', 'What should I know about my BMI?']);\n  }, []);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleSendMessage = async (messageText = inputMessage) => {\n    if (!messageText.trim()) return;\n    const userMessage = {\n      id: Date.now(),\n      text: messageText,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate typing delay\n    setTimeout(async () => {\n      const result = await chatWithBot(messageText);\n      const botMessage = {\n        id: Date.now() + 1,\n        text: result.success ? result.data.botResponse : \"I'm sorry, I'm having trouble right now. Please try again.\",\n        sender: 'bot',\n        timestamp: new Date(),\n        suggestions: result.success ? result.data.suggestions : []\n      };\n      setMessages(prev => [...prev, botMessage]);\n      setIsTyping(false);\n\n      // Update suggestions if provided\n      if (result.success && result.data.suggestions) {\n        setSuggestions(result.data.suggestions);\n      }\n    }, 1000);\n  };\n  const handleSuggestionClick = suggestion => {\n    handleSendMessage(suggestion);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chatbot-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bot-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bot-avatar\",\n            children: \"\\uD83E\\uDD16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bot-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Health Assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"bot-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-dot\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), \"Online - Ready to help\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-messages\",\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${message.sender === 'user' ? 'user-message' : 'bot-message'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-text\",\n              children: message.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: formatTime(message.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message bot-message\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"typing-indicator\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-input-section\",\n        children: [suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"suggestions\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"suggestions-title\",\n            children: \"Suggested questions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"suggestions-list\",\n            children: suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"suggestion-chip\",\n              onClick: () => handleSuggestionClick(suggestion),\n              children: suggestion\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Ask me about your health, fitness, or wellness...\",\n            className: \"message-input\",\n            rows: \"1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSendMessage(),\n            disabled: !inputMessage.trim() || isTyping,\n            className: \"send-button\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"send-icon\",\n              children: \"\\uD83D\\uDCE4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .chatbot-page {\n          height: calc(100vh - 140px);\n          display: flex;\n          justify-content: center;\n          padding: 0;\n        }\n\n        .chat-container {\n          width: 100%;\n          max-width: 800px;\n          background: rgba(255, 255, 255, 0.95);\n          border-radius: 15px;\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          display: flex;\n          flex-direction: column;\n          height: 100%;\n        }\n\n        .chat-header {\n          padding: 20px;\n          border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          border-radius: 15px 15px 0 0;\n          color: white;\n        }\n\n        .bot-info {\n          display: flex;\n          align-items: center;\n        }\n\n        .bot-avatar {\n          width: 50px;\n          height: 50px;\n          border-radius: 50%;\n          background: rgba(255, 255, 255, 0.2);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          margin-right: 15px;\n        }\n\n        .bot-details h2 {\n          margin: 0 0 5px 0;\n          font-size: 1.3rem;\n          font-weight: 600;\n        }\n\n        .bot-status {\n          margin: 0;\n          font-size: 0.9rem;\n          opacity: 0.9;\n          display: flex;\n          align-items: center;\n        }\n\n        .status-dot {\n          width: 8px;\n          height: 8px;\n          background: #27ae60;\n          border-radius: 50%;\n          margin-right: 8px;\n          animation: pulse 2s infinite;\n        }\n\n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n\n        .chat-messages {\n          flex: 1;\n          padding: 20px;\n          overflow-y: auto;\n          display: flex;\n          flex-direction: column;\n          gap: 15px;\n        }\n\n        .message {\n          display: flex;\n          max-width: 80%;\n        }\n\n        .user-message {\n          align-self: flex-end;\n        }\n\n        .bot-message {\n          align-self: flex-start;\n        }\n\n        .message-content {\n          background: #f8f9fa;\n          padding: 12px 16px;\n          border-radius: 18px;\n          position: relative;\n        }\n\n        .user-message .message-content {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          border-bottom-right-radius: 4px;\n        }\n\n        .bot-message .message-content {\n          background: #f8f9fa;\n          color: #2c3e50;\n          border-bottom-left-radius: 4px;\n        }\n\n        .message-text {\n          font-size: 0.95rem;\n          line-height: 1.4;\n          margin-bottom: 5px;\n        }\n\n        .message-time {\n          font-size: 0.75rem;\n          opacity: 0.7;\n          text-align: right;\n        }\n\n        .typing-indicator {\n          display: flex;\n          gap: 4px;\n          padding: 8px 0;\n        }\n\n        .typing-indicator span {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background: #667eea;\n          animation: typing 1.4s infinite ease-in-out;\n        }\n\n        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }\n        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }\n\n        @keyframes typing {\n          0%, 80%, 100% { transform: scale(0); opacity: 0.5; }\n          40% { transform: scale(1); opacity: 1; }\n        }\n\n        .chat-input-section {\n          padding: 20px;\n          border-top: 1px solid rgba(0, 0, 0, 0.1);\n        }\n\n        .suggestions {\n          margin-bottom: 15px;\n        }\n\n        .suggestions-title {\n          margin: 0 0 10px 0;\n          font-size: 0.9rem;\n          color: #7f8c8d;\n          font-weight: 500;\n        }\n\n        .suggestions-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 8px;\n        }\n\n        .suggestion-chip {\n          background: rgba(102, 126, 234, 0.1);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 20px;\n          padding: 6px 12px;\n          font-size: 0.85rem;\n          color: #667eea;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .suggestion-chip:hover {\n          background: rgba(102, 126, 234, 0.2);\n          transform: translateY(-1px);\n        }\n\n        .chat-input {\n          display: flex;\n          gap: 10px;\n          align-items: flex-end;\n        }\n\n        .message-input {\n          flex: 1;\n          border: 2px solid #ecf0f1;\n          border-radius: 20px;\n          padding: 12px 16px;\n          font-size: 0.95rem;\n          resize: none;\n          max-height: 100px;\n          background: rgba(255, 255, 255, 0.9);\n          transition: border-color 0.3s ease;\n        }\n\n        .message-input:focus {\n          outline: none;\n          border-color: #667eea;\n          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n        }\n\n        .send-button {\n          width: 45px;\n          height: 45px;\n          border: none;\n          border-radius: 50%;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.3s ease;\n        }\n\n        .send-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);\n        }\n\n        .send-button:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n\n        .send-icon {\n          font-size: 1.1rem;\n        }\n\n        @media (max-width: 768px) {\n          .chatbot-page {\n            height: calc(100vh - 120px);\n            padding: 0;\n          }\n\n          .chat-container {\n            border-radius: 0;\n            height: 100%;\n          }\n\n          .chat-header {\n            border-radius: 0;\n            padding: 15px;\n          }\n\n          .bot-avatar {\n            width: 40px;\n            height: 40px;\n            font-size: 1.2rem;\n          }\n\n          .bot-details h2 {\n            font-size: 1.1rem;\n          }\n\n          .message {\n            max-width: 90%;\n          }\n\n          .suggestions-list {\n            flex-direction: column;\n          }\n\n          .suggestion-chip {\n            text-align: left;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(Chatbot, \"Tz1oIpJoOERtYqZ7nkqQM6Nz6Yc=\", false, function () {\n  return [useHealthData];\n});\n_c = Chatbot;\nexport default Chatbot;\nvar _c;\n$RefreshReg$(_c, \"Chatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useHealthData", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "messages", "setMessages", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "suggestions", "setSuggestions", "messagesEndRef", "chatWithBot", "id", "text", "sender", "timestamp", "Date", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "messageText", "trim", "userMessage", "now", "prev", "setTimeout", "result", "botMessage", "success", "data", "botResponse", "handleSuggestionClick", "suggestion", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTime", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "ref", "length", "index", "onClick", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/pages/Chatbot.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useHealthData } from '../contexts/HealthDataContext';\n\nconst Chatbot = () => {\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [suggestions, setSuggestions] = useState([]);\n  const messagesEndRef = useRef(null);\n  const { chatWithBot } = useHealthData();\n\n  useEffect(() => {\n    // Initial welcome message\n    setMessages([\n      {\n        id: 1,\n        text: \"Hello! I'm your health assistant. I can help you with questions about fitness, nutrition, sleep, and understanding your health data. How can I assist you today?\",\n        sender: 'bot',\n        timestamp: new Date()\n      }\n    ]);\n\n    // Load initial suggestions\n    setSuggestions([\n      'How can I improve my sleep quality?',\n      'What\\'s a healthy heart rate?',\n      'How many steps should I take daily?',\n      'Tips for staying motivated to exercise',\n      'How does stress affect my health?',\n      'What should I know about my BMI?'\n    ]);\n  }, []);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = async (messageText = inputMessage) => {\n    if (!messageText.trim()) return;\n\n    const userMessage = {\n      id: Date.now(),\n      text: messageText,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    // Simulate typing delay\n    setTimeout(async () => {\n      const result = await chatWithBot(messageText);\n      \n      const botMessage = {\n        id: Date.now() + 1,\n        text: result.success ? result.data.botResponse : \"I'm sorry, I'm having trouble right now. Please try again.\",\n        sender: 'bot',\n        timestamp: new Date(),\n        suggestions: result.success ? result.data.suggestions : []\n      };\n\n      setMessages(prev => [...prev, botMessage]);\n      setIsTyping(false);\n\n      // Update suggestions if provided\n      if (result.success && result.data.suggestions) {\n        setSuggestions(result.data.suggestions);\n      }\n    }, 1000);\n  };\n\n  const handleSuggestionClick = (suggestion) => {\n    handleSendMessage(suggestion);\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit' \n    });\n  };\n\n  return (\n    <div className=\"chatbot-page\">\n      <div className=\"chat-container\">\n        <div className=\"chat-header\">\n          <div className=\"bot-info\">\n            <div className=\"bot-avatar\">🤖</div>\n            <div className=\"bot-details\">\n              <h2>Health Assistant</h2>\n              <p className=\"bot-status\">\n                <span className=\"status-dot\"></span>\n                Online - Ready to help\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"chat-messages\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`message ${message.sender === 'user' ? 'user-message' : 'bot-message'}`}\n            >\n              <div className=\"message-content\">\n                <div className=\"message-text\">{message.text}</div>\n                <div className=\"message-time\">{formatTime(message.timestamp)}</div>\n              </div>\n            </div>\n          ))}\n\n          {isTyping && (\n            <div className=\"message bot-message\">\n              <div className=\"message-content\">\n                <div className=\"typing-indicator\">\n                  <span></span>\n                  <span></span>\n                  <span></span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          <div ref={messagesEndRef} />\n        </div>\n\n        <div className=\"chat-input-section\">\n          {suggestions.length > 0 && (\n            <div className=\"suggestions\">\n              <p className=\"suggestions-title\">Suggested questions:</p>\n              <div className=\"suggestions-list\">\n                {suggestions.map((suggestion, index) => (\n                  <button\n                    key={index}\n                    className=\"suggestion-chip\"\n                    onClick={() => handleSuggestionClick(suggestion)}\n                  >\n                    {suggestion}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"chat-input\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask me about your health, fitness, or wellness...\"\n              className=\"message-input\"\n              rows=\"1\"\n            />\n            <button\n              onClick={() => handleSendMessage()}\n              disabled={!inputMessage.trim() || isTyping}\n              className=\"send-button\"\n            >\n              <span className=\"send-icon\">📤</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .chatbot-page {\n          height: calc(100vh - 140px);\n          display: flex;\n          justify-content: center;\n          padding: 0;\n        }\n\n        .chat-container {\n          width: 100%;\n          max-width: 800px;\n          background: rgba(255, 255, 255, 0.95);\n          border-radius: 15px;\n          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          display: flex;\n          flex-direction: column;\n          height: 100%;\n        }\n\n        .chat-header {\n          padding: 20px;\n          border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          border-radius: 15px 15px 0 0;\n          color: white;\n        }\n\n        .bot-info {\n          display: flex;\n          align-items: center;\n        }\n\n        .bot-avatar {\n          width: 50px;\n          height: 50px;\n          border-radius: 50%;\n          background: rgba(255, 255, 255, 0.2);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          margin-right: 15px;\n        }\n\n        .bot-details h2 {\n          margin: 0 0 5px 0;\n          font-size: 1.3rem;\n          font-weight: 600;\n        }\n\n        .bot-status {\n          margin: 0;\n          font-size: 0.9rem;\n          opacity: 0.9;\n          display: flex;\n          align-items: center;\n        }\n\n        .status-dot {\n          width: 8px;\n          height: 8px;\n          background: #27ae60;\n          border-radius: 50%;\n          margin-right: 8px;\n          animation: pulse 2s infinite;\n        }\n\n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n\n        .chat-messages {\n          flex: 1;\n          padding: 20px;\n          overflow-y: auto;\n          display: flex;\n          flex-direction: column;\n          gap: 15px;\n        }\n\n        .message {\n          display: flex;\n          max-width: 80%;\n        }\n\n        .user-message {\n          align-self: flex-end;\n        }\n\n        .bot-message {\n          align-self: flex-start;\n        }\n\n        .message-content {\n          background: #f8f9fa;\n          padding: 12px 16px;\n          border-radius: 18px;\n          position: relative;\n        }\n\n        .user-message .message-content {\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          border-bottom-right-radius: 4px;\n        }\n\n        .bot-message .message-content {\n          background: #f8f9fa;\n          color: #2c3e50;\n          border-bottom-left-radius: 4px;\n        }\n\n        .message-text {\n          font-size: 0.95rem;\n          line-height: 1.4;\n          margin-bottom: 5px;\n        }\n\n        .message-time {\n          font-size: 0.75rem;\n          opacity: 0.7;\n          text-align: right;\n        }\n\n        .typing-indicator {\n          display: flex;\n          gap: 4px;\n          padding: 8px 0;\n        }\n\n        .typing-indicator span {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background: #667eea;\n          animation: typing 1.4s infinite ease-in-out;\n        }\n\n        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }\n        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }\n\n        @keyframes typing {\n          0%, 80%, 100% { transform: scale(0); opacity: 0.5; }\n          40% { transform: scale(1); opacity: 1; }\n        }\n\n        .chat-input-section {\n          padding: 20px;\n          border-top: 1px solid rgba(0, 0, 0, 0.1);\n        }\n\n        .suggestions {\n          margin-bottom: 15px;\n        }\n\n        .suggestions-title {\n          margin: 0 0 10px 0;\n          font-size: 0.9rem;\n          color: #7f8c8d;\n          font-weight: 500;\n        }\n\n        .suggestions-list {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 8px;\n        }\n\n        .suggestion-chip {\n          background: rgba(102, 126, 234, 0.1);\n          border: 1px solid rgba(102, 126, 234, 0.2);\n          border-radius: 20px;\n          padding: 6px 12px;\n          font-size: 0.85rem;\n          color: #667eea;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .suggestion-chip:hover {\n          background: rgba(102, 126, 234, 0.2);\n          transform: translateY(-1px);\n        }\n\n        .chat-input {\n          display: flex;\n          gap: 10px;\n          align-items: flex-end;\n        }\n\n        .message-input {\n          flex: 1;\n          border: 2px solid #ecf0f1;\n          border-radius: 20px;\n          padding: 12px 16px;\n          font-size: 0.95rem;\n          resize: none;\n          max-height: 100px;\n          background: rgba(255, 255, 255, 0.9);\n          transition: border-color 0.3s ease;\n        }\n\n        .message-input:focus {\n          outline: none;\n          border-color: #667eea;\n          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n        }\n\n        .send-button {\n          width: 45px;\n          height: 45px;\n          border: none;\n          border-radius: 50%;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.3s ease;\n        }\n\n        .send-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);\n        }\n\n        .send-button:disabled {\n          opacity: 0.5;\n          cursor: not-allowed;\n        }\n\n        .send-icon {\n          font-size: 1.1rem;\n        }\n\n        @media (max-width: 768px) {\n          .chatbot-page {\n            height: calc(100vh - 120px);\n            padding: 0;\n          }\n\n          .chat-container {\n            border-radius: 0;\n            height: 100%;\n          }\n\n          .chat-header {\n            border-radius: 0;\n            padding: 15px;\n          }\n\n          .bot-avatar {\n            width: 40px;\n            height: 40px;\n            font-size: 1.2rem;\n          }\n\n          .bot-details h2 {\n            font-size: 1.1rem;\n          }\n\n          .message {\n            max-width: 90%;\n          }\n\n          .suggestions-list {\n            flex-direction: column;\n          }\n\n          .suggestion-chip {\n            text-align: left;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Chatbot;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMgB,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM;IAAEe;EAAY,CAAC,GAAGd,aAAa,CAAC,CAAC;EAEvCF,SAAS,CAAC,MAAM;IACd;IACAQ,WAAW,CAAC,CACV;MACES,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kKAAkK;MACxKC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;;IAEF;IACAP,cAAc,CAAC,CACb,qCAAqC,EACrC,+BAA+B,EAC/B,qCAAqC,EACrC,wCAAwC,EACxC,mCAAmC,EACnC,kCAAkC,CACnC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAENd,SAAS,CAAC,MAAM;IACdsB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EAEd,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAR,cAAc,CAACS,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,WAAW,GAAGnB,YAAY,KAAK;IAC9D,IAAI,CAACmB,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,MAAMC,WAAW,GAAG;MAClBb,EAAE,EAAEI,IAAI,CAACU,GAAG,CAAC,CAAC;MACdb,IAAI,EAAEU,WAAW;MACjBT,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDb,WAAW,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3CpB,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAqB,UAAU,CAAC,YAAY;MACrB,MAAMC,MAAM,GAAG,MAAMlB,WAAW,CAACY,WAAW,CAAC;MAE7C,MAAMO,UAAU,GAAG;QACjBlB,EAAE,EAAEI,IAAI,CAACU,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBb,IAAI,EAAEgB,MAAM,CAACE,OAAO,GAAGF,MAAM,CAACG,IAAI,CAACC,WAAW,GAAG,4DAA4D;QAC7GnB,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBR,WAAW,EAAEqB,MAAM,CAACE,OAAO,GAAGF,MAAM,CAACG,IAAI,CAACxB,WAAW,GAAG;MAC1D,CAAC;MAEDL,WAAW,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEG,UAAU,CAAC,CAAC;MAC1CvB,WAAW,CAAC,KAAK,CAAC;;MAElB;MACA,IAAIsB,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACG,IAAI,CAACxB,WAAW,EAAE;QAC7CC,cAAc,CAACoB,MAAM,CAACG,IAAI,CAACxB,WAAW,CAAC;MACzC;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM0B,qBAAqB,GAAIC,UAAU,IAAK;IAC5Cb,iBAAiB,CAACa,UAAU,CAAC;EAC/B,CAAC;EAED,MAAMC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBlB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMmB,UAAU,GAAI1B,SAAS,IAAK;IAChC,OAAOA,SAAS,CAAC2B,kBAAkB,CAAC,OAAO,EAAE;MAC3CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7C,OAAA;IAAK8C,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B/C,OAAA;MAAK8C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/C,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B/C,OAAA;UAAK8C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/C,OAAA;YAAK8C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpCnD,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBnD,OAAA;cAAG8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB/C,OAAA;gBAAM8C,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,0BAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3B5C,QAAQ,CAACiD,GAAG,CAAEC,OAAO,iBACpBrD,OAAA;UAEE8C,SAAS,EAAE,WAAWO,OAAO,CAACtC,MAAM,KAAK,MAAM,GAAG,cAAc,GAAG,aAAa,EAAG;UAAAgC,QAAA,eAEnF/C,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B/C,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,OAAO,CAACvC;YAAI;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDnD,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEL,UAAU,CAACW,OAAO,CAACrC,SAAS;YAAC;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC,GANDE,OAAO,CAACxC,EAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOZ,CACN,CAAC,EAED5C,QAAQ,iBACPP,OAAA;UAAK8C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC/C,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B/C,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/C,OAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnD,OAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnD,OAAA;UAAKsD,GAAG,EAAE3C;QAAe;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAChCtC,WAAW,CAAC8C,MAAM,GAAG,CAAC,iBACrBvD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/C,OAAA;YAAG8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDnD,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BtC,WAAW,CAAC2C,GAAG,CAAC,CAAChB,UAAU,EAAEoB,KAAK,kBACjCxD,OAAA;cAEE8C,SAAS,EAAC,iBAAiB;cAC3BW,OAAO,EAAEA,CAAA,KAAMtB,qBAAqB,CAACC,UAAU,CAAE;cAAAW,QAAA,EAEhDX;YAAU,GAJNoB,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnD,OAAA;UAAK8C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/C,OAAA;YACE0D,KAAK,EAAErD,YAAa;YACpBsD,QAAQ,EAAGrB,CAAC,IAAKhC,eAAe,CAACgC,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;YACjDG,UAAU,EAAExB,cAAe;YAC3ByB,WAAW,EAAC,mDAAmD;YAC/DhB,SAAS,EAAC,eAAe;YACzBiB,IAAI,EAAC;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnD,OAAA;YACEyD,OAAO,EAAEA,CAAA,KAAMlC,iBAAiB,CAAC,CAAE;YACnCyC,QAAQ,EAAE,CAAC3D,YAAY,CAACoB,IAAI,CAAC,CAAC,IAAIlB,QAAS;YAC3CuC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAEvB/C,OAAA;cAAM8C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnD,OAAA;MAAOiE,GAAG;MAAAlB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjD,EAAA,CAvcID,OAAO;EAAA,QAMaH,aAAa;AAAA;AAAAoE,EAAA,GANjCjE,OAAO;AAycb,eAAeA,OAAO;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}