/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { workloadmanager_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof workloadmanager_v1.Workloadmanager;
};
export declare function workloadmanager(version: 'v1'): workloadmanager_v1.Workloadmanager;
export declare function workloadmanager(options: workloadmanager_v1.Options): workloadmanager_v1.Workloadmanager;
declare const auth: AuthPlus;
export { auth };
export { workloadmanager_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
