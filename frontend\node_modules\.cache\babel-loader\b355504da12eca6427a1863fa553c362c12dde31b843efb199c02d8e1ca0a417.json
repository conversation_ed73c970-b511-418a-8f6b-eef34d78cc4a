{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          console.log('Checking authentication with token:', token.substring(0, 20) + '...');\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          console.log('Auth check successful:', response.data.data.user);\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          console.log('Clearing invalid token and user data');\n          logout();\n        }\n      } else {\n        console.log('No token found, user not authenticated');\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (email, password) => {\n    try {\n      console.log('Attempting login with:', {\n        email,\n        password\n      });\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n      console.log('Login response:', response.data);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      console.log('Login successful, user set:', user);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('Login error:', error);\n      console.error('Error response:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return {\n        success: true,\n        user: response.data.data.user\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Profile update failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Password change failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user && !!token\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "console", "log", "substring", "response", "get", "data", "error", "logout", "login", "email", "password", "post", "setItem", "success", "_error$response", "_error$response2", "_error$response2$data", "message", "register", "userData", "_error$response3", "_error$response3$data", "removeItem", "updateProfile", "profileData", "put", "_error$response4", "_error$response4$data", "changePassword", "currentPassword", "newPassword", "_error$response5", "_error$response5$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          console.log('Checking authentication with token:', token.substring(0, 20) + '...');\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          console.log('Auth check successful:', response.data.data.user);\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          console.log('Clearing invalid token and user data');\n          logout();\n        }\n      } else {\n        console.log('No token found, user not authenticated');\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (email, password) => {\n    try {\n      console.log('Attempting login with:', { email, password });\n\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n\n      console.log('Login response:', response.data);\n\n      const { user, token } = response.data.data;\n\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n\n      console.log('Login successful, user set:', user);\n\n      return { success: true, user };\n    } catch (error) {\n      console.error('Login error:', error);\n      console.error('Error response:', error.response?.data);\n      const message = error.response?.data?.message || 'Login failed';\n      return { success: false, message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      \n      const { user, token } = response.data.data;\n      \n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      \n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      return { success: false, message };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return { success: true, user: response.data.data.user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      return { success: false, message };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      return { success: false, message };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user && !!token\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACFQ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAET,KAAK,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAClF,MAAMC,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,mCAAmC,CAAC;UACrEJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;UAC9DC,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;QAClC,CAAC,CAAC,OAAOkB,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnDM,MAAM,CAAC,CAAC;QACV;MACF,CAAC,MAAM;QACLP,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD;MACAV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EAEX,MAAMgB,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFV,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QAAEQ,KAAK;QAAEC;MAAS,CAAC,CAAC;MAE1D,MAAMP,QAAQ,GAAG,MAAM1B,KAAK,CAACkC,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,CAAC;MAEFV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,QAAQ,CAACE,IAAI,CAAC;MAE7C,MAAM;QAAEjB,IAAI;QAAEI;MAAM,CAAC,GAAGW,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1ChB,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAEpB,KAAK,CAAC;MAEpCQ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEb,IAAI,CAAC;MAEhD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAEzB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAQ,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhB,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCN,OAAO,CAACM,KAAK,CAAC,iBAAiB,GAAAQ,eAAA,GAAER,KAAK,CAACH,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBT,IAAI,CAAC;MACtD,MAAMY,OAAO,GAAG,EAAAF,gBAAA,GAAAT,KAAK,CAACH,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBC,OAAO,KAAI,cAAc;MAC/D,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEI;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM1B,KAAK,CAACkC,IAAI,CAAC,yCAAyC,EAAEQ,QAAQ,CAAC;MAEtF,MAAM;QAAE/B,IAAI;QAAEI;MAAM,CAAC,GAAGW,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1ChB,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAEpB,KAAK,CAAC;MAEpC,OAAO;QAAEqB,OAAO,EAAE,IAAI;QAAEzB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,OAAO,GAAG,EAAAG,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MACtE,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEI;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMV,MAAM,GAAGA,CAAA,KAAM;IACnBlB,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAAC4B,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO7C,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAMyB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAM1B,KAAK,CAACgD,GAAG,CAAC,wCAAwC,EAAED,WAAW,CAAC;MACvFnC,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,IAAI,CAAC;MAChC,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAEe,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB;MAAK,CAAC;IACzD,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACd,MAAMV,OAAO,GAAG,EAAAS,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,uBAAuB;MACxE,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEI;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMW,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMrD,KAAK,CAACgD,GAAG,CAAC,gDAAgD,EAAE;QAChEI,eAAe;QACfC;MACF,CAAC,CAAC;MACF,OAAO;QAAEjB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;MAAA,IAAAyB,gBAAA,EAAAC,qBAAA;MACd,MAAMf,OAAO,GAAG,EAAAc,gBAAA,GAAAzB,KAAK,CAACH,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,wBAAwB;MACzE,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEI;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMgB,KAAK,GAAG;IACZ7C,IAAI;IACJI,KAAK;IACLF,OAAO;IACPkB,KAAK;IACLU,QAAQ;IACRX,MAAM;IACNgB,aAAa;IACbK,cAAc;IACdM,eAAe,EAAE,CAAC,CAAC9C,IAAI,IAAI,CAAC,CAACI;EAC/B,CAAC;EAED,oBACEb,OAAA,CAACC,WAAW,CAACuD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA/C,QAAA,EAChCA;EAAQ;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpD,GAAA,CAlIWF,YAAY;AAAAuD,EAAA,GAAZvD,YAAY;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}