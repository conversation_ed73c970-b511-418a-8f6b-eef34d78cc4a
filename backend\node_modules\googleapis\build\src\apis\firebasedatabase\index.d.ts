/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebasedatabase_v1beta } from './v1beta';
export declare const VERSIONS: {
    v1beta: typeof firebasedatabase_v1beta.Firebasedatabase;
};
export declare function firebasedatabase(version: 'v1beta'): firebasedatabase_v1beta.Firebasedatabase;
export declare function firebasedatabase(options: firebasedatabase_v1beta.Options): firebasedatabase_v1beta.Firebasedatabase;
declare const auth: AuthPlus;
export { auth };
export { firebasedatabase_v1beta };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
