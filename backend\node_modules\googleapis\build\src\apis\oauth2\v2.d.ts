import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace oauth2_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * Data format for the response.
         */
        alt?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * An opaque string that represents a user for quota purposes. Must not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Deprecated. Please use quotaUser instead.
         */
        userIp?: string;
    }
    /**
     * Google OAuth2 API
     *
     * Obtains end-user authorization grants for use with other Google APIs.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const oauth2 = google.oauth2('v2');
     * ```
     */
    export class Oauth2 {
        context: APIRequestContext;
        userinfo: Resource$Userinfo;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
        /**
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        tokeninfo(params: Params$$Tokeninfo, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        tokeninfo(params?: Params$$Tokeninfo, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Tokeninfo>>;
        tokeninfo(params: Params$$Tokeninfo, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        tokeninfo(params: Params$$Tokeninfo, options: MethodOptions | BodyResponseCallback<Schema$Tokeninfo>, callback: BodyResponseCallback<Schema$Tokeninfo>): void;
        tokeninfo(params: Params$$Tokeninfo, callback: BodyResponseCallback<Schema$Tokeninfo>): void;
        tokeninfo(callback: BodyResponseCallback<Schema$Tokeninfo>): void;
    }
    export interface Schema$Tokeninfo {
        /**
         * Who is the intended audience for this token. In general the same as issued_to.
         */
        audience?: string | null;
        /**
         * The email address of the user. Present only if the email scope is present in the request.
         */
        email?: string | null;
        /**
         * The expiry time of the token, as number of seconds left until expiry.
         */
        expires_in?: number | null;
        /**
         * To whom was the token issued to. In general the same as audience.
         */
        issued_to?: string | null;
        /**
         * The space separated list of scopes granted to this token.
         */
        scope?: string | null;
        /**
         * The obfuscated user id.
         */
        user_id?: string | null;
        /**
         * Boolean flag which is true if the email address is verified. Present only if the email scope is present in the request.
         */
        verified_email?: boolean | null;
    }
    export interface Schema$Userinfo {
        /**
         * The user's email address.
         */
        email?: string | null;
        /**
         * The user's last name.
         */
        family_name?: string | null;
        /**
         * The user's gender.
         */
        gender?: string | null;
        /**
         * The user's first name.
         */
        given_name?: string | null;
        /**
         * The hosted domain e.g. example.com if the user is Google apps user.
         */
        hd?: string | null;
        /**
         * The obfuscated ID of the user.
         */
        id?: string | null;
        /**
         * URL of the profile page.
         */
        link?: string | null;
        /**
         * The user's preferred locale.
         */
        locale?: string | null;
        /**
         * The user's full name.
         */
        name?: string | null;
        /**
         * URL of the user's picture image.
         */
        picture?: string | null;
        /**
         * Boolean flag which is true if the email address is verified. Always verified because we only return the user's primary email address.
         */
        verified_email?: boolean | null;
    }
    export interface Params$$Tokeninfo extends StandardParameters {
        /**
         *
         */
        access_token?: string;
        /**
         *
         */
        id_token?: string;
    }
    export class Resource$Userinfo {
        context: APIRequestContext;
        v2: Resource$Userinfo$V2;
        constructor(context: APIRequestContext);
        /**
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Userinfo$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Userinfo$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Userinfo>>;
        get(params: Params$Resource$Userinfo$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Userinfo$Get, options: MethodOptions | BodyResponseCallback<Schema$Userinfo>, callback: BodyResponseCallback<Schema$Userinfo>): void;
        get(params: Params$Resource$Userinfo$Get, callback: BodyResponseCallback<Schema$Userinfo>): void;
        get(callback: BodyResponseCallback<Schema$Userinfo>): void;
    }
    export interface Params$Resource$Userinfo$Get extends StandardParameters {
    }
    export class Resource$Userinfo$V2 {
        context: APIRequestContext;
        me: Resource$Userinfo$V2$Me;
        constructor(context: APIRequestContext);
    }
    export class Resource$Userinfo$V2$Me {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Userinfo$V2$Me$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Userinfo$V2$Me$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Userinfo>>;
        get(params: Params$Resource$Userinfo$V2$Me$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Userinfo$V2$Me$Get, options: MethodOptions | BodyResponseCallback<Schema$Userinfo>, callback: BodyResponseCallback<Schema$Userinfo>): void;
        get(params: Params$Resource$Userinfo$V2$Me$Get, callback: BodyResponseCallback<Schema$Userinfo>): void;
        get(callback: BodyResponseCallback<Schema$Userinfo>): void;
    }
    export interface Params$Resource$Userinfo$V2$Me$Get extends StandardParameters {
    }
    export {};
}
