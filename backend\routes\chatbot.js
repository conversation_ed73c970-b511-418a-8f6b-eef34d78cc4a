const express = require('express');
const { protect } = require('../middleware/auth');
const router = express.Router();

// Simple chatbot responses for health-related queries
const healthResponses = {
  // Greetings
  'hello': 'Hello! I\'m your health assistant. How can I help you today?',
  'hi': 'Hi there! I\'m here to help with your health questions.',
  'hey': 'Hey! What health topic would you like to discuss?',
  
  // General health
  'health': 'I can help you with fitness tracking, sleep analysis, nutrition tips, and understanding your health metrics. What specific area interests you?',
  'fitness': 'Great choice! Regular exercise is key to good health. I can help you set goals, track progress, and suggest activities based on your fitness level.',
  'exercise': 'Exercise is fantastic for your health! Aim for at least 150 minutes of moderate activity per week. What type of exercise do you enjoy?',
  
  // Heart rate
  'heart rate': 'Your heart rate is an important indicator of cardiovascular health. Resting heart rate typically ranges from 60-100 bpm for adults. Regular exercise can help lower your resting heart rate.',
  'pulse': 'Your pulse reflects your heart rate. A normal resting pulse for adults is 60-100 beats per minute. Athletes often have lower resting heart rates.',
  
  // Sleep
  'sleep': 'Quality sleep is crucial for health! Adults need 7-9 hours per night. Good sleep hygiene includes consistent bedtime, cool dark room, and avoiding screens before bed.',
  'insomnia': 'Sleep troubles can affect your health. Try maintaining a regular sleep schedule, creating a relaxing bedtime routine, and avoiding caffeine late in the day. If problems persist, consult a healthcare provider.',
  
  // Steps and activity
  'steps': 'Great question! The general recommendation is 10,000 steps per day, but even 7,000-8,000 steps can provide significant health benefits. Every step counts!',
  'walking': 'Walking is excellent exercise! It\'s low-impact, accessible, and great for cardiovascular health. Try to incorporate more walking into your daily routine.',
  
  // Nutrition
  'nutrition': 'Good nutrition fuels your body! Focus on whole foods, plenty of vegetables, lean proteins, and stay hydrated. I can help you understand how nutrition affects your health metrics.',
  'water': 'Staying hydrated is essential! Aim for about 8 glasses of water daily, more if you\'re active. Proper hydration supports all body functions.',
  
  // Stress
  'stress': 'Managing stress is important for overall health. Try deep breathing, meditation, regular exercise, and adequate sleep. Chronic stress can impact your heart rate and sleep quality.',
  'anxiety': 'Anxiety can affect your physical health too. Regular exercise, good sleep, and relaxation techniques can help. If anxiety persists, consider speaking with a healthcare professional.',
  
  // Weight
  'weight': 'Healthy weight management involves balanced nutrition and regular activity. Focus on sustainable habits rather than quick fixes. Your BMI and activity levels are good indicators to track.',
  'bmi': 'BMI is one measure of health, but it doesn\'t tell the whole story. It\'s calculated from height and weight. A healthy BMI is typically 18.5-24.9, but muscle mass and other factors matter too.',
  
  // Goals
  'goals': 'Setting health goals is great! Make them SMART: Specific, Measurable, Achievable, Relevant, and Time-bound. I can help you track progress toward your fitness goals.',
  'motivation': 'Staying motivated can be challenging! Try setting small, achievable goals, tracking your progress, celebrating successes, and finding activities you enjoy.',
  
  // Default responses
  'help': 'I can help you with questions about fitness, sleep, heart rate, nutrition, stress management, and understanding your health data. What would you like to know?',
  'thanks': 'You\'re welcome! I\'m here whenever you need health guidance.',
  'thank you': 'Happy to help! Feel free to ask me anything about your health and fitness.',
};

// Function to find the best response
function findBestResponse(message) {
  const lowerMessage = message.toLowerCase();
  
  // Check for exact matches first
  for (const [key, response] of Object.entries(healthResponses)) {
    if (lowerMessage.includes(key)) {
      return response;
    }
  }
  
  // Check for partial matches or related terms
  if (lowerMessage.includes('tired') || lowerMessage.includes('fatigue')) {
    return 'Feeling tired can be related to sleep quality, activity level, or stress. Make sure you\'re getting 7-9 hours of quality sleep and staying active. If fatigue persists, consider consulting a healthcare provider.';
  }
  
  if (lowerMessage.includes('pain') || lowerMessage.includes('hurt')) {
    return 'I understand you\'re experiencing discomfort. While I can provide general wellness tips, for any pain or medical concerns, it\'s best to consult with a healthcare professional who can properly assess your situation.';
  }
  
  if (lowerMessage.includes('diet') || lowerMessage.includes('food')) {
    return 'A balanced diet is key to good health! Focus on whole foods, plenty of vegetables, lean proteins, and whole grains. Stay hydrated and limit processed foods. Your nutrition directly impacts your energy and health metrics.';
  }
  
  if (lowerMessage.includes('calories') || lowerMessage.includes('burn')) {
    return 'Calorie burn depends on your activity level, body weight, and metabolism. Regular exercise and daily activities all contribute. Focus on being consistently active rather than just calorie counting!';
  }
  
  // Default response
  return 'That\'s an interesting question! While I focus on general health and fitness guidance, for specific medical concerns, I\'d recommend consulting with a healthcare professional. Is there a particular aspect of your health data or fitness routine you\'d like to discuss?';
}

// @desc    Chat with health bot
// @route   POST /api/chatbot/chat
// @access  Private
router.post('/chat', protect, async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a message'
      });
    }
    
    // Get bot response
    const botResponse = findBestResponse(message.trim());
    
    // In a real implementation, you might want to:
    // 1. Store chat history in database
    // 2. Use user's health data to personalize responses
    // 3. Integrate with more sophisticated NLP/AI services
    
    const response = {
      userMessage: message,
      botResponse: botResponse,
      timestamp: new Date(),
      suggestions: getSuggestions(message)
    };
    
    res.json({
      success: true,
      data: response
    });
    
  } catch (error) {
    console.error('Chatbot error:', error);
    res.status(500).json({
      success: false,
      message: 'Sorry, I\'m having trouble right now. Please try again.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get chat suggestions
// @route   GET /api/chatbot/suggestions
// @access  Private
router.get('/suggestions', protect, async (req, res) => {
  try {
    const suggestions = [
      'How can I improve my sleep quality?',
      'What\'s a healthy heart rate?',
      'How many steps should I take daily?',
      'Tips for staying motivated to exercise',
      'How does stress affect my health?',
      'What should I know about my BMI?',
      'How can I track my fitness goals?',
      'Nutrition tips for better health'
    ];
    
    res.json({
      success: true,
      data: { suggestions }
    });
    
  } catch (error) {
    console.error('Get suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Helper function to get contextual suggestions
function getSuggestions(message) {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('sleep')) {
    return [
      'How to create a bedtime routine?',
      'What affects sleep quality?',
      'Tips for better sleep hygiene'
    ];
  }
  
  if (lowerMessage.includes('exercise') || lowerMessage.includes('fitness')) {
    return [
      'How to set fitness goals?',
      'Best exercises for beginners',
      'How to stay motivated to exercise'
    ];
  }
  
  if (lowerMessage.includes('heart') || lowerMessage.includes('pulse')) {
    return [
      'What\'s a normal heart rate?',
      'How to improve cardiovascular health?',
      'Understanding heart rate zones'
    ];
  }
  
  if (lowerMessage.includes('weight') || lowerMessage.includes('bmi')) {
    return [
      'Healthy weight management tips',
      'Understanding BMI',
      'How to maintain a healthy weight'
    ];
  }
  
  // Default suggestions
  return [
    'Tell me about my health metrics',
    'How can I improve my fitness?',
    'What are healthy lifestyle habits?'
  ];
}

module.exports = router;
