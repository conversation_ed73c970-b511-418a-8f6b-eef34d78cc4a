const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Sample data for HealthConnect application
const sampleData = {
  // Sample Users
  users: [
    {
      name: 'Demo User',
      email: '<EMAIL>',
      password: 'demo123', // Will be hashed
      dateOfBirth: new Date('1990-05-15'),
      gender: 'male',
      height: 175,
      weight: 70,
      activityLevel: 'moderately_active',
      medicalConditions: [],
      medications: [],
      emergencyContact: {
        name: '<PERSON>',
        phone: '+**********',
        relationship: 'spouse'
      },
      preferences: {
        units: 'metric',
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        privacy: {
          shareData: false,
          publicProfile: false
        }
      },
      deviceInfo: {
        deviceType: 'fitbit',
        deviceModel: 'Fitbit Charge 5',
        lastSync: new Date()
      }
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'password123',
      dateOfBirth: new Date('1985-08-22'),
      gender: 'female',
      height: 165,
      weight: 60,
      activityLevel: 'very_active',
      medicalConditions: [],
      medications: [],
      preferences: {
        units: 'metric',
        notifications: {
          email: true,
          push: true,
          sms: true
        }
      },
      deviceInfo: {
        deviceType: 'apple_watch',
        deviceModel: 'Apple Watch Series 8',
        lastSync: new Date()
      }
    }
  ],

  // Sample Health Data (last 30 days for demo user)
  generateHealthData: (userId) => {
    const healthData = [];
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Generate realistic fitness data with some variation
      const baseSteps = 8000 + Math.random() * 4000; // 8000-12000 steps
      const steps = Math.floor(baseSteps + (Math.sin(i * 0.5) * 2000)); // Weekly pattern
      
      const data = {
        userId: userId,
        date: date,
        steps: Math.max(3000, steps),
        distance: (steps * 0.0008), // Rough conversion: steps to km
        caloriesBurned: Math.floor(1800 + (steps * 0.04) + Math.random() * 400),
        activeMinutes: Math.floor(30 + Math.random() * 60),
        
        heartRate: {
          resting: Math.floor(60 + Math.random() * 20),
          average: Math.floor(80 + Math.random() * 30),
          maximum: Math.floor(150 + Math.random() * 40),
          zones: {
            fatBurn: Math.floor(Math.random() * 30),
            cardio: Math.floor(Math.random() * 20),
            peak: Math.floor(Math.random() * 10)
          }
        },
        
        sleep: {
          totalSleep: Math.floor(360 + Math.random() * 180), // 6-9 hours in minutes
          deepSleep: Math.floor(60 + Math.random() * 60),
          lightSleep: Math.floor(200 + Math.random() * 100),
          remSleep: Math.floor(60 + Math.random() * 40),
          awakeTime: Math.floor(Math.random() * 30),
          sleepEfficiency: Math.floor(75 + Math.random() * 20),
          bedTime: new Date(date.getTime() - (10 + Math.random() * 4) * 60 * 60 * 1000),
          wakeTime: new Date(date.getTime() - (2 + Math.random() * 2) * 60 * 60 * 1000)
        },
        
        exercises: Math.random() > 0.7 ? [{
          type: ['running', 'walking', 'cycling', 'swimming'][Math.floor(Math.random() * 4)],
          duration: Math.floor(20 + Math.random() * 40),
          caloriesBurned: Math.floor(200 + Math.random() * 300),
          averageHeartRate: Math.floor(120 + Math.random() * 40),
          maxHeartRate: Math.floor(160 + Math.random() * 30),
          startTime: new Date(date.getTime() - Math.random() * 12 * 60 * 60 * 1000),
          endTime: new Date(date.getTime() - Math.random() * 8 * 60 * 60 * 1000)
        }] : [],
        
        vitals: {
          bloodPressure: {
            systolic: Math.floor(110 + Math.random() * 30),
            diastolic: Math.floor(70 + Math.random() * 20)
          },
          bloodOxygen: Math.floor(95 + Math.random() * 5),
          bodyTemperature: 36.5 + Math.random() * 1,
          weight: 70 + (Math.random() - 0.5) * 2 // Small daily variations
        },
        
        stress: {
          level: Math.floor(1 + Math.random() * 8),
          duration: Math.floor(Math.random() * 120),
          triggers: Math.random() > 0.8 ? ['work', 'traffic', 'personal'] : []
        },
        
        waterIntake: 1.5 + Math.random() * 2, // 1.5-3.5 liters
        mood: Math.floor(5 + Math.random() * 4), // 5-8 scale
        energyLevel: Math.floor(5 + Math.random() * 4),
        
        dataSource: 'fitbit',
        dataQuality: {
          completeness: Math.floor(85 + Math.random() * 15),
          accuracy: ['high', 'medium'][Math.floor(Math.random() * 2)]
        },
        
        syncedAt: new Date(),
        lastModified: new Date()
      };
      
      healthData.push(data);
    }
    
    return healthData;
  },

  // Sample Goals
  generateGoals: (userId) => [
    {
      userId: userId,
      title: 'Walk 10,000 steps daily',
      description: 'Maintain an active lifestyle by walking at least 10,000 steps every day',
      category: 'steps',
      type: 'daily',
      target: {
        value: 10000,
        unit: 'steps'
      },
      current: {
        value: 8500,
        lastUpdated: new Date()
      },
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
      status: 'active',
      priority: 'high',
      reminders: {
        enabled: true,
        frequency: 'daily',
        time: '18:00'
      },
      milestones: [
        { percentage: 25, achieved: true, achievedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), reward: '25% Complete!' },
        { percentage: 50, achieved: true, achievedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), reward: 'Halfway There!' },
        { percentage: 75, achieved: true, achievedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), reward: '75% Complete!' },
        { percentage: 100, achieved: false, reward: 'Goal Achieved!' }
      ],
      achievements: [
        { date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), value: 8500, note: 'Great progress!' }
      ]
    },
    {
      userId: userId,
      title: 'Sleep 8 hours nightly',
      description: 'Improve sleep quality by getting 8 hours of sleep each night',
      category: 'sleep',
      type: 'daily',
      target: {
        value: 480,
        unit: 'minutes'
      },
      current: {
        value: 420,
        lastUpdated: new Date()
      },
      startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000),
      status: 'active',
      priority: 'medium',
      reminders: {
        enabled: true,
        frequency: 'daily',
        time: '22:00'
      }
    },
    {
      userId: userId,
      title: 'Exercise 5 times per week',
      description: 'Maintain fitness by exercising at least 5 times per week',
      category: 'exercise',
      type: 'weekly',
      target: {
        value: 5,
        unit: 'sessions'
      },
      current: {
        value: 3,
        lastUpdated: new Date()
      },
      startDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000),
      status: 'active',
      priority: 'high'
    }
  ],

  // Sample Predictions
  generatePredictions: (userId) => [
    {
      userId: userId,
      predictionType: 'comprehensive',
      inputData: {
        heartRate: { resting: 68, average: 85, variability: 12 },
        activity: { steps: 8500, activeMinutes: 45, caloriesBurned: 2200 },
        sleep: { duration: 420, efficiency: 85, deepSleepPercentage: 20 },
        vitals: { bloodPressure: { systolic: 120, diastolic: 80 }, bloodOxygen: 98, weight: 70, bmi: 22.9 },
        lifestyle: { stressLevel: 4, mood: 7, energyLevel: 7, waterIntake: 2.5 },
        demographics: { age: 33, gender: 'male', activityLevel: 'moderately_active' },
        timeframe: { startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), endDate: new Date(), dataPoints: 7 }
      },
      predictions: [
        {
          condition: 'Cardiovascular Disease',
          riskLevel: 'low',
          confidence: 78,
          probability: 0.15,
          description: 'Based on your current health metrics, your cardiovascular risk is low.',
          recommendations: [
            'Continue regular physical activity',
            'Maintain healthy diet',
            'Monitor blood pressure regularly'
          ],
          urgency: 'low'
        },
        {
          condition: 'Type 2 Diabetes',
          riskLevel: 'very_low',
          confidence: 82,
          probability: 0.08,
          description: 'Your diabetes risk assessment indicates very low risk.',
          recommendations: [
            'Maintain current activity level',
            'Continue balanced nutrition',
            'Regular health checkups'
          ],
          urgency: 'low'
        }
      ],
      recommendations: [
        {
          category: 'exercise',
          priority: 'medium',
          title: 'Increase Daily Activity',
          description: 'Consider adding 1000 more steps to reach the recommended 10,000 daily steps',
          actionItems: ['Take stairs instead of elevators', 'Walk during lunch breaks', 'Park further away'],
          expectedBenefit: 'Improved cardiovascular health and weight management',
          timeframe: '2-4 weeks'
        },
        {
          category: 'sleep',
          priority: 'medium',
          title: 'Optimize Sleep Duration',
          description: 'Aim for 7-9 hours of sleep nightly for optimal recovery',
          actionItems: ['Set consistent bedtime', 'Create relaxing bedtime routine', 'Limit screen time before bed'],
          expectedBenefit: 'Better energy, mood, and immune function',
          timeframe: '1-2 weeks'
        }
      ],
      modelInfo: {
        modelName: 'HealthConnect Predictor',
        modelVersion: '1.0.0',
        algorithm: 'Rule-based with statistical analysis',
        trainingDate: new Date(),
        accuracy: 78,
        features: ['age', 'gender', 'bmi', 'resting_heart_rate', 'avg_heart_rate', 'steps', 'active_minutes', 'sleep_duration']
      },
      status: 'completed',
      processingTime: 1250,
      isArchived: false
    }
  ]
};

module.exports = sampleData;
