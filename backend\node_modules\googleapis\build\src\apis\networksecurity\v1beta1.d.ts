import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace networksecurity_v1beta1 {
    export interface Options extends GlobalOptions {
        version: 'v1beta1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Network Security API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const networksecurity = google.networksecurity('v1beta1');
     * ```
     */
    export class Networksecurity {
        context: APIRequestContext;
        organizations: Resource$Organizations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Request used by the AddAddressGroupItems method.
     */
    export interface Schema$AddAddressGroupItemsRequest {
        /**
         * Required. List of items to add.
         */
        items?: string[] | null;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string | null;
    }
    /**
     * AddressGroup is a resource that specifies how a collection of IP/DNS used in Firewall Policy.
     */
    export interface Schema$AddressGroup {
        /**
         * Required. Capacity of the Address Group
         */
        capacity?: number | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Optional. List of items.
         */
        items?: string[] | null;
        /**
         * Optional. Set of label tags associated with the AddressGroup resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Name of the AddressGroup resource. It matches pattern `projects/x/locations/{location\}/addressGroups/`.
         */
        name?: string | null;
        /**
         * Optional. List of supported purposes of the Address Group.
         */
        purpose?: string[] | null;
        /**
         * Output only. Server-defined fully-qualified URL for this resource.
         */
        selfLink?: string | null;
        /**
         * Required. The type of the Address Group. Possible values are "IPv4" or "IPV6".
         */
        type?: string | null;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Defines what action to take for antivirus threats per protocol.
     */
    export interface Schema$AntivirusOverride {
        /**
         * Required. Threat action override. For some threat types, only a subset of actions applies.
         */
        action?: string | null;
        /**
         * Required. Protocol to match.
         */
        protocol?: string | null;
    }
    /**
     * AuthorizationPolicy is a resource that specifies how a server should authorize incoming connections. This resource in itself does not change the configuration unless it's attached to a target https proxy or endpoint config selector resource.
     */
    export interface Schema$AuthorizationPolicy {
        /**
         * Required. The action to take when a rule match is found. Possible values are "ALLOW" or "DENY".
         */
        action?: string | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Optional. Set of label tags associated with the AuthorizationPolicy resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Name of the AuthorizationPolicy resource. It matches pattern `projects/{project\}/locations/{location\}/authorizationPolicies/`.
         */
        name?: string | null;
        /**
         * Optional. List of rules to match. Note that at least one of the rules must match in order for the action specified in the 'action' field to be taken. A rule is a match if there is a matching source and destination. If left blank, the action specified in the `action` field will be applied on every request.
         */
        rules?: Schema$Rule[];
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * `AuthzPolicy` is a resource that allows to forward traffic to a callout backend designed to scan the traffic for security purposes.
     */
    export interface Schema$AuthzPolicy {
        /**
         * Required. Can be one of `ALLOW`, `DENY`, `CUSTOM`. When the action is `CUSTOM`, `customProvider` must be specified. When the action is `ALLOW`, only requests matching the policy will be allowed. When the action is `DENY`, only requests matching the policy will be denied. When a request arrives, the policies are evaluated in the following order: 1. If there is a `CUSTOM` policy that matches the request, the `CUSTOM` policy is evaluated using the custom authorization providers and the request is denied if the provider rejects the request. 2. If there are any `DENY` policies that match the request, the request is denied. 3. If there are no `ALLOW` policies for the resource or if any of the `ALLOW` policies match the request, the request is allowed. 4. Else the request is denied by default if none of the configured AuthzPolicies with `ALLOW` action match the request.
         */
        action?: string | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Required if the action is `CUSTOM`. Allows delegating authorization decisions to Cloud IAP or to Service Extensions. One of `cloudIap` or `authzExtension` must be specified.
         */
        customProvider?: Schema$AuthzPolicyCustomProvider;
        /**
         * Optional. A human-readable description of the resource.
         */
        description?: string | null;
        /**
         * Optional. A list of authorization HTTP rules to match against the incoming request. A policy match occurs when at least one HTTP rule matches the request or when no HTTP rules are specified in the policy. At least one HTTP Rule is required for Allow or Deny Action. Limited to 5 rules.
         */
        httpRules?: Schema$AuthzPolicyAuthzRule[];
        /**
         * Optional. Set of labels associated with the `AuthzPolicy` resource. The format must comply with [the following requirements](/compute/docs/labeling-resources#requirements).
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Identifier. Name of the `AuthzPolicy` resource in the following format: `projects/{project\}/locations/{location\}/authzPolicies/{authz_policy\}`.
         */
        name?: string | null;
        /**
         * Required. Specifies the set of resources to which this policy should be applied to.
         */
        target?: Schema$AuthzPolicyTarget;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Conditions to match against the incoming request.
     */
    export interface Schema$AuthzPolicyAuthzRule {
        /**
         * Optional. Describes properties of a source of a request.
         */
        from?: Schema$AuthzPolicyAuthzRuleFrom;
        /**
         * Optional. Describes properties of a target of a request.
         */
        to?: Schema$AuthzPolicyAuthzRuleTo;
        /**
         * Optional. CEL expression that describes the conditions to be satisfied for the action. The result of the CEL expression is ANDed with the from and to. Refer to the CEL language reference for a list of available attributes.
         */
        when?: string | null;
    }
    /**
     * Describes properties of one or more sources of a request.
     */
    export interface Schema$AuthzPolicyAuthzRuleFrom {
        /**
         * Optional. Describes the negated properties of request sources. Matches requests from sources that do not match the criteria specified in this field. At least one of sources or notSources must be specified.
         */
        notSources?: Schema$AuthzPolicyAuthzRuleFromRequestSource[];
        /**
         * Optional. Describes the properties of a request's sources. At least one of sources or notSources must be specified. Limited to 1 source. A match occurs when ANY source (in sources or notSources) matches the request. Within a single source, the match follows AND semantics across fields and OR semantics within a single field, i.e. a match occurs when ANY principal matches AND ANY ipBlocks match.
         */
        sources?: Schema$AuthzPolicyAuthzRuleFromRequestSource[];
    }
    /**
     * Describes the properties of a single source.
     */
    export interface Schema$AuthzPolicyAuthzRuleFromRequestSource {
        /**
         * Optional. A list of identities derived from the client's certificate. This field is under development and we don't recommend using it at this time. Limited to 5 principals.
         */
        principals?: Schema$AuthzPolicyAuthzRuleStringMatch[];
        /**
         * Optional. A list of resources to match against the resource of the source VM of a request. Limited to 5 resources.
         */
        resources?: Schema$AuthzPolicyAuthzRuleRequestResource[];
    }
    /**
     * Determines how a HTTP header should be matched.
     */
    export interface Schema$AuthzPolicyAuthzRuleHeaderMatch {
        /**
         * Optional. Specifies the name of the header in the request.
         */
        name?: string | null;
        /**
         * Optional. Specifies how the header match will be performed.
         */
        value?: Schema$AuthzPolicyAuthzRuleStringMatch;
    }
    /**
     * Describes the properties of a client VM resource accessing the internal application load balancers.
     */
    export interface Schema$AuthzPolicyAuthzRuleRequestResource {
        /**
         * Optional. An IAM service account to match against the source service account of the VM sending the request.
         */
        iamServiceAccount?: Schema$AuthzPolicyAuthzRuleStringMatch;
        /**
         * Optional. A list of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request.
         */
        tagValueIdSet?: Schema$AuthzPolicyAuthzRuleRequestResourceTagValueIdSet;
    }
    /**
     * Describes a set of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request.
     */
    export interface Schema$AuthzPolicyAuthzRuleRequestResourceTagValueIdSet {
        /**
         * Required. A list of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request. The match follows AND semantics which means all the ids must match. Limited to 5 matches.
         */
        ids?: string[] | null;
    }
    /**
     * Determines how a string value should be matched.
     */
    export interface Schema$AuthzPolicyAuthzRuleStringMatch {
        /**
         * The input string must have the substring specified here. Note: empty contains match is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``xyz.abc.def``
         */
        contains?: string | null;
        /**
         * The input string must match exactly the string specified here. Examples: * ``abc`` only matches the value ``abc``.
         */
        exact?: string | null;
        /**
         * If true, indicates the exact/prefix/suffix/contains matching should be case insensitive. For example, the matcher ``data`` will match both input string ``Data`` and ``data`` if set to true.
         */
        ignoreCase?: boolean | null;
        /**
         * The input string must have the prefix specified here. Note: empty prefix is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``abc.xyz``
         */
        prefix?: string | null;
        /**
         * The input string must have the suffix specified here. Note: empty prefix is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``xyz.abc``
         */
        suffix?: string | null;
    }
    /**
     * Describes properties of one or more targets of a request.
     */
    export interface Schema$AuthzPolicyAuthzRuleTo {
        /**
         * Optional. Describes the negated properties of the targets of a request. Matches requests for operations that do not match the criteria specified in this field. At least one of operations or notOperations must be specified.
         */
        notOperations?: Schema$AuthzPolicyAuthzRuleToRequestOperation[];
        /**
         * Optional. Describes properties of one or more targets of a request. At least one of operations or notOperations must be specified. Limited to 1 operation. A match occurs when ANY operation (in operations or notOperations) matches. Within an operation, the match follows AND semantics across fields and OR semantics within a field, i.e. a match occurs when ANY path matches AND ANY header matches and ANY method matches.
         */
        operations?: Schema$AuthzPolicyAuthzRuleToRequestOperation[];
    }
    /**
     * Describes properties of one or more targets of a request.
     */
    export interface Schema$AuthzPolicyAuthzRuleToRequestOperation {
        /**
         * Optional. A list of headers to match against in http header.
         */
        headerSet?: Schema$AuthzPolicyAuthzRuleToRequestOperationHeaderSet;
        /**
         * Optional. A list of HTTP Hosts to match against. The match can be one of exact, prefix, suffix, or contains (substring match). Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches.
         */
        hosts?: Schema$AuthzPolicyAuthzRuleStringMatch[];
        /**
         * Optional. A list of HTTP methods to match against. Each entry must be a valid HTTP method name (GET, PUT, POST, HEAD, PATCH, DELETE, OPTIONS). It only allows exact match and is always case sensitive.
         */
        methods?: string[] | null;
        /**
         * Optional. A list of paths to match against. The match can be one of exact, prefix, suffix, or contains (substring match). Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches. Note that this path match includes the query parameters. For gRPC services, this should be a fully-qualified name of the form /package.service/method.
         */
        paths?: Schema$AuthzPolicyAuthzRuleStringMatch[];
    }
    /**
     * Describes a set of HTTP headers to match against.
     */
    export interface Schema$AuthzPolicyAuthzRuleToRequestOperationHeaderSet {
        /**
         * Required. A list of headers to match against in http header. The match can be one of exact, prefix, suffix, or contains (substring match). The match follows AND semantics which means all the headers must match. Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches.
         */
        headers?: Schema$AuthzPolicyAuthzRuleHeaderMatch[];
    }
    /**
     * Allows delegating authorization decisions to Cloud IAP or to Service Extensions.
     */
    export interface Schema$AuthzPolicyCustomProvider {
        /**
         * Optional. Delegate authorization decision to user authored Service Extension. Only one of cloudIap or authzExtension can be specified.
         */
        authzExtension?: Schema$AuthzPolicyCustomProviderAuthzExtension;
        /**
         * Optional. Delegates authorization decisions to Cloud IAP. Applicable only for managed load balancers. Enabling Cloud IAP at the AuthzPolicy level is not compatible with Cloud IAP settings in the BackendService. Enabling IAP in both places will result in request failure. Ensure that IAP is enabled in either the AuthzPolicy or the BackendService but not in both places.
         */
        cloudIap?: Schema$AuthzPolicyCustomProviderCloudIap;
    }
    /**
     * Optional. Delegate authorization decision to user authored extension. Only one of cloudIap or authzExtension can be specified.
     */
    export interface Schema$AuthzPolicyCustomProviderAuthzExtension {
        /**
         * Required. A list of references to authorization extensions that will be invoked for requests matching this policy. Limited to 1 custom provider.
         */
        resources?: string[] | null;
    }
    /**
     * Optional. Delegates authorization decisions to Cloud IAP. Applicable only for managed load balancers. Enabling Cloud IAP at the AuthzPolicy level is not compatible with Cloud IAP settings in the BackendService. Enabling IAP in both places will result in request failure. Ensure that IAP is enabled in either the AuthzPolicy or the BackendService but not in both places.
     */
    export interface Schema$AuthzPolicyCustomProviderCloudIap {
    }
    /**
     * Specifies the set of targets to which this policy should be applied to.
     */
    export interface Schema$AuthzPolicyTarget {
        /**
         * Required. All gateways and forwarding rules referenced by this policy and extensions must share the same load balancing scheme. Supported values: `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more information, refer to [Backend services overview](https://cloud.google.com/load-balancing/docs/backend-service).
         */
        loadBalancingScheme?: string | null;
        /**
         * Required. A list of references to the Forwarding Rules on which this policy will be applied.
         */
        resources?: string[] | null;
    }
    /**
     * BackendAuthenticationConfig message groups the TrustConfig together with other settings that control how the load balancer authenticates, and expresses its identity to, the backend: * `trustConfig` is the attached TrustConfig. * `wellKnownRoots` indicates whether the load balance should trust backend server certificates that are issued by public certificate authorities, in addition to certificates trusted by the TrustConfig. * `clientCertificate` is a client certificate that the load balancer uses to express its identity to the backend, if the connection to the backend uses mTLS. You can attach the BackendAuthenticationConfig to the load balancer’s BackendService directly determining how that BackendService negotiates TLS.
     */
    export interface Schema$BackendAuthenticationConfig {
        /**
         * Optional. A reference to a certificatemanager.googleapis.com.Certificate resource. This is a relative resource path following the form "projects/{project\}/locations/{location\}/certificates/{certificate\}". Used by a BackendService to negotiate mTLS when the backend connection uses TLS and the backend requests a client certificate. Must have a CLIENT_AUTH scope.
         */
        clientCertificate?: string | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Output only. Etag of the resource.
         */
        etag?: string | null;
        /**
         * Set of label tags associated with the resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Name of the BackendAuthenticationConfig resource. It matches the pattern `projects/x/locations/{location\}/backendAuthenticationConfigs/{backend_authentication_config\}`
         */
        name?: string | null;
        /**
         * Optional. A reference to a TrustConfig resource from the certificatemanager.googleapis.com namespace. This is a relative resource path following the form "projects/{project\}/locations/{location\}/trustConfigs/{trust_config\}". A BackendService uses the chain of trust represented by this TrustConfig, if specified, to validate the server certificates presented by the backend. Required unless wellKnownRoots is set to PUBLIC_ROOTS.
         */
        trustConfig?: string | null;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
        /**
         * Well known roots to use for server certificate validation.
         */
        wellKnownRoots?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Specification of a TLS certificate provider instance. Workloads may have one or more CertificateProvider instances (plugins) and one of them is enabled and configured by specifying this message. Workloads use the values from this message to locate and load the CertificateProvider instance configuration.
     */
    export interface Schema$CertificateProviderInstance {
        /**
         * Required. Plugin instance name, used to locate and load CertificateProvider instance configuration. Set to "google_cloud_private_spiffe" to use Certificate Authority Service certificate provider instance.
         */
        pluginInstance?: string | null;
    }
    /**
     * ClientTlsPolicy is a resource that specifies how a client should authenticate connections to backends of a service. This resource itself does not affect configuration unless it is attached to a backend service resource.
     */
    export interface Schema$ClientTlsPolicy {
        /**
         * Optional. Defines a mechanism to provision client identity (public and private keys) for peer to peer authentication. The presence of this dictates mTLS.
         */
        clientCertificate?: Schema$GoogleCloudNetworksecurityV1beta1CertificateProvider;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Optional. Set of label tags associated with the resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Name of the ClientTlsPolicy resource. It matches the pattern `projects/x/locations/{location\}/clientTlsPolicies/{client_tls_policy\}`
         */
        name?: string | null;
        /**
         * Optional. Defines the mechanism to obtain the Certificate Authority certificate to validate the server certificate. If empty, client does not validate the server certificate.
         */
        serverValidationCa?: Schema$ValidationCA[];
        /**
         * Optional. Server Name Indication string to present to the server during TLS handshake. E.g: "secure.example.com".
         */
        sni?: string | null;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Request used by the CloneAddressGroupItems method.
     */
    export interface Schema$CloneAddressGroupItemsRequest {
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string | null;
        /**
         * Required. Source address group to clone items from.
         */
        sourceAddressGroup?: string | null;
    }
    /**
     * CustomInterceptProfile defines in-band integration behavior (intercept). It is used by firewall rules with an APPLY_SECURITY_PROFILE_GROUP action.
     */
    export interface Schema$CustomInterceptProfile {
        /**
         * Required. The target InterceptEndpointGroup. When a firewall rule with this security profile attached matches a packet, the packet will be intercepted to the location-local target in this group.
         */
        interceptEndpointGroup?: string | null;
    }
    /**
     * CustomMirroringProfile defines out-of-band integration behavior (mirroring). It is used by mirroring rules with a MIRROR action.
     */
    export interface Schema$CustomMirroringProfile {
        /**
         * Required. The target MirroringEndpointGroup. When a mirroring rule with this security profile attached matches a packet, a replica will be mirrored to the location-local target in this group.
         */
        mirroringEndpointGroup?: string | null;
    }
    /**
     * Specification of traffic destination attributes.
     */
    export interface Schema$Destination {
        /**
         * Required. List of host names to match. Matched against the ":authority" header in http requests. At least one host should match. Each host can be an exact match, or a prefix match (example "mydomain.*") or a suffix match (example "*.myorg.com") or a presence (any) match "*".
         */
        hosts?: string[] | null;
        /**
         * Optional. Match against key:value pair in http header. Provides a flexible match based on HTTP headers, for potentially advanced use cases. At least one header should match. Avoid using header matches to make authorization decisions unless there is a strong guarantee that requests arrive through a trusted client or proxy.
         */
        httpHeaderMatch?: Schema$HttpHeaderMatch;
        /**
         * Optional. A list of HTTP methods to match. At least one method should match. Should not be set for gRPC services.
         */
        methods?: string[] | null;
        /**
         * Required. List of destination ports to match. At least one port should match.
         */
        ports?: number[] | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Message describing Endpoint object
     */
    export interface Schema$FirewallEndpoint {
        /**
         * Output only. List of networks that are associated with this endpoint in the local zone. This is a projection of the FirewallEndpointAssociations pointing at this endpoint. A network will only appear in this list after traffic routing is fully configured. Format: projects/{project\}/global/networks/{name\}.
         */
        associatedNetworks?: string[] | null;
        /**
         * Output only. List of FirewallEndpointAssociations that are associated to this endpoint. An association will only appear in this list after traffic routing is fully configured.
         */
        associations?: Schema$FirewallEndpointAssociationReference[];
        /**
         * Required. Project to bill on endpoint uptime usage.
         */
        billingProjectId?: string | null;
        /**
         * Output only. Create time stamp
         */
        createTime?: string | null;
        /**
         * Optional. Description of the firewall endpoint. Max length 2048 characters.
         */
        description?: string | null;
        /**
         * Optional. Labels as key value pairs
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. name of resource
         */
        name?: string | null;
        /**
         * Output only. Whether reconciling is in progress, recommended per https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. [Output Only] Reserved for future use.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. [Output Only] Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. Current state of the endpoint.
         */
        state?: string | null;
        /**
         * Output only. Update time stamp
         */
        updateTime?: string | null;
    }
    /**
     * Message describing Association object
     */
    export interface Schema$FirewallEndpointAssociation {
        /**
         * Output only. Create time stamp
         */
        createTime?: string | null;
        /**
         * Optional. Whether the association is disabled. True indicates that traffic won't be intercepted
         */
        disabled?: boolean | null;
        /**
         * Required. The URL of the FirewallEndpoint that is being associated.
         */
        firewallEndpoint?: string | null;
        /**
         * Optional. Labels as key value pairs
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. name of resource
         */
        name?: string | null;
        /**
         * Required. The URL of the network that is being associated.
         */
        network?: string | null;
        /**
         * Output only. Whether reconciling is in progress, recommended per https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Current state of the association.
         */
        state?: string | null;
        /**
         * Optional. The URL of the TlsInspectionPolicy that is being associated.
         */
        tlsInspectionPolicy?: string | null;
        /**
         * Output only. Update time stamp
         */
        updateTime?: string | null;
    }
    /**
     * This is a subset of the FirewallEndpointAssociation message, containing fields to be used by the consumer.
     */
    export interface Schema$FirewallEndpointAssociationReference {
        /**
         * Output only. The resource name of the FirewallEndpointAssociation. Format: projects/{project\}/locations/{location\}/firewallEndpointAssociations/{id\}
         */
        name?: string | null;
        /**
         * Output only. The VPC network associated. Format: projects/{project\}/global/networks/{name\}.
         */
        network?: string | null;
    }
    /**
     * The GatewaySecurityPolicy resource contains a collection of GatewaySecurityPolicyRules and associated metadata.
     */
    export interface Schema$GatewaySecurityPolicy {
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Required. Name of the resource. Name is of the form projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gateway_security_policy\} gateway_security_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string | null;
        /**
         * Optional. Name of a TLS Inspection Policy resource that defines how TLS inspection will be performed for any rule(s) which enables it.
         */
        tlsInspectionPolicy?: string | null;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * The GatewaySecurityPolicyRule resource is in a nested collection within a GatewaySecurityPolicy and represents a traffic matching condition and associated action to perform.
     */
    export interface Schema$GatewaySecurityPolicyRule {
        /**
         * Optional. CEL expression for matching on L7/application level criteria.
         */
        applicationMatcher?: string | null;
        /**
         * Required. Profile which tells what the primitive action should be.
         */
        basicProfile?: string | null;
        /**
         * Output only. Time when the rule was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Required. Whether the rule is enforced.
         */
        enabled?: boolean | null;
        /**
         * Required. Immutable. Name of the resource. ame is the full resource name so projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gateway_security_policy\}/rules/{rule\} rule should match the pattern: (^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string | null;
        /**
         * Required. Priority of the rule. Lower number corresponds to higher precedence.
         */
        priority?: number | null;
        /**
         * Required. CEL expression for matching on session criteria.
         */
        sessionMatcher?: string | null;
        /**
         * Optional. Flag to enable TLS inspection of traffic matching on , can only be true if the parent GatewaySecurityPolicy references a TLSInspectionConfig.
         */
        tlsInspectionEnabled?: boolean | null;
        /**
         * Output only. Time when the rule was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Specification of certificate provider. Defines the mechanism to obtain the certificate and private key for peer to peer authentication.
     */
    export interface Schema$GoogleCloudNetworksecurityV1beta1CertificateProvider {
        /**
         * The certificate provider instance specification that will be passed to the data plane, which will be used to load necessary credential information.
         */
        certificateProviderInstance?: Schema$CertificateProviderInstance;
        /**
         * gRPC specific configuration to access the gRPC server to obtain the cert and private key.
         */
        grpcEndpoint?: Schema$GoogleCloudNetworksecurityV1beta1GrpcEndpoint;
    }
    /**
     * Specification of the GRPC Endpoint.
     */
    export interface Schema$GoogleCloudNetworksecurityV1beta1GrpcEndpoint {
        /**
         * Required. The target URI of the gRPC endpoint. Only UDS path is supported, and should start with "unix:".
         */
        targetUri?: string | null;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$GoogleIamV1AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$GoogleIamV1AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$GoogleIamV1AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$GoogleIamV1Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$GoogleIamV1Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$GoogleIamV1AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$GoogleIamV1Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$GoogleIamV1SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$GoogleIamV1Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$GoogleIamV1TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Specification of HTTP header match attributes.
     */
    export interface Schema$HttpHeaderMatch {
        /**
         * Required. The name of the HTTP header to match. For matching against the HTTP request's authority, use a headerMatch with the header name ":authority". For matching a request's method, use the headerName ":method".
         */
        headerName?: string | null;
        /**
         * Required. The value of the header must match the regular expression specified in regexMatch. For regular expression grammar, please see: en.cppreference.com/w/cpp/regex/ecmascript For matching against a port specified in the HTTP request, use a headerMatch with headerName set to Host and a regular expression that satisfies the RFC2616 Host header's port specifier.
         */
        regexMatch?: string | null;
    }
    /**
     * A deployment represents a zonal intercept backend ready to accept GENEVE-encapsulated traffic, e.g. a zonal instance group fronted by an internal passthrough load balancer. Deployments are always part of a global deployment group which represents a global intercept service.
     */
    export interface Schema$InterceptDeployment {
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the deployment. Used as additional context for the deployment.
         */
        description?: string | null;
        /**
         * Required. Immutable. The regional forwarding rule that fronts the interceptors, for example: `projects/123456789/regions/us-central1/forwardingRules/my-rule`. See https://google.aip.dev/124.
         */
        forwardingRule?: string | null;
        /**
         * Required. Immutable. The deployment group that this deployment is a part of, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        interceptDeploymentGroup?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. linking a new association to the parent group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the deployment. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * A deployment group aggregates many zonal intercept backends (deployments) into a single global intercept service. Consumers can connect this service using an endpoint group.
     */
    export interface Schema$InterceptDeploymentGroup {
        /**
         * Output only. The list of endpoint groups that are connected to this resource.
         */
        connectedEndpointGroups?: Schema$InterceptDeploymentGroupConnectedEndpointGroup[];
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the deployment group. Used as additional context for the deployment group.
         */
        description?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of locations where the deployment group is present.
         */
        locations?: Schema$InterceptLocation[];
        /**
         * Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The list of Intercept Deployments that belong to this group.
         */
        nestedDeployments?: Schema$InterceptDeploymentGroupDeployment[];
        /**
         * Required. Immutable. The network that will be used for all child deployments, for example: `projects/{project\}/global/networks/{network\}`. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new deployment to the group) See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the deployment group. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * An endpoint group connected to this deployment group.
     */
    export interface Schema$InterceptDeploymentGroupConnectedEndpointGroup {
        /**
         * Output only. The connected endpoint group's resource name, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/124.
         */
        name?: string | null;
    }
    /**
     * A deployment belonging to this deployment group.
     */
    export interface Schema$InterceptDeploymentGroupDeployment {
        /**
         * Output only. The name of the Intercept Deployment, in the format: `projects/{project\}/locations/{location\}/interceptDeployments/{intercept_deployment\}`.
         */
        name?: string | null;
        /**
         * Output only. Most recent known state of the deployment.
         */
        state?: string | null;
    }
    /**
     * An endpoint group is a consumer frontend for a deployment group (backend). In order to configure intercept for a network, consumers must create: - An association between their network and the endpoint group. - A security profile that points to the endpoint group. - A firewall rule that references the security profile (group).
     */
    export interface Schema$InterceptEndpointGroup {
        /**
         * Output only. List of associations to this endpoint group.
         */
        associations?: Schema$InterceptEndpointGroupAssociationDetails[];
        /**
         * Output only. Details about the connected deployment group to this endpoint group.
         */
        connectedDeploymentGroup?: Schema$InterceptEndpointGroupConnectedDeploymentGroup;
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the endpoint group. Used as additional context for the endpoint group.
         */
        description?: string | null;
        /**
         * Required. Immutable. The deployment group that this endpoint group is connected to, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        interceptDeploymentGroup?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new association to the group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the endpoint group. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * An endpoint group association represents a link between a network and an endpoint group in the organization. Creating an association creates the networking infrastructure linking the network to the endpoint group, but does not enable intercept by itself. To enable intercept, the user must also create a network firewall policy containing intercept rules and associate it with the network.
     */
    export interface Schema$InterceptEndpointGroupAssociation {
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Required. Immutable. The endpoint group that this association is connected to, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/124.
         */
        interceptEndpointGroup?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of locations where the association is configured. This information is retrieved from the linked endpoint group.
         */
        locations?: Schema$InterceptLocation[];
        /**
         * Output only. The list of locations where the association is present. This information is retrieved from the linked endpoint group, and not configured as part of the association itself.
         */
        locationsDetails?: Schema$InterceptEndpointGroupAssociationLocationDetails[];
        /**
         * Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Required. Immutable. The VPC network that is associated. for example: `projects/123456789/global/networks/my-network`. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. adding a new location to the target deployment group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Current state of the endpoint group association.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * The endpoint group's view of a connected association.
     */
    export interface Schema$InterceptEndpointGroupAssociationDetails {
        /**
         * Output only. The connected association's resource name, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-ega`. See https://google.aip.dev/124.
         */
        name?: string | null;
        /**
         * Output only. The associated network, for example: projects/123456789/global/networks/my-network. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. Most recent known state of the association.
         */
        state?: string | null;
    }
    /**
     * Contains details about the state of an association in a specific cloud location.
     */
    export interface Schema$InterceptEndpointGroupAssociationLocationDetails {
        /**
         * Output only. The cloud location, e.g. "us-central1-a" or "asia-south1".
         */
        location?: string | null;
        /**
         * Output only. The current state of the association in this location.
         */
        state?: string | null;
    }
    /**
     * The endpoint group's view of a connected deployment group.
     */
    export interface Schema$InterceptEndpointGroupConnectedDeploymentGroup {
        /**
         * Output only. The list of locations where the deployment group is present.
         */
        locations?: Schema$InterceptLocation[];
        /**
         * Output only. The connected deployment group's resource name, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        name?: string | null;
    }
    /**
     * Details about intercept in a specific cloud location.
     */
    export interface Schema$InterceptLocation {
        /**
         * Output only. The cloud location, e.g. "us-central1-a" or "asia-south1".
         */
        location?: string | null;
        /**
         * Output only. The current state of the association in this location.
         */
        state?: string | null;
    }
    /**
     * Response of the ListAddressGroupReferences method.
     */
    export interface Schema$ListAddressGroupReferencesResponse {
        /**
         * A list of references that matches the specified filter in the request.
         */
        addressGroupReferences?: Schema$ListAddressGroupReferencesResponseAddressGroupReference[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
    }
    /**
     * The Reference of AddressGroup.
     */
    export interface Schema$ListAddressGroupReferencesResponseAddressGroupReference {
        /**
         * FirewallPolicy that is using the Address Group.
         */
        firewallPolicy?: string | null;
        /**
         * Rule priority of the FirewallPolicy that is using the Address Group.
         */
        rulePriority?: number | null;
        /**
         * Cloud Armor SecurityPolicy that is using the Address Group.
         */
        securityPolicy?: string | null;
    }
    /**
     * Response returned by the ListAddressGroups method.
     */
    export interface Schema$ListAddressGroupsResponse {
        /**
         * List of AddressGroups resources.
         */
        addressGroups?: Schema$AddressGroup[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListAuthorizationPolicies method.
     */
    export interface Schema$ListAuthorizationPoliciesResponse {
        /**
         * List of AuthorizationPolicies resources.
         */
        authorizationPolicies?: Schema$AuthorizationPolicy[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
    }
    /**
     * Message for response to listing `AuthzPolicy` resources.
     */
    export interface Schema$ListAuthzPoliciesResponse {
        /**
         * The list of `AuthzPolicy` resources.
         */
        authzPolicies?: Schema$AuthzPolicy[];
        /**
         * A token identifying a page of results that the server returns.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListBackendAuthenticationConfigs method.
     */
    export interface Schema$ListBackendAuthenticationConfigsResponse {
        /**
         * List of BackendAuthenticationConfig resources.
         */
        backendAuthenticationConfigs?: Schema$BackendAuthenticationConfig[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListClientTlsPolicies method.
     */
    export interface Schema$ListClientTlsPoliciesResponse {
        /**
         * List of ClientTlsPolicy resources.
         */
        clientTlsPolicies?: Schema$ClientTlsPolicy[];
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
    }
    /**
     * Message for response to listing Associations
     */
    export interface Schema$ListFirewallEndpointAssociationsResponse {
        /**
         * The list of Association
         */
        firewallEndpointAssociations?: Schema$FirewallEndpointAssociation[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Message for response to listing Endpoints
     */
    export interface Schema$ListFirewallEndpointsResponse {
        /**
         * The list of Endpoint
         */
        firewallEndpoints?: Schema$FirewallEndpoint[];
        /**
         * A token identifying a page of results the server should return.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListGatewaySecurityPolicies method.
     */
    export interface Schema$ListGatewaySecurityPoliciesResponse {
        /**
         * List of GatewaySecurityPolicies resources.
         */
        gatewaySecurityPolicies?: Schema$GatewaySecurityPolicy[];
        /**
         * If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListGatewaySecurityPolicyRules method.
     */
    export interface Schema$ListGatewaySecurityPolicyRulesResponse {
        /**
         * List of GatewaySecurityPolicyRule resources.
         */
        gatewaySecurityPolicyRules?: Schema$GatewaySecurityPolicyRule[];
        /**
         * If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for ListInterceptDeploymentGroups.
     */
    export interface Schema$ListInterceptDeploymentGroupsResponse {
        /**
         * The deployment groups from the specified parent.
         */
        interceptDeploymentGroups?: Schema$InterceptDeploymentGroup[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListInterceptDeployments.
     */
    export interface Schema$ListInterceptDeploymentsResponse {
        /**
         * The deployments from the specified parent.
         */
        interceptDeployments?: Schema$InterceptDeployment[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for ListInterceptEndpointGroupAssociations.
     */
    export interface Schema$ListInterceptEndpointGroupAssociationsResponse {
        /**
         * The associations from the specified parent.
         */
        interceptEndpointGroupAssociations?: Schema$InterceptEndpointGroupAssociation[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListInterceptEndpointGroups.
     */
    export interface Schema$ListInterceptEndpointGroupsResponse {
        /**
         * The endpoint groups from the specified parent.
         */
        interceptEndpointGroups?: Schema$InterceptEndpointGroup[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListMirroringDeploymentGroups.
     */
    export interface Schema$ListMirroringDeploymentGroupsResponse {
        /**
         * The deployment groups from the specified parent.
         */
        mirroringDeploymentGroups?: Schema$MirroringDeploymentGroup[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListMirroringDeployments.
     */
    export interface Schema$ListMirroringDeploymentsResponse {
        /**
         * The deployments from the specified parent.
         */
        mirroringDeployments?: Schema$MirroringDeployment[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response message for ListMirroringEndpointGroupAssociations.
     */
    export interface Schema$ListMirroringEndpointGroupAssociationsResponse {
        /**
         * The associations from the specified parent.
         */
        mirroringEndpointGroupAssociations?: Schema$MirroringEndpointGroupAssociation[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for ListMirroringEndpointGroups.
     */
    export interface Schema$ListMirroringEndpointGroupsResponse {
        /**
         * The endpoint groups from the specified parent.
         */
        mirroringEndpointGroups?: Schema$MirroringEndpointGroup[];
        /**
         * A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response returned by the ListSecurityProfileGroups method.
     */
    export interface Schema$ListSecurityProfileGroupsResponse {
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * List of SecurityProfileGroups resources.
         */
        securityProfileGroups?: Schema$SecurityProfileGroup[];
    }
    /**
     * Response returned by the ListSecurityProfiles method.
     */
    export interface Schema$ListSecurityProfilesResponse {
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * List of SecurityProfile resources.
         */
        securityProfiles?: Schema$SecurityProfile[];
    }
    /**
     * Response returned by the ListServerTlsPolicies method.
     */
    export interface Schema$ListServerTlsPoliciesResponse {
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * List of ServerTlsPolicy resources.
         */
        serverTlsPolicies?: Schema$ServerTlsPolicy[];
        /**
         * Unreachable resources. Populated when the request opts into `return_partial_success` and reading across collections e.g. when attempting to list all resources across all supported locations.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListTlsInspectionPolicies method.
     */
    export interface Schema$ListTlsInspectionPoliciesResponse {
        /**
         * If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.
         */
        nextPageToken?: string | null;
        /**
         * List of TlsInspectionPolicies resources.
         */
        tlsInspectionPolicies?: Schema$TlsInspectionPolicy[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response returned by the ListUrlLists method.
     */
    export interface Schema$ListUrlListsResponse {
        /**
         * If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
        /**
         * List of UrlList resources.
         */
        urlLists?: Schema$UrlList[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * A deployment represents a zonal mirroring backend ready to accept GENEVE-encapsulated replica traffic, e.g. a zonal instance group fronted by an internal passthrough load balancer. Deployments are always part of a global deployment group which represents a global mirroring service.
     */
    export interface Schema$MirroringDeployment {
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the deployment. Used as additional context for the deployment.
         */
        description?: string | null;
        /**
         * Required. Immutable. The regional forwarding rule that fronts the mirroring collectors, for example: `projects/123456789/regions/us-central1/forwardingRules/my-rule`. See https://google.aip.dev/124.
         */
        forwardingRule?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Immutable. The deployment group that this deployment is a part of, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        mirroringDeploymentGroup?: string | null;
        /**
         * Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. linking a new association to the parent group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the deployment. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * A deployment group aggregates many zonal mirroring backends (deployments) into a single global mirroring service. Consumers can connect this service using an endpoint group.
     */
    export interface Schema$MirroringDeploymentGroup {
        /**
         * Output only. The list of endpoint groups that are connected to this resource.
         */
        connectedEndpointGroups?: Schema$MirroringDeploymentGroupConnectedEndpointGroup[];
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the deployment group. Used as additional context for the deployment group.
         */
        description?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of locations where the deployment group is present.
         */
        locations?: Schema$MirroringLocation[];
        /**
         * Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The list of Mirroring Deployments that belong to this group.
         */
        nestedDeployments?: Schema$MirroringDeploymentGroupDeployment[];
        /**
         * Required. Immutable. The network that will be used for all child deployments, for example: `projects/{project\}/global/networks/{network\}`. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new deployment to the group) See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the deployment group. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * An endpoint group connected to this deployment group.
     */
    export interface Schema$MirroringDeploymentGroupConnectedEndpointGroup {
        /**
         * Output only. The connected endpoint group's resource name, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/124.
         */
        name?: string | null;
    }
    /**
     * A deployment belonging to this deployment group.
     */
    export interface Schema$MirroringDeploymentGroupDeployment {
        /**
         * Output only. The name of the Mirroring Deployment, in the format: `projects/{project\}/locations/{location\}/mirroringDeployments/{mirroring_deployment\}`.
         */
        name?: string | null;
        /**
         * Output only. Most recent known state of the deployment.
         */
        state?: string | null;
    }
    /**
     * An endpoint group is a consumer frontend for a deployment group (backend). In order to configure mirroring for a network, consumers must create: - An association between their network and the endpoint group. - A security profile that points to the endpoint group. - A mirroring rule that references the security profile (group).
     */
    export interface Schema$MirroringEndpointGroup {
        /**
         * Output only. List of associations to this endpoint group.
         */
        associations?: Schema$MirroringEndpointGroupAssociationDetails[];
        /**
         * Output only. List of details about the connected deployment groups to this endpoint group.
         */
        connectedDeploymentGroups?: Schema$MirroringEndpointGroupConnectedDeploymentGroup[];
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. User-provided description of the endpoint group. Used as additional context for the endpoint group.
         */
        description?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. The deployment group that this DIRECT endpoint group is connected to, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        mirroringDeploymentGroup?: string | null;
        /**
         * Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new association to the group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. The current state of the endpoint group. See https://google.aip.dev/216.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * An endpoint group association represents a link between a network and an endpoint group in the organization. Creating an association creates the networking infrastructure linking the network to the endpoint group, but does not enable mirroring by itself. To enable mirroring, the user must also create a network firewall policy containing mirroring rules and associate it with the network.
     */
    export interface Schema$MirroringEndpointGroupAssociation {
        /**
         * Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.
         */
        createTime?: string | null;
        /**
         * Optional. Labels are key/value pairs that help to organize and filter resources.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of locations where the association is configured. This information is retrieved from the linked endpoint group.
         */
        locations?: Schema$MirroringLocation[];
        /**
         * Output only. The list of locations where the association is present. This information is retrieved from the linked endpoint group, and not configured as part of the association itself.
         */
        locationsDetails?: Schema$MirroringEndpointGroupAssociationLocationDetails[];
        /**
         * Immutable. The endpoint group that this association is connected to, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/124.
         */
        mirroringEndpointGroup?: string | null;
        /**
         * Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.
         */
        name?: string | null;
        /**
         * Immutable. The VPC network that is associated. for example: `projects/123456789/global/networks/my-network`. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. adding a new location to the target deployment group). See https://google.aip.dev/128.
         */
        reconciling?: boolean | null;
        /**
         * Output only. Current state of the endpoint group association.
         */
        state?: string | null;
        /**
         * Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.
         */
        updateTime?: string | null;
    }
    /**
     * The endpoint group's view of a connected association.
     */
    export interface Schema$MirroringEndpointGroupAssociationDetails {
        /**
         * Output only. The connected association's resource name, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-ega`. See https://google.aip.dev/124.
         */
        name?: string | null;
        /**
         * Output only. The associated network, for example: projects/123456789/global/networks/my-network. See https://google.aip.dev/124.
         */
        network?: string | null;
        /**
         * Output only. Most recent known state of the association.
         */
        state?: string | null;
    }
    /**
     * Contains details about the state of an association in a specific cloud location.
     */
    export interface Schema$MirroringEndpointGroupAssociationLocationDetails {
        /**
         * Output only. The cloud location, e.g. "us-central1-a" or "asia-south1".
         */
        location?: string | null;
        /**
         * Output only. The current state of the association in this location.
         */
        state?: string | null;
    }
    /**
     * The endpoint group's view of a connected deployment group.
     */
    export interface Schema$MirroringEndpointGroupConnectedDeploymentGroup {
        /**
         * Output only. The list of locations where the deployment group is present.
         */
        locations?: Schema$MirroringLocation[];
        /**
         * Output only. The connected deployment group's resource name, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.
         */
        name?: string | null;
    }
    /**
     * Details about mirroring in a specific cloud location.
     */
    export interface Schema$MirroringLocation {
        /**
         * Output only. The cloud location, e.g. "us-central1-a" or "asia-south1".
         */
        location?: string | null;
        /**
         * Output only. The current state of the association in this location.
         */
        state?: string | null;
    }
    /**
     * Specification of the MTLSPolicy.
     */
    export interface Schema$MTLSPolicy {
        /**
         * Required if the policy is to be used with Traffic Director. For Application Load Balancers it must be empty. Defines the mechanism to obtain the Certificate Authority certificate to validate the client certificate.
         */
        clientValidationCa?: Schema$ValidationCA[];
        /**
         * When the client presents an invalid certificate or no certificate to the load balancer, the `client_validation_mode` specifies how the client connection is handled. Required if the policy is to be used with the Application Load Balancers. For Traffic Director it must be empty.
         */
        clientValidationMode?: string | null;
        /**
         * Reference to the TrustConfig from certificatemanager.googleapis.com namespace. If specified, the chain validation will be performed against certificates configured in the given TrustConfig. Allowed only if the policy is to be used with Application Load Balancers.
         */
        clientValidationTrustConfig?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Request used by the RemoveAddressGroupItems method.
     */
    export interface Schema$RemoveAddressGroupItemsRequest {
        /**
         * Required. List of items to remove.
         */
        items?: string[] | null;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string | null;
    }
    /**
     * Specification of rules.
     */
    export interface Schema$Rule {
        /**
         * Optional. List of attributes for the traffic destination. All of the destinations must match. A destination is a match if a request matches all the specified hosts, ports, methods and headers. If not set, the action specified in the 'action' field will be applied without any rule checks for the destination.
         */
        destinations?: Schema$Destination[];
        /**
         * Optional. List of attributes for the traffic source. All of the sources must match. A source is a match if both principals and ip_blocks match. If not set, the action specified in the 'action' field will be applied without any rule checks for the source.
         */
        sources?: Schema$Source[];
    }
    /**
     * SecurityProfile is a resource that defines the behavior for one of many ProfileTypes.
     */
    export interface Schema$SecurityProfile {
        /**
         * Output only. Resource creation timestamp.
         */
        createTime?: string | null;
        /**
         * The custom TPPI configuration for the SecurityProfile.
         */
        customInterceptProfile?: Schema$CustomInterceptProfile;
        /**
         * The custom Packet Mirroring v2 configuration for the SecurityProfile.
         */
        customMirroringProfile?: Schema$CustomMirroringProfile;
        /**
         * Optional. An optional description of the profile. Max length 512 characters.
         */
        description?: string | null;
        /**
         * Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Optional. Labels as key value pairs.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. Name of the SecurityProfile resource. It matches pattern `projects|organizations/x/locations/{location\}/securityProfiles/{security_profile\}`.
         */
        name?: string | null;
        /**
         * The threat prevention configuration for the SecurityProfile.
         */
        threatPreventionProfile?: Schema$ThreatPreventionProfile;
        /**
         * Immutable. The single ProfileType that the SecurityProfile resource configures.
         */
        type?: string | null;
        /**
         * Output only. Last resource update timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * SecurityProfileGroup is a resource that defines the behavior for various ProfileTypes.
     */
    export interface Schema$SecurityProfileGroup {
        /**
         * Output only. Resource creation timestamp.
         */
        createTime?: string | null;
        /**
         * Optional. Reference to a SecurityProfile with the CustomIntercept configuration.
         */
        customInterceptProfile?: string | null;
        /**
         * Optional. Reference to a SecurityProfile with the CustomMirroring configuration.
         */
        customMirroringProfile?: string | null;
        /**
         * Output only. Identifier used by the data-path. Unique within {container, location\}.
         */
        dataPathId?: string | null;
        /**
         * Optional. An optional description of the profile group. Max length 2048 characters.
         */
        description?: string | null;
        /**
         * Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Optional. Labels as key value pairs.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Immutable. Identifier. Name of the SecurityProfileGroup resource. It matches pattern `projects|organizations/x/locations/{location\}/securityProfileGroups/{security_profile_group\}`.
         */
        name?: string | null;
        /**
         * Optional. Reference to a SecurityProfile with the ThreatPrevention configuration.
         */
        threatPreventionProfile?: string | null;
        /**
         * Output only. Last resource update timestamp.
         */
        updateTime?: string | null;
    }
    /**
     * ServerTlsPolicy is a resource that specifies how a server should authenticate incoming requests. This resource itself does not affect configuration unless it is attached to a target HTTPS proxy or endpoint config selector resource. ServerTlsPolicy in the form accepted by Application Load Balancers can be attached only to TargetHttpsProxy with an `EXTERNAL`, `EXTERNAL_MANAGED` or `INTERNAL_MANAGED` load balancing scheme. Traffic Director compatible ServerTlsPolicies can be attached to EndpointPolicy and TargetHttpsProxy with Traffic Director `INTERNAL_SELF_MANAGED` load balancing scheme.
     */
    export interface Schema$ServerTlsPolicy {
        /**
         * This field applies only for Traffic Director policies. It is must be set to false for Application Load Balancer policies. Determines if server allows plaintext connections. If set to true, server allows plain text connections. By default, it is set to false. This setting is not exclusive of other encryption modes. For example, if `allow_open` and `mtls_policy` are set, server allows both plain text and mTLS connections. See documentation of other encryption modes to confirm compatibility. Consider using it if you wish to upgrade in place your deployment to TLS while having mixed TLS and non-TLS traffic reaching port :80.
         */
        allowOpen?: boolean | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Set of label tags associated with the resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * This field is required if the policy is used with Application Load Balancers. This field can be empty for Traffic Director. Defines a mechanism to provision peer validation certificates for peer to peer authentication (Mutual TLS - mTLS). If not specified, client certificate will not be requested. The connection is treated as TLS and not mTLS. If `allow_open` and `mtls_policy` are set, server allows both plain text and mTLS connections.
         */
        mtlsPolicy?: Schema$MTLSPolicy;
        /**
         * Required. Name of the ServerTlsPolicy resource. It matches the pattern `projects/x/locations/{location\}/serverTlsPolicies/{server_tls_policy\}`
         */
        name?: string | null;
        /**
         * Optional if policy is to be used with Traffic Director. For Application Load Balancers must be empty. Defines a mechanism to provision server identity (public and private keys). Cannot be combined with `allow_open` as a permissive mode that allows both plain text and TLS is not supported.
         */
        serverCertificate?: Schema$GoogleCloudNetworksecurityV1beta1CertificateProvider;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * Defines what action to take for a specific severity match.
     */
    export interface Schema$SeverityOverride {
        /**
         * Required. Threat action override.
         */
        action?: string | null;
        /**
         * Required. Severity level to match.
         */
        severity?: string | null;
    }
    /**
     * Specification of traffic source attributes.
     */
    export interface Schema$Source {
        /**
         * Optional. List of CIDR ranges to match based on source IP address. At least one IP block should match. Single IP (e.g., "*******") and CIDR (e.g., "*******/24") are supported. Authorization based on source IP alone should be avoided. The IP addresses of any load balancers or proxies should be considered untrusted.
         */
        ipBlocks?: string[] | null;
        /**
         * Optional. List of peer identities to match for authorization. At least one principal should match. Each peer can be an exact match, or a prefix match (example, "namespace/x") or a suffix match (example, "x/service-account") or a presence match "*". Authorization based on the principal name without certificate validation (configured by ServerTlsPolicy resource) is considered insecure.
         */
        principals?: string[] | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Defines what action to take for a specific threat_id match.
     */
    export interface Schema$ThreatOverride {
        /**
         * Required. Threat action override. For some threat types, only a subset of actions applies.
         */
        action?: string | null;
        /**
         * Required. Vendor-specific ID of a threat to override.
         */
        threatId?: string | null;
        /**
         * Output only. Type of the threat (read only).
         */
        type?: string | null;
    }
    /**
     * ThreatPreventionProfile defines an action for specific threat signatures or severity levels.
     */
    export interface Schema$ThreatPreventionProfile {
        /**
         * Optional. Configuration for overriding antivirus actions per protocol.
         */
        antivirusOverrides?: Schema$AntivirusOverride[];
        /**
         * Optional. Configuration for overriding threats actions by severity match.
         */
        severityOverrides?: Schema$SeverityOverride[];
        /**
         * Optional. Configuration for overriding threats actions by threat_id match. If a threat is matched both by configuration provided in severity_overrides and threat_overrides, the threat_overrides action is applied.
         */
        threatOverrides?: Schema$ThreatOverride[];
    }
    /**
     * The TlsInspectionPolicy resource contains references to CA pools in Certificate Authority Service and associated metadata.
     */
    export interface Schema$TlsInspectionPolicy {
        /**
         * Required. A CA pool resource used to issue interception certificates. The CA pool string has a relative resource path following the form "projects/{project\}/locations/{location\}/caPools/{ca_pool\}".
         */
        caPool?: string | null;
        /**
         * Output only. The timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. List of custom TLS cipher suites selected. This field is valid only if the selected tls_feature_profile is CUSTOM. The compute.SslPoliciesService.ListAvailableFeatures method returns the set of features that can be specified in this list. Note that Secure Web Proxy does not yet honor this field.
         */
        customTlsFeatures?: string[] | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Optional. If FALSE (the default), use our default set of public CAs in addition to any CAs specified in trust_config. These public CAs are currently based on the Mozilla Root Program and are subject to change over time. If TRUE, do not accept our default set of public CAs. Only CAs specified in trust_config will be accepted. This defaults to FALSE (use public CAs in addition to trust_config) for backwards compatibility, but trusting public root CAs is *not recommended* unless the traffic in question is outbound to public web servers. When possible, prefer setting this to "false" and explicitly specifying trusted CAs and certificates in a TrustConfig. Note that Secure Web Proxy does not yet honor this field.
         */
        excludePublicCaSet?: boolean | null;
        /**
         * Optional. Minimum TLS version that the firewall should use when negotiating connections with both clients and servers. If this is not set, then the default value is to allow the broadest set of clients and servers (TLS 1.0 or higher). Setting this to more restrictive values may improve security, but may also prevent the firewall from connecting to some clients or servers. Note that Secure Web Proxy does not yet honor this field.
         */
        minTlsVersion?: string | null;
        /**
         * Required. Name of the resource. Name is of the form projects/{project\}/locations/{location\}/tlsInspectionPolicies/{tls_inspection_policy\} tls_inspection_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string | null;
        /**
         * Optional. The selected Profile. If this is not set, then the default value is to allow the broadest set of clients and servers ("PROFILE_COMPATIBLE"). Setting this to more restrictive values may improve security, but may also prevent the TLS inspection proxy from connecting to some clients or servers. Note that Secure Web Proxy does not yet honor this field.
         */
        tlsFeatureProfile?: string | null;
        /**
         * Optional. A TrustConfig resource used when making a connection to the TLS server. This is a relative resource path following the form "projects/{project\}/locations/{location\}/trustConfigs/{trust_config\}". This is necessary to intercept TLS connections to servers with certificates signed by a private CA or self-signed certificates. Note that Secure Web Proxy does not yet honor this field.
         */
        trustConfig?: string | null;
        /**
         * Output only. The timestamp when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * UrlList proto helps users to set reusable, independently manageable lists of hosts, host patterns, URLs, URL patterns.
     */
    export interface Schema$UrlList {
        /**
         * Output only. Time when the security policy was created.
         */
        createTime?: string | null;
        /**
         * Optional. Free-text description of the resource.
         */
        description?: string | null;
        /**
         * Required. Name of the resource provided by the user. Name is of the form projects/{project\}/locations/{location\}/urlLists/{url_list\} url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string | null;
        /**
         * Output only. Time when the security policy was updated.
         */
        updateTime?: string | null;
        /**
         * Required. FQDNs and URLs.
         */
        values?: string[] | null;
    }
    /**
     * Specification of ValidationCA. Defines the mechanism to obtain the Certificate Authority certificate to validate the peer certificate.
     */
    export interface Schema$ValidationCA {
        /**
         * The certificate provider instance specification that will be passed to the data plane, which will be used to load necessary credential information.
         */
        certificateProviderInstance?: Schema$CertificateProviderInstance;
        /**
         * gRPC specific configuration to access the gRPC server to obtain the CA certificate.
         */
        grpcEndpoint?: Schema$GoogleCloudNetworksecurityV1beta1GrpcEndpoint;
    }
    export class Resource$Organizations {
        context: APIRequestContext;
        locations: Resource$Organizations$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations {
        context: APIRequestContext;
        addressGroups: Resource$Organizations$Locations$Addressgroups;
        firewallEndpoints: Resource$Organizations$Locations$Firewallendpoints;
        operations: Resource$Organizations$Locations$Operations;
        securityProfileGroups: Resource$Organizations$Locations$Securityprofilegroups;
        securityProfiles: Resource$Organizations$Locations$Securityprofiles;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations$Addressgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Adds items to an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        addItems(params: Params$Resource$Organizations$Locations$Addressgroups$Additems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        addItems(params?: Params$Resource$Organizations$Locations$Addressgroups$Additems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        addItems(params: Params$Resource$Organizations$Locations$Addressgroups$Additems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        addItems(params: Params$Resource$Organizations$Locations$Addressgroups$Additems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        addItems(params: Params$Resource$Organizations$Locations$Addressgroups$Additems, callback: BodyResponseCallback<Schema$Operation>): void;
        addItems(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Clones items from one address group to another.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cloneItems(params: Params$Resource$Organizations$Locations$Addressgroups$Cloneitems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cloneItems(params?: Params$Resource$Organizations$Locations$Addressgroups$Cloneitems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        cloneItems(params: Params$Resource$Organizations$Locations$Addressgroups$Cloneitems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cloneItems(params: Params$Resource$Organizations$Locations$Addressgroups$Cloneitems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        cloneItems(params: Params$Resource$Organizations$Locations$Addressgroups$Cloneitems, callback: BodyResponseCallback<Schema$Operation>): void;
        cloneItems(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a new address group in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Addressgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Organizations$Locations$Addressgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Organizations$Locations$Addressgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Addressgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Organizations$Locations$Addressgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Addressgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Addressgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Organizations$Locations$Addressgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Addressgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Organizations$Locations$Addressgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Addressgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Addressgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AddressGroup>>;
        get(params: Params$Resource$Organizations$Locations$Addressgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Addressgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$AddressGroup>, callback: BodyResponseCallback<Schema$AddressGroup>): void;
        get(params: Params$Resource$Organizations$Locations$Addressgroups$Get, callback: BodyResponseCallback<Schema$AddressGroup>): void;
        get(callback: BodyResponseCallback<Schema$AddressGroup>): void;
        /**
         * Lists address groups in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Addressgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Addressgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAddressGroupsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Addressgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Addressgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListAddressGroupsResponse>, callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Addressgroups$List, callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        /**
         * Lists references of an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listReferences(params: Params$Resource$Organizations$Locations$Addressgroups$Listreferences, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listReferences(params?: Params$Resource$Organizations$Locations$Addressgroups$Listreferences, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAddressGroupReferencesResponse>>;
        listReferences(params: Params$Resource$Organizations$Locations$Addressgroups$Listreferences, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listReferences(params: Params$Resource$Organizations$Locations$Addressgroups$Listreferences, options: MethodOptions | BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>, callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        listReferences(params: Params$Resource$Organizations$Locations$Addressgroups$Listreferences, callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        listReferences(callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        /**
         * Updates parameters of an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Organizations$Locations$Addressgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Organizations$Locations$Addressgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Organizations$Locations$Addressgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Organizations$Locations$Addressgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Organizations$Locations$Addressgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Removes items from an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        removeItems(params: Params$Resource$Organizations$Locations$Addressgroups$Removeitems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        removeItems(params?: Params$Resource$Organizations$Locations$Addressgroups$Removeitems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        removeItems(params: Params$Resource$Organizations$Locations$Addressgroups$Removeitems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        removeItems(params: Params$Resource$Organizations$Locations$Addressgroups$Removeitems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        removeItems(params: Params$Resource$Organizations$Locations$Addressgroups$Removeitems, callback: BodyResponseCallback<Schema$Operation>): void;
        removeItems(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Additems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to add items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddAddressGroupItemsRequest;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Cloneitems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CloneAddressGroupItemsRequest;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Create extends StandardParameters {
        /**
         * Required. Short name of the AddressGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "authz_policy".
         */
        addressGroupId?: string;
        /**
         * Required. The parent resource of the AddressGroup. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddressGroup;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Delete extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to delete. Must be in the format `projects/x/locations/{location\}/addressGroups/x`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Get extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to get. Must be in the format `projects/x/locations/{location\}/addressGroups/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$List extends StandardParameters {
        /**
         * Maximum number of AddressGroups to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListAddressGroupsResponse` Indicates that this is a continuation of a prior `ListAddressGroups` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the AddressGroups should be listed, specified in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. If true, allow partial responses for multi-regional Aggregated List requests.
         */
        returnPartialSuccess?: boolean;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Listreferences extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * The maximum number of references to return. If unspecified, server will pick an appropriate default. Server may return fewer items than requested. A caller should only rely on response's next_page_token to determine if there are more AddressGroupUsers left to be queried.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Patch extends StandardParameters {
        /**
         * Required. Name of the AddressGroup resource. It matches pattern `projects/x/locations/{location\}/addressGroups/`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the AddressGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddressGroup;
    }
    export interface Params$Resource$Organizations$Locations$Addressgroups$Removeitems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to remove items from. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RemoveAddressGroupItemsRequest;
    }
    export class Resource$Organizations$Locations$Firewallendpoints {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new FirewallEndpoint in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Firewallendpoints$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Organizations$Locations$Firewallendpoints$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Organizations$Locations$Firewallendpoints$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Firewallendpoints$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Organizations$Locations$Firewallendpoints$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Endpoint.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Firewallendpoints$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Firewallendpoints$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Organizations$Locations$Firewallendpoints$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Firewallendpoints$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Organizations$Locations$Firewallendpoints$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Endpoint.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Firewallendpoints$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Firewallendpoints$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FirewallEndpoint>>;
        get(params: Params$Resource$Organizations$Locations$Firewallendpoints$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Firewallendpoints$Get, options: MethodOptions | BodyResponseCallback<Schema$FirewallEndpoint>, callback: BodyResponseCallback<Schema$FirewallEndpoint>): void;
        get(params: Params$Resource$Organizations$Locations$Firewallendpoints$Get, callback: BodyResponseCallback<Schema$FirewallEndpoint>): void;
        get(callback: BodyResponseCallback<Schema$FirewallEndpoint>): void;
        /**
         * Lists FirewallEndpoints in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Firewallendpoints$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Firewallendpoints$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListFirewallEndpointsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Firewallendpoints$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Firewallendpoints$List, options: MethodOptions | BodyResponseCallback<Schema$ListFirewallEndpointsResponse>, callback: BodyResponseCallback<Schema$ListFirewallEndpointsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Firewallendpoints$List, callback: BodyResponseCallback<Schema$ListFirewallEndpointsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListFirewallEndpointsResponse>): void;
        /**
         * Update a single Endpoint.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Organizations$Locations$Firewallendpoints$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Organizations$Locations$Firewallendpoints$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Organizations$Locations$Firewallendpoints$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Organizations$Locations$Firewallendpoints$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Organizations$Locations$Firewallendpoints$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Organizations$Locations$Firewallendpoints$Create extends StandardParameters {
        /**
         * Required. Id of the requesting object. If auto-generating Id server-side, remove this field and firewall_endpoint_id from the method_signature of Create RPC.
         */
        firewallEndpointId?: string;
        /**
         * Required. Value for parent.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallEndpoint;
    }
    export interface Params$Resource$Organizations$Locations$Firewallendpoints$Delete extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Organizations$Locations$Firewallendpoints$Get extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Firewallendpoints$List extends StandardParameters {
        /**
         * Optional. Filtering results
         */
        filter?: string;
        /**
         * Hint for how to order the results
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. Parent value for ListEndpointsRequest
         */
        parent?: string;
    }
    export interface Params$Resource$Organizations$Locations$Firewallendpoints$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. name of resource
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the Endpoint resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallEndpoint;
    }
    export class Resource$Organizations$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Organizations$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Organizations$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Organizations$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Organizations$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Organizations$Locations$Securityprofilegroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new SecurityProfileGroup in a given organization and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Organizations$Locations$Securityprofilegroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single SecurityProfileGroup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Securityprofilegroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single SecurityProfileGroup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Securityprofilegroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$SecurityProfileGroup>>;
        get(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Get, options: MethodOptions | BodyResponseCallback<Schema$SecurityProfileGroup>, callback: BodyResponseCallback<Schema$SecurityProfileGroup>): void;
        get(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Get, callback: BodyResponseCallback<Schema$SecurityProfileGroup>): void;
        get(callback: BodyResponseCallback<Schema$SecurityProfileGroup>): void;
        /**
         * Lists SecurityProfileGroups in a given organization and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Securityprofilegroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Securityprofilegroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListSecurityProfileGroupsResponse>>;
        list(params: Params$Resource$Organizations$Locations$Securityprofilegroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Securityprofilegroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListSecurityProfileGroupsResponse>, callback: BodyResponseCallback<Schema$ListSecurityProfileGroupsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Securityprofilegroups$List, callback: BodyResponseCallback<Schema$ListSecurityProfileGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSecurityProfileGroupsResponse>): void;
        /**
         * Updates the parameters of a single SecurityProfileGroup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Organizations$Locations$Securityprofilegroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Organizations$Locations$Securityprofilegroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofilegroups$Create extends StandardParameters {
        /**
         * Required. The parent resource of the SecurityProfileGroup. Must be in the format `projects|organizations/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Required. Short name of the SecurityProfileGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "security_profile_group1".
         */
        securityProfileGroupId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SecurityProfileGroup;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofilegroups$Delete extends StandardParameters {
        /**
         * Optional. If client provided etag is out of date, delete will return FAILED_PRECONDITION error.
         */
        etag?: string;
        /**
         * Required. A name of the SecurityProfileGroup to delete. Must be in the format `projects|organizations/x/locations/{location\}/securityProfileGroups/{security_profile_group\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofilegroups$Get extends StandardParameters {
        /**
         * Required. A name of the SecurityProfileGroup to get. Must be in the format `projects|organizations/x/locations/{location\}/securityProfileGroups/{security_profile_group\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofilegroups$List extends StandardParameters {
        /**
         * Maximum number of SecurityProfileGroups to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListSecurityProfileGroupsResponse` Indicates that this is a continuation of a prior `ListSecurityProfileGroups` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project or organization and location from which the SecurityProfileGroups should be listed, specified in the format `projects|organizations/x/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofilegroups$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. Name of the SecurityProfileGroup resource. It matches pattern `projects|organizations/x/locations/{location\}/securityProfileGroups/{security_profile_group\}`.
         */
        name?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the SecurityProfileGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SecurityProfileGroup;
    }
    export class Resource$Organizations$Locations$Securityprofiles {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new SecurityProfile in a given organization and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Securityprofiles$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Organizations$Locations$Securityprofiles$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Organizations$Locations$Securityprofiles$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Securityprofiles$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Organizations$Locations$Securityprofiles$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single SecurityProfile.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Organizations$Locations$Securityprofiles$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Organizations$Locations$Securityprofiles$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Organizations$Locations$Securityprofiles$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Organizations$Locations$Securityprofiles$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Organizations$Locations$Securityprofiles$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single SecurityProfile.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Securityprofiles$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Organizations$Locations$Securityprofiles$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$SecurityProfile>>;
        get(params: Params$Resource$Organizations$Locations$Securityprofiles$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Securityprofiles$Get, options: MethodOptions | BodyResponseCallback<Schema$SecurityProfile>, callback: BodyResponseCallback<Schema$SecurityProfile>): void;
        get(params: Params$Resource$Organizations$Locations$Securityprofiles$Get, callback: BodyResponseCallback<Schema$SecurityProfile>): void;
        get(callback: BodyResponseCallback<Schema$SecurityProfile>): void;
        /**
         * Lists SecurityProfiles in a given organization and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Securityprofiles$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Organizations$Locations$Securityprofiles$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListSecurityProfilesResponse>>;
        list(params: Params$Resource$Organizations$Locations$Securityprofiles$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Securityprofiles$List, options: MethodOptions | BodyResponseCallback<Schema$ListSecurityProfilesResponse>, callback: BodyResponseCallback<Schema$ListSecurityProfilesResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Securityprofiles$List, callback: BodyResponseCallback<Schema$ListSecurityProfilesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSecurityProfilesResponse>): void;
        /**
         * Updates the parameters of a single SecurityProfile.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Organizations$Locations$Securityprofiles$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Organizations$Locations$Securityprofiles$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Organizations$Locations$Securityprofiles$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Organizations$Locations$Securityprofiles$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Organizations$Locations$Securityprofiles$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofiles$Create extends StandardParameters {
        /**
         * Required. The parent resource of the SecurityProfile. Must be in the format `projects|organizations/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Required. Short name of the SecurityProfile resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "security_profile1".
         */
        securityProfileId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SecurityProfile;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofiles$Delete extends StandardParameters {
        /**
         * Optional. If client provided etag is out of date, delete will return FAILED_PRECONDITION error.
         */
        etag?: string;
        /**
         * Required. A name of the SecurityProfile to delete. Must be in the format `projects|organizations/x/locations/{location\}/securityProfiles/{security_profile_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofiles$Get extends StandardParameters {
        /**
         * Required. A name of the SecurityProfile to get. Must be in the format `projects|organizations/x/locations/{location\}/securityProfiles/{security_profile_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofiles$List extends StandardParameters {
        /**
         * Maximum number of SecurityProfiles to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListSecurityProfilesResponse` Indicates that this is a continuation of a prior `ListSecurityProfiles` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project or organization and location from which the SecurityProfiles should be listed, specified in the format `projects|organizations/x/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Organizations$Locations$Securityprofiles$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. Name of the SecurityProfile resource. It matches pattern `projects|organizations/x/locations/{location\}/securityProfiles/{security_profile\}`.
         */
        name?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the SecurityProfile resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SecurityProfile;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        addressGroups: Resource$Projects$Locations$Addressgroups;
        authorizationPolicies: Resource$Projects$Locations$Authorizationpolicies;
        authzPolicies: Resource$Projects$Locations$Authzpolicies;
        backendAuthenticationConfigs: Resource$Projects$Locations$Backendauthenticationconfigs;
        clientTlsPolicies: Resource$Projects$Locations$Clienttlspolicies;
        firewallEndpointAssociations: Resource$Projects$Locations$Firewallendpointassociations;
        gatewaySecurityPolicies: Resource$Projects$Locations$Gatewaysecuritypolicies;
        interceptDeploymentGroups: Resource$Projects$Locations$Interceptdeploymentgroups;
        interceptDeployments: Resource$Projects$Locations$Interceptdeployments;
        interceptEndpointGroupAssociations: Resource$Projects$Locations$Interceptendpointgroupassociations;
        interceptEndpointGroups: Resource$Projects$Locations$Interceptendpointgroups;
        mirroringDeploymentGroups: Resource$Projects$Locations$Mirroringdeploymentgroups;
        mirroringDeployments: Resource$Projects$Locations$Mirroringdeployments;
        mirroringEndpointGroupAssociations: Resource$Projects$Locations$Mirroringendpointgroupassociations;
        mirroringEndpointGroups: Resource$Projects$Locations$Mirroringendpointgroups;
        operations: Resource$Projects$Locations$Operations;
        serverTlsPolicies: Resource$Projects$Locations$Servertlspolicies;
        tlsInspectionPolicies: Resource$Projects$Locations$Tlsinspectionpolicies;
        urlLists: Resource$Projects$Locations$Urllists;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Addressgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Adds items to an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        addItems(params: Params$Resource$Projects$Locations$Addressgroups$Additems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        addItems(params?: Params$Resource$Projects$Locations$Addressgroups$Additems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        addItems(params: Params$Resource$Projects$Locations$Addressgroups$Additems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        addItems(params: Params$Resource$Projects$Locations$Addressgroups$Additems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        addItems(params: Params$Resource$Projects$Locations$Addressgroups$Additems, callback: BodyResponseCallback<Schema$Operation>): void;
        addItems(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Clones items from one address group to another.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cloneItems(params: Params$Resource$Projects$Locations$Addressgroups$Cloneitems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cloneItems(params?: Params$Resource$Projects$Locations$Addressgroups$Cloneitems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        cloneItems(params: Params$Resource$Projects$Locations$Addressgroups$Cloneitems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cloneItems(params: Params$Resource$Projects$Locations$Addressgroups$Cloneitems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        cloneItems(params: Params$Resource$Projects$Locations$Addressgroups$Cloneitems, callback: BodyResponseCallback<Schema$Operation>): void;
        cloneItems(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a new address group in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Addressgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Addressgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Addressgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Addressgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Addressgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Addressgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Addressgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Addressgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Addressgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Addressgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Addressgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Addressgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AddressGroup>>;
        get(params: Params$Resource$Projects$Locations$Addressgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Addressgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$AddressGroup>, callback: BodyResponseCallback<Schema$AddressGroup>): void;
        get(params: Params$Resource$Projects$Locations$Addressgroups$Get, callback: BodyResponseCallback<Schema$AddressGroup>): void;
        get(callback: BodyResponseCallback<Schema$AddressGroup>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Addressgroups$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists address groups in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Addressgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Addressgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAddressGroupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Addressgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Addressgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListAddressGroupsResponse>, callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Addressgroups$List, callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAddressGroupsResponse>): void;
        /**
         * Lists references of an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listReferences(params: Params$Resource$Projects$Locations$Addressgroups$Listreferences, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listReferences(params?: Params$Resource$Projects$Locations$Addressgroups$Listreferences, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAddressGroupReferencesResponse>>;
        listReferences(params: Params$Resource$Projects$Locations$Addressgroups$Listreferences, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listReferences(params: Params$Resource$Projects$Locations$Addressgroups$Listreferences, options: MethodOptions | BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>, callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        listReferences(params: Params$Resource$Projects$Locations$Addressgroups$Listreferences, callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        listReferences(callback: BodyResponseCallback<Schema$ListAddressGroupReferencesResponse>): void;
        /**
         * Updates the parameters of a single address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Addressgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Addressgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Addressgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Addressgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Addressgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Removes items from an address group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        removeItems(params: Params$Resource$Projects$Locations$Addressgroups$Removeitems, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        removeItems(params?: Params$Resource$Projects$Locations$Addressgroups$Removeitems, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        removeItems(params: Params$Resource$Projects$Locations$Addressgroups$Removeitems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        removeItems(params: Params$Resource$Projects$Locations$Addressgroups$Removeitems, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        removeItems(params: Params$Resource$Projects$Locations$Addressgroups$Removeitems, callback: BodyResponseCallback<Schema$Operation>): void;
        removeItems(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Addressgroups$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Addressgroups$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Addressgroups$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Addressgroups$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Addressgroups$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Addressgroups$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Addressgroups$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Additems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to add items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddAddressGroupItemsRequest;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Cloneitems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CloneAddressGroupItemsRequest;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Create extends StandardParameters {
        /**
         * Required. Short name of the AddressGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "authz_policy".
         */
        addressGroupId?: string;
        /**
         * Required. The parent resource of the AddressGroup. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddressGroup;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Delete extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to delete. Must be in the format `projects/x/locations/{location\}/addressGroups/x`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Get extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to get. Must be in the format `projects/x/locations/{location\}/addressGroups/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$List extends StandardParameters {
        /**
         * Maximum number of AddressGroups to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListAddressGroupsResponse` Indicates that this is a continuation of a prior `ListAddressGroups` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the AddressGroups should be listed, specified in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. If true, allow partial responses for multi-regional Aggregated List requests.
         */
        returnPartialSuccess?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Listreferences extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * The maximum number of references to return. If unspecified, server will pick an appropriate default. Server may return fewer items than requested. A caller should only rely on response's next_page_token to determine if there are more AddressGroupUsers left to be queried.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Patch extends StandardParameters {
        /**
         * Required. Name of the AddressGroup resource. It matches pattern `projects/x/locations/{location\}/addressGroups/`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the AddressGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddressGroup;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Removeitems extends StandardParameters {
        /**
         * Required. A name of the AddressGroup to remove items from. Must be in the format `projects|organization/x/locations/{location\}/addressGroups/x`.
         */
        addressGroup?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RemoveAddressGroupItemsRequest;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Addressgroups$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Authorizationpolicies {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new AuthorizationPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Authorizationpolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Authorizationpolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Authorizationpolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Authorizationpolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single AuthorizationPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Authorizationpolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Authorizationpolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Authorizationpolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Authorizationpolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single AuthorizationPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Authorizationpolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AuthorizationPolicy>>;
        get(params: Params$Resource$Projects$Locations$Authorizationpolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Authorizationpolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$AuthorizationPolicy>, callback: BodyResponseCallback<Schema$AuthorizationPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Authorizationpolicies$Get, callback: BodyResponseCallback<Schema$AuthorizationPolicy>): void;
        get(callback: BodyResponseCallback<Schema$AuthorizationPolicy>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists AuthorizationPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Authorizationpolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Authorizationpolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAuthorizationPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Authorizationpolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Authorizationpolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizationPoliciesResponse>, callback: BodyResponseCallback<Schema$ListAuthorizationPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Authorizationpolicies$List, callback: BodyResponseCallback<Schema$ListAuthorizationPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizationPoliciesResponse>): void;
        /**
         * Updates the parameters of a single AuthorizationPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Authorizationpolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Authorizationpolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Authorizationpolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Authorizationpolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Create extends StandardParameters {
        /**
         * Required. Short name of the AuthorizationPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "authz_policy".
         */
        authorizationPolicyId?: string;
        /**
         * Required. The parent resource of the AuthorizationPolicy. Must be in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizationPolicy;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Delete extends StandardParameters {
        /**
         * Required. A name of the AuthorizationPolicy to delete. Must be in the format `projects/{project\}/locations/{location\}/authorizationPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Get extends StandardParameters {
        /**
         * Required. A name of the AuthorizationPolicy to get. Must be in the format `projects/{project\}/locations/{location\}/authorizationPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$List extends StandardParameters {
        /**
         * Maximum number of AuthorizationPolicies to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListAuthorizationPoliciesResponse` Indicates that this is a continuation of a prior `ListAuthorizationPolicies` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the AuthorizationPolicies should be listed, specified in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Patch extends StandardParameters {
        /**
         * Required. Name of the AuthorizationPolicy resource. It matches pattern `projects/{project\}/locations/{location\}/authorizationPolicies/`.
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the AuthorizationPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizationPolicy;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Authorizationpolicies$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Authzpolicies {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new AuthzPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Authzpolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Authzpolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Authzpolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Authzpolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Authzpolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single AuthzPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Authzpolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Authzpolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Authzpolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Authzpolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Authzpolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single AuthzPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Authzpolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Authzpolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AuthzPolicy>>;
        get(params: Params$Resource$Projects$Locations$Authzpolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Authzpolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$AuthzPolicy>, callback: BodyResponseCallback<Schema$AuthzPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Authzpolicies$Get, callback: BodyResponseCallback<Schema$AuthzPolicy>): void;
        get(callback: BodyResponseCallback<Schema$AuthzPolicy>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists AuthzPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Authzpolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Authzpolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAuthzPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Authzpolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Authzpolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthzPoliciesResponse>, callback: BodyResponseCallback<Schema$ListAuthzPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Authzpolicies$List, callback: BodyResponseCallback<Schema$ListAuthzPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthzPoliciesResponse>): void;
        /**
         * Updates the parameters of a single AuthzPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Authzpolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Authzpolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Authzpolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Authzpolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Authzpolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Create extends StandardParameters {
        /**
         * Required. User-provided ID of the `AuthzPolicy` resource to be created.
         */
        authzPolicyId?: string;
        /**
         * Required. The parent resource of the `AuthzPolicy` resource. Must be in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthzPolicy;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Delete extends StandardParameters {
        /**
         * Required. The name of the `AuthzPolicy` resource to delete. Must be in the format `projects/{project\}/locations/{location\}/authzPolicies/{authz_policy\}`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Get extends StandardParameters {
        /**
         * Required. A name of the `AuthzPolicy` resource to get. Must be in the format `projects/{project\}/locations/{location\}/authzPolicies/{authz_policy\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$List extends StandardParameters {
        /**
         * Optional. Filtering results.
         */
        filter?: string;
        /**
         * Optional. Hint for how to order the results.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.
         */
        pageSize?: number;
        /**
         * Optional. A token identifying a page of results that the server returns.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the `AuthzPolicy` resources are listed, specified in the following format: `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Patch extends StandardParameters {
        /**
         * Required. Identifier. Name of the `AuthzPolicy` resource in the following format: `projects/{project\}/locations/{location\}/authzPolicies/{authz_policy\}`.
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Used to specify the fields to be overwritten in the `AuthzPolicy` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthzPolicy;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Authzpolicies$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Backendauthenticationconfigs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new BackendAuthenticationConfig in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single BackendAuthenticationConfig to BackendAuthenticationConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single BackendAuthenticationConfig to BackendAuthenticationConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$BackendAuthenticationConfig>>;
        get(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get, options: MethodOptions | BodyResponseCallback<Schema$BackendAuthenticationConfig>, callback: BodyResponseCallback<Schema$BackendAuthenticationConfig>): void;
        get(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get, callback: BodyResponseCallback<Schema$BackendAuthenticationConfig>): void;
        get(callback: BodyResponseCallback<Schema$BackendAuthenticationConfig>): void;
        /**
         * Lists BackendAuthenticationConfigs in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Backendauthenticationconfigs$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListBackendAuthenticationConfigsResponse>>;
        list(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$List, options: MethodOptions | BodyResponseCallback<Schema$ListBackendAuthenticationConfigsResponse>, callback: BodyResponseCallback<Schema$ListBackendAuthenticationConfigsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$List, callback: BodyResponseCallback<Schema$ListBackendAuthenticationConfigsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListBackendAuthenticationConfigsResponse>): void;
        /**
         * Updates the parameters of a single BackendAuthenticationConfig to BackendAuthenticationConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Backendauthenticationconfigs$Create extends StandardParameters {
        /**
         * Required. Short name of the BackendAuthenticationConfig resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "backend-auth-config".
         */
        backendAuthenticationConfigId?: string;
        /**
         * Required. The parent resource of the BackendAuthenticationConfig. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BackendAuthenticationConfig;
    }
    export interface Params$Resource$Projects$Locations$Backendauthenticationconfigs$Delete extends StandardParameters {
        /**
         * Optional. Etag of the resource. If this is provided, it must match the server's etag.
         */
        etag?: string;
        /**
         * Required. A name of the BackendAuthenticationConfig to delete. Must be in the format `projects/x/locations/{location\}/backendAuthenticationConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Backendauthenticationconfigs$Get extends StandardParameters {
        /**
         * Required. A name of the BackendAuthenticationConfig to get. Must be in the format `projects/x/locations/{location\}/backendAuthenticationConfigs/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Backendauthenticationconfigs$List extends StandardParameters {
        /**
         * Maximum number of BackendAuthenticationConfigs to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListBackendAuthenticationConfigsResponse` Indicates that this is a continuation of a prior `ListBackendAuthenticationConfigs` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the BackendAuthenticationConfigs should be listed, specified in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Backendauthenticationconfigs$Patch extends StandardParameters {
        /**
         * Required. Name of the BackendAuthenticationConfig resource. It matches the pattern `projects/x/locations/{location\}/backendAuthenticationConfigs/{backend_authentication_config\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the BackendAuthenticationConfig resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BackendAuthenticationConfig;
    }
    export class Resource$Projects$Locations$Clienttlspolicies {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new ClientTlsPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Clienttlspolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Clienttlspolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Clienttlspolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Clienttlspolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single ClientTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Clienttlspolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Clienttlspolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Clienttlspolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Clienttlspolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single ClientTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Clienttlspolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ClientTlsPolicy>>;
        get(params: Params$Resource$Projects$Locations$Clienttlspolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Clienttlspolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$ClientTlsPolicy>, callback: BodyResponseCallback<Schema$ClientTlsPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Clienttlspolicies$Get, callback: BodyResponseCallback<Schema$ClientTlsPolicy>): void;
        get(callback: BodyResponseCallback<Schema$ClientTlsPolicy>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists ClientTlsPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Clienttlspolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Clienttlspolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListClientTlsPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Clienttlspolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Clienttlspolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListClientTlsPoliciesResponse>, callback: BodyResponseCallback<Schema$ListClientTlsPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Clienttlspolicies$List, callback: BodyResponseCallback<Schema$ListClientTlsPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListClientTlsPoliciesResponse>): void;
        /**
         * Updates the parameters of a single ClientTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Clienttlspolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Clienttlspolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Clienttlspolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Clienttlspolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Create extends StandardParameters {
        /**
         * Required. Short name of the ClientTlsPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "client_mtls_policy".
         */
        clientTlsPolicyId?: string;
        /**
         * Required. The parent resource of the ClientTlsPolicy. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClientTlsPolicy;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Delete extends StandardParameters {
        /**
         * Required. A name of the ClientTlsPolicy to delete. Must be in the format `projects/x/locations/{location\}/clientTlsPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Get extends StandardParameters {
        /**
         * Required. A name of the ClientTlsPolicy to get. Must be in the format `projects/x/locations/{location\}/clientTlsPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$List extends StandardParameters {
        /**
         * Maximum number of ClientTlsPolicies to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListClientTlsPoliciesResponse` Indicates that this is a continuation of a prior `ListClientTlsPolicies` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the ClientTlsPolicies should be listed, specified in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Patch extends StandardParameters {
        /**
         * Required. Name of the ClientTlsPolicy resource. It matches the pattern `projects/x/locations/{location\}/clientTlsPolicies/{client_tls_policy\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the ClientTlsPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ClientTlsPolicy;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Clienttlspolicies$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Firewallendpointassociations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new FirewallEndpointAssociation in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Firewallendpointassociations$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single FirewallEndpointAssociation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Firewallendpointassociations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single FirewallEndpointAssociation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Firewallendpointassociations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FirewallEndpointAssociation>>;
        get(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Get, options: MethodOptions | BodyResponseCallback<Schema$FirewallEndpointAssociation>, callback: BodyResponseCallback<Schema$FirewallEndpointAssociation>): void;
        get(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Get, callback: BodyResponseCallback<Schema$FirewallEndpointAssociation>): void;
        get(callback: BodyResponseCallback<Schema$FirewallEndpointAssociation>): void;
        /**
         * Lists Associations in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Firewallendpointassociations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Firewallendpointassociations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListFirewallEndpointAssociationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Firewallendpointassociations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Firewallendpointassociations$List, options: MethodOptions | BodyResponseCallback<Schema$ListFirewallEndpointAssociationsResponse>, callback: BodyResponseCallback<Schema$ListFirewallEndpointAssociationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Firewallendpointassociations$List, callback: BodyResponseCallback<Schema$ListFirewallEndpointAssociationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListFirewallEndpointAssociationsResponse>): void;
        /**
         * Update a single FirewallEndpointAssociation.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Firewallendpointassociations$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Firewallendpointassociations$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Firewallendpointassociations$Create extends StandardParameters {
        /**
         * Optional. Id of the requesting object. If auto-generating Id server-side, remove this field and firewall_endpoint_association_id from the method_signature of Create RPC.
         */
        firewallEndpointAssociationId?: string;
        /**
         * Required. Value for parent.
         */
        parent?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallEndpointAssociation;
    }
    export interface Params$Resource$Projects$Locations$Firewallendpointassociations$Delete extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Firewallendpointassociations$Get extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Firewallendpointassociations$List extends StandardParameters {
        /**
         * Optional. Filtering results
         */
        filter?: string;
        /**
         * Hint for how to order the results
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.
         */
        pageSize?: number;
        /**
         * A token identifying a page of results the server should return.
         */
        pageToken?: string;
        /**
         * Required. Parent value for ListAssociationsRequest
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Firewallendpointassociations$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. name of resource
         */
        name?: string;
        /**
         * Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
         */
        requestId?: string;
        /**
         * Required. Field mask is used to specify the fields to be overwritten in the Association resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallEndpointAssociation;
    }
    export class Resource$Projects$Locations$Gatewaysecuritypolicies {
        context: APIRequestContext;
        rules: Resource$Projects$Locations$Gatewaysecuritypolicies$Rules;
        constructor(context: APIRequestContext);
        /**
         * Creates a new GatewaySecurityPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single GatewaySecurityPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single GatewaySecurityPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GatewaySecurityPolicy>>;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$GatewaySecurityPolicy>, callback: BodyResponseCallback<Schema$GatewaySecurityPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get, callback: BodyResponseCallback<Schema$GatewaySecurityPolicy>): void;
        get(callback: BodyResponseCallback<Schema$GatewaySecurityPolicy>): void;
        /**
         * Lists GatewaySecurityPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListGatewaySecurityPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListGatewaySecurityPoliciesResponse>, callback: BodyResponseCallback<Schema$ListGatewaySecurityPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List, callback: BodyResponseCallback<Schema$ListGatewaySecurityPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListGatewaySecurityPoliciesResponse>): void;
        /**
         * Updates the parameters of a single GatewaySecurityPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Create extends StandardParameters {
        /**
         * Required. Short name of the GatewaySecurityPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "gateway_security_policy1".
         */
        gatewaySecurityPolicyId?: string;
        /**
         * Required. The parent resource of the GatewaySecurityPolicy. Must be in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GatewaySecurityPolicy;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Delete extends StandardParameters {
        /**
         * Required. A name of the GatewaySecurityPolicy to delete. Must be in the format `projects/{project\}/locations/{location\}/gatewaySecurityPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Get extends StandardParameters {
        /**
         * Required. A name of the GatewaySecurityPolicy to get. Must be in the format `projects/{project\}/locations/{location\}/gatewaySecurityPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$List extends StandardParameters {
        /**
         * Maximum number of GatewaySecurityPolicies to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last 'ListGatewaySecurityPoliciesResponse' Indicates that this is a continuation of a prior 'ListGatewaySecurityPolicies' call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the GatewaySecurityPolicies should be listed, specified in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Patch extends StandardParameters {
        /**
         * Required. Name of the resource. Name is of the form projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gateway_security_policy\} gateway_security_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the GatewaySecurityPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GatewaySecurityPolicy;
    }
    export class Resource$Projects$Locations$Gatewaysecuritypolicies$Rules {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new GatewaySecurityPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single GatewaySecurityPolicyRule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single GatewaySecurityPolicyRule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GatewaySecurityPolicyRule>>;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get, options: MethodOptions | BodyResponseCallback<Schema$GatewaySecurityPolicyRule>, callback: BodyResponseCallback<Schema$GatewaySecurityPolicyRule>): void;
        get(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get, callback: BodyResponseCallback<Schema$GatewaySecurityPolicyRule>): void;
        get(callback: BodyResponseCallback<Schema$GatewaySecurityPolicyRule>): void;
        /**
         * Lists GatewaySecurityPolicyRules in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListGatewaySecurityPolicyRulesResponse>>;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List, options: MethodOptions | BodyResponseCallback<Schema$ListGatewaySecurityPolicyRulesResponse>, callback: BodyResponseCallback<Schema$ListGatewaySecurityPolicyRulesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List, callback: BodyResponseCallback<Schema$ListGatewaySecurityPolicyRulesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListGatewaySecurityPolicyRulesResponse>): void;
        /**
         * Updates the parameters of a single GatewaySecurityPolicyRule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Create extends StandardParameters {
        /**
         * The ID to use for the rule, which will become the final component of the rule's resource name. This value should be 4-63 characters, and valid characters are /a-z-/.
         */
        gatewaySecurityPolicyRuleId?: string;
        /**
         * Required. The parent where this rule will be created. Format : projects/{project\}/location/{location\}/gatewaySecurityPolicies/x
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GatewaySecurityPolicyRule;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Delete extends StandardParameters {
        /**
         * Required. A name of the GatewaySecurityPolicyRule to delete. Must be in the format `projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gatewaySecurityPolicy\}/rules/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Get extends StandardParameters {
        /**
         * Required. The name of the GatewaySecurityPolicyRule to retrieve. Format: projects/{project\}/location/{location\}/gatewaySecurityPolicies/x/rules/x
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$List extends StandardParameters {
        /**
         * Maximum number of GatewaySecurityPolicyRules to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last 'ListGatewaySecurityPolicyRulesResponse' Indicates that this is a continuation of a prior 'ListGatewaySecurityPolicyRules' call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project, location and GatewaySecurityPolicy from which the GatewaySecurityPolicyRules should be listed, specified in the format `projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gatewaySecurityPolicy\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Gatewaysecuritypolicies$Rules$Patch extends StandardParameters {
        /**
         * Required. Immutable. Name of the resource. ame is the full resource name so projects/{project\}/locations/{location\}/gatewaySecurityPolicies/{gateway_security_policy\}/rules/{rule\} rule should match the pattern: (^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the GatewaySecurityPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GatewaySecurityPolicyRule;
    }
    export class Resource$Projects$Locations$Interceptdeploymentgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a deployment group in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a deployment group. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific deployment group. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$InterceptDeploymentGroup>>;
        get(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$InterceptDeploymentGroup>, callback: BodyResponseCallback<Schema$InterceptDeploymentGroup>): void;
        get(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get, callback: BodyResponseCallback<Schema$InterceptDeploymentGroup>): void;
        get(callback: BodyResponseCallback<Schema$InterceptDeploymentGroup>): void;
        /**
         * Lists deployment groups in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Interceptdeploymentgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInterceptDeploymentGroupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListInterceptDeploymentGroupsResponse>, callback: BodyResponseCallback<Schema$ListInterceptDeploymentGroupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$List, callback: BodyResponseCallback<Schema$ListInterceptDeploymentGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInterceptDeploymentGroupsResponse>): void;
        /**
         * Updates a deployment group. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeploymentgroups$Create extends StandardParameters {
        /**
         * Required. The ID to use for the new deployment group, which will become the final component of the deployment group's resource name.
         */
        interceptDeploymentGroupId?: string;
        /**
         * Required. The parent resource where this deployment group will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptDeploymentGroup;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeploymentgroups$Delete extends StandardParameters {
        /**
         * Required. The deployment group to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeploymentgroups$Get extends StandardParameters {
        /**
         * Required. The name of the deployment group to retrieve. Format: projects/{project\}/locations/{location\}/interceptDeploymentGroups/{intercept_deployment_group\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeploymentgroups$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListInterceptDeploymentGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptDeploymentGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of deployment groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeploymentgroups$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the deployment group (e.g. `description`; *not* `intercept_deployment_group.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptDeploymentGroup;
    }
    export class Resource$Projects$Locations$Interceptdeployments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a deployment in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Interceptdeployments$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Interceptdeployments$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Interceptdeployments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Interceptdeployments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Interceptdeployments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a deployment. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Interceptdeployments$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Interceptdeployments$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Interceptdeployments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptdeployments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptdeployments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific deployment. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Interceptdeployments$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Interceptdeployments$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$InterceptDeployment>>;
        get(params: Params$Resource$Projects$Locations$Interceptdeployments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Interceptdeployments$Get, options: MethodOptions | BodyResponseCallback<Schema$InterceptDeployment>, callback: BodyResponseCallback<Schema$InterceptDeployment>): void;
        get(params: Params$Resource$Projects$Locations$Interceptdeployments$Get, callback: BodyResponseCallback<Schema$InterceptDeployment>): void;
        get(callback: BodyResponseCallback<Schema$InterceptDeployment>): void;
        /**
         * Lists deployments in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Interceptdeployments$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Interceptdeployments$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInterceptDeploymentsResponse>>;
        list(params: Params$Resource$Projects$Locations$Interceptdeployments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Interceptdeployments$List, options: MethodOptions | BodyResponseCallback<Schema$ListInterceptDeploymentsResponse>, callback: BodyResponseCallback<Schema$ListInterceptDeploymentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Interceptdeployments$List, callback: BodyResponseCallback<Schema$ListInterceptDeploymentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInterceptDeploymentsResponse>): void;
        /**
         * Updates a deployment. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Interceptdeployments$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Interceptdeployments$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Interceptdeployments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptdeployments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptdeployments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeployments$Create extends StandardParameters {
        /**
         * Required. The ID to use for the new deployment, which will become the final component of the deployment's resource name.
         */
        interceptDeploymentId?: string;
        /**
         * Required. The parent resource where this deployment will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptDeployment;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeployments$Delete extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeployments$Get extends StandardParameters {
        /**
         * Required. The name of the deployment to retrieve. Format: projects/{project\}/locations/{location\}/interceptDeployments/{intercept_deployment\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeployments$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListInterceptDeployments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptDeployments` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of deployments. Example: `projects/123456789/locations/us-central1-a`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptdeployments$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the deployment (e.g. `description`; *not* `intercept_deployment.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptDeployment;
    }
    export class Resource$Projects$Locations$Interceptendpointgroupassociations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an association in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an association. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific association. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$InterceptEndpointGroupAssociation>>;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get, options: MethodOptions | BodyResponseCallback<Schema$InterceptEndpointGroupAssociation>, callback: BodyResponseCallback<Schema$InterceptEndpointGroupAssociation>): void;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get, callback: BodyResponseCallback<Schema$InterceptEndpointGroupAssociation>): void;
        get(callback: BodyResponseCallback<Schema$InterceptEndpointGroupAssociation>): void;
        /**
         * Lists associations in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInterceptEndpointGroupAssociationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List, options: MethodOptions | BodyResponseCallback<Schema$ListInterceptEndpointGroupAssociationsResponse>, callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupAssociationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List, callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupAssociationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupAssociationsResponse>): void;
        /**
         * Updates an association. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Create extends StandardParameters {
        /**
         * Optional. The ID to use for the new association, which will become the final component of the endpoint group's resource name. If not provided, the server will generate a unique ID.
         */
        interceptEndpointGroupAssociationId?: string;
        /**
         * Required. The parent resource where this association will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptEndpointGroupAssociation;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Delete extends StandardParameters {
        /**
         * Required. The association to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Get extends StandardParameters {
        /**
         * Required. The name of the association to retrieve. Format: projects/{project\}/locations/{location\}/interceptEndpointGroupAssociations/{intercept_endpoint_group_association\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroupassociations$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListInterceptEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of associations. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroupassociations$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the association (e.g. `description`; *not* `intercept_endpoint_group_association.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptEndpointGroupAssociation;
    }
    export class Resource$Projects$Locations$Interceptendpointgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an endpoint group in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Interceptendpointgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an endpoint group. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Interceptendpointgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific endpoint group. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Interceptendpointgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$InterceptEndpointGroup>>;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$InterceptEndpointGroup>, callback: BodyResponseCallback<Schema$InterceptEndpointGroup>): void;
        get(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Get, callback: BodyResponseCallback<Schema$InterceptEndpointGroup>): void;
        get(callback: BodyResponseCallback<Schema$InterceptEndpointGroup>): void;
        /**
         * Lists endpoint groups in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Interceptendpointgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInterceptEndpointGroupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListInterceptEndpointGroupsResponse>, callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Interceptendpointgroups$List, callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInterceptEndpointGroupsResponse>): void;
        /**
         * Updates an endpoint group. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Interceptendpointgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Interceptendpointgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroups$Create extends StandardParameters {
        /**
         * Required. The ID to use for the endpoint group, which will become the final component of the endpoint group's resource name.
         */
        interceptEndpointGroupId?: string;
        /**
         * Required. The parent resource where this endpoint group will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptEndpointGroup;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroups$Delete extends StandardParameters {
        /**
         * Required. The endpoint group to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroups$Get extends StandardParameters {
        /**
         * Required. The name of the endpoint group to retrieve. Format: projects/{project\}/locations/{location\}/interceptEndpointGroups/{intercept_endpoint_group\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroups$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListInterceptEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of endpoint groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Interceptendpointgroups$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the endpoint group (e.g. `description`; *not* `intercept_endpoint_group.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$InterceptEndpointGroup;
    }
    export class Resource$Projects$Locations$Mirroringdeploymentgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a deployment group in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a deployment group. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific deployment group. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$MirroringDeploymentGroup>>;
        get(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$MirroringDeploymentGroup>, callback: BodyResponseCallback<Schema$MirroringDeploymentGroup>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get, callback: BodyResponseCallback<Schema$MirroringDeploymentGroup>): void;
        get(callback: BodyResponseCallback<Schema$MirroringDeploymentGroup>): void;
        /**
         * Lists deployment groups in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListMirroringDeploymentGroupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListMirroringDeploymentGroupsResponse>, callback: BodyResponseCallback<Schema$ListMirroringDeploymentGroupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List, callback: BodyResponseCallback<Schema$ListMirroringDeploymentGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListMirroringDeploymentGroupsResponse>): void;
        /**
         * Updates a deployment group. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Create extends StandardParameters {
        /**
         * Required. The ID to use for the new deployment group, which will become the final component of the deployment group's resource name.
         */
        mirroringDeploymentGroupId?: string;
        /**
         * Required. The parent resource where this deployment group will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringDeploymentGroup;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Delete extends StandardParameters {
        /**
         * Required. The deployment group to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Get extends StandardParameters {
        /**
         * Required. The name of the deployment group to retrieve. Format: projects/{project\}/locations/{location\}/mirroringDeploymentGroups/{mirroring_deployment_group\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeploymentgroups$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListMirroringDeploymentGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringDeploymentGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of deployment groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeploymentgroups$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the deployment group (e.g. `description`; *not* `mirroring_deployment_group.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringDeploymentGroup;
    }
    export class Resource$Projects$Locations$Mirroringdeployments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a deployment in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Mirroringdeployments$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Mirroringdeployments$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Mirroringdeployments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringdeployments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringdeployments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a deployment. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Mirroringdeployments$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Mirroringdeployments$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeployments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeployments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringdeployments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific deployment. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Mirroringdeployments$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Mirroringdeployments$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$MirroringDeployment>>;
        get(params: Params$Resource$Projects$Locations$Mirroringdeployments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringdeployments$Get, options: MethodOptions | BodyResponseCallback<Schema$MirroringDeployment>, callback: BodyResponseCallback<Schema$MirroringDeployment>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringdeployments$Get, callback: BodyResponseCallback<Schema$MirroringDeployment>): void;
        get(callback: BodyResponseCallback<Schema$MirroringDeployment>): void;
        /**
         * Lists deployments in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Mirroringdeployments$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Mirroringdeployments$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListMirroringDeploymentsResponse>>;
        list(params: Params$Resource$Projects$Locations$Mirroringdeployments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringdeployments$List, options: MethodOptions | BodyResponseCallback<Schema$ListMirroringDeploymentsResponse>, callback: BodyResponseCallback<Schema$ListMirroringDeploymentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringdeployments$List, callback: BodyResponseCallback<Schema$ListMirroringDeploymentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListMirroringDeploymentsResponse>): void;
        /**
         * Updates a deployment. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Mirroringdeployments$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Mirroringdeployments$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeployments$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeployments$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringdeployments$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeployments$Create extends StandardParameters {
        /**
         * Required. The ID to use for the new deployment, which will become the final component of the deployment's resource name.
         */
        mirroringDeploymentId?: string;
        /**
         * Required. The parent resource where this deployment will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringDeployment;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeployments$Delete extends StandardParameters {
        /**
         * Required. Name of the resource
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeployments$Get extends StandardParameters {
        /**
         * Required. The name of the deployment to retrieve. Format: projects/{project\}/locations/{location\}/mirroringDeployments/{mirroring_deployment\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeployments$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListMirroringDeployments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringDeployments` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of deployments. Example: `projects/123456789/locations/us-central1-a`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringdeployments$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the deployment (e.g. `description`; *not* `mirroring_deployment.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringDeployment;
    }
    export class Resource$Projects$Locations$Mirroringendpointgroupassociations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an association in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an association. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific association. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$MirroringEndpointGroupAssociation>>;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get, options: MethodOptions | BodyResponseCallback<Schema$MirroringEndpointGroupAssociation>, callback: BodyResponseCallback<Schema$MirroringEndpointGroupAssociation>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get, callback: BodyResponseCallback<Schema$MirroringEndpointGroupAssociation>): void;
        get(callback: BodyResponseCallback<Schema$MirroringEndpointGroupAssociation>): void;
        /**
         * Lists associations in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListMirroringEndpointGroupAssociationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List, options: MethodOptions | BodyResponseCallback<Schema$ListMirroringEndpointGroupAssociationsResponse>, callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupAssociationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List, callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupAssociationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupAssociationsResponse>): void;
        /**
         * Updates an association. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Create extends StandardParameters {
        /**
         * Optional. The ID to use for the new association, which will become the final component of the endpoint group's resource name. If not provided, the server will generate a unique ID.
         */
        mirroringEndpointGroupAssociationId?: string;
        /**
         * Required. The parent resource where this association will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringEndpointGroupAssociation;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Delete extends StandardParameters {
        /**
         * Required. The association to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Get extends StandardParameters {
        /**
         * Required. The name of the association to retrieve. Format: projects/{project\}/locations/{location\}/mirroringEndpointGroupAssociations/{mirroring_endpoint_group_association\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListMirroringEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of associations. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroupassociations$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the association (e.g. `description`; *not* `mirroring_endpoint_group_association.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringEndpointGroupAssociation;
    }
    export class Resource$Projects$Locations$Mirroringendpointgroups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates an endpoint group in a given project and location. See https://google.aip.dev/133.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Mirroringendpointgroups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an endpoint group. See https://google.aip.dev/135.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets a specific endpoint group. See https://google.aip.dev/131.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Mirroringendpointgroups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$MirroringEndpointGroup>>;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Get, options: MethodOptions | BodyResponseCallback<Schema$MirroringEndpointGroup>, callback: BodyResponseCallback<Schema$MirroringEndpointGroup>): void;
        get(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Get, callback: BodyResponseCallback<Schema$MirroringEndpointGroup>): void;
        get(callback: BodyResponseCallback<Schema$MirroringEndpointGroup>): void;
        /**
         * Lists endpoint groups in a given project and location. See https://google.aip.dev/132.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Mirroringendpointgroups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListMirroringEndpointGroupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$List, options: MethodOptions | BodyResponseCallback<Schema$ListMirroringEndpointGroupsResponse>, callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$List, callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListMirroringEndpointGroupsResponse>): void;
        /**
         * Updates an endpoint group. See https://google.aip.dev/134.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroups$Create extends StandardParameters {
        /**
         * Required. The ID to use for the endpoint group, which will become the final component of the endpoint group's resource name.
         */
        mirroringEndpointGroupId?: string;
        /**
         * Required. The parent resource where this endpoint group will be created. Format: projects/{project\}/locations/{location\}
         */
        parent?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringEndpointGroup;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroups$Delete extends StandardParameters {
        /**
         * Required. The endpoint group to delete.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroups$Get extends StandardParameters {
        /**
         * Required. The name of the endpoint group to retrieve. Format: projects/{project\}/locations/{location\}/mirroringEndpointGroups/{mirroring_endpoint_group\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroups$List extends StandardParameters {
        /**
         * Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.
         */
        filter?: string;
        /**
         * Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.
         */
        orderBy?: string;
        /**
         * Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListMirroringEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.
         */
        pageToken?: string;
        /**
         * Required. The parent, which owns this collection of endpoint groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Mirroringendpointgroups$Patch extends StandardParameters {
        /**
         * Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.
         */
        name?: string;
        /**
         * Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.
         */
        requestId?: string;
        /**
         * Optional. The list of fields to update. Fields are specified relative to the endpoint group (e.g. `description`; *not* `mirroring_endpoint_group.description`). See https://google.aip.dev/161 for more details.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MirroringEndpointGroup;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Servertlspolicies {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new ServerTlsPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Servertlspolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Servertlspolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Servertlspolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Servertlspolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Servertlspolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single ServerTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Servertlspolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Servertlspolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Servertlspolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Servertlspolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Servertlspolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single ServerTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Servertlspolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Servertlspolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ServerTlsPolicy>>;
        get(params: Params$Resource$Projects$Locations$Servertlspolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Servertlspolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$ServerTlsPolicy>, callback: BodyResponseCallback<Schema$ServerTlsPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Servertlspolicies$Get, callback: BodyResponseCallback<Schema$ServerTlsPolicy>): void;
        get(callback: BodyResponseCallback<Schema$ServerTlsPolicy>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Lists ServerTlsPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Servertlspolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Servertlspolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListServerTlsPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Servertlspolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Servertlspolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListServerTlsPoliciesResponse>, callback: BodyResponseCallback<Schema$ListServerTlsPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Servertlspolicies$List, callback: BodyResponseCallback<Schema$ListServerTlsPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListServerTlsPoliciesResponse>): void;
        /**
         * Updates the parameters of a single ServerTlsPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Servertlspolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Servertlspolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Servertlspolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Servertlspolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Servertlspolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1Policy>, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy, callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$GoogleIamV1Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleIamV1TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions, callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$GoogleIamV1TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Create extends StandardParameters {
        /**
         * Required. The parent resource of the ServerTlsPolicy. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Required. Short name of the ServerTlsPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "server_mtls_policy".
         */
        serverTlsPolicyId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ServerTlsPolicy;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Delete extends StandardParameters {
        /**
         * Required. A name of the ServerTlsPolicy to delete. Must be in the format `projects/x/locations/{location\}/serverTlsPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Get extends StandardParameters {
        /**
         * Required. A name of the ServerTlsPolicy to get. Must be in the format `projects/x/locations/{location\}/serverTlsPolicies/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$List extends StandardParameters {
        /**
         * Maximum number of ServerTlsPolicies to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListServerTlsPoliciesResponse` Indicates that this is a continuation of a prior `ListServerTlsPolicies` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the ServerTlsPolicies should be listed, specified in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Optional. Setting this field to `true` will opt the request into returning the resources that are reachable, and into including the names of those that were unreachable in the [ListServerTlsPoliciesResponse.unreachable] field. This can only be `true` when reading across collections e.g. when `parent` is set to `"projects/example/locations/-"`.
         */
        returnPartialSuccess?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Patch extends StandardParameters {
        /**
         * Required. Name of the ServerTlsPolicy resource. It matches the pattern `projects/x/locations/{location\}/serverTlsPolicies/{server_tls_policy\}`
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the ServerTlsPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ServerTlsPolicy;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Servertlspolicies$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleIamV1TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Tlsinspectionpolicies {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new TlsInspectionPolicy in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single TlsInspectionPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single TlsInspectionPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TlsInspectionPolicy>>;
        get(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get, options: MethodOptions | BodyResponseCallback<Schema$TlsInspectionPolicy>, callback: BodyResponseCallback<Schema$TlsInspectionPolicy>): void;
        get(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get, callback: BodyResponseCallback<Schema$TlsInspectionPolicy>): void;
        get(callback: BodyResponseCallback<Schema$TlsInspectionPolicy>): void;
        /**
         * Lists TlsInspectionPolicies in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Tlsinspectionpolicies$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListTlsInspectionPoliciesResponse>>;
        list(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$List, options: MethodOptions | BodyResponseCallback<Schema$ListTlsInspectionPoliciesResponse>, callback: BodyResponseCallback<Schema$ListTlsInspectionPoliciesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$List, callback: BodyResponseCallback<Schema$ListTlsInspectionPoliciesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTlsInspectionPoliciesResponse>): void;
        /**
         * Updates the parameters of a single TlsInspectionPolicy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Tlsinspectionpolicies$Create extends StandardParameters {
        /**
         * Required. The parent resource of the TlsInspectionPolicy. Must be in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
        /**
         * Required. Short name of the TlsInspectionPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "tls_inspection_policy1".
         */
        tlsInspectionPolicyId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TlsInspectionPolicy;
    }
    export interface Params$Resource$Projects$Locations$Tlsinspectionpolicies$Delete extends StandardParameters {
        /**
         * If set to true, any rules for this TlsInspectionPolicy will also be deleted. (Otherwise, the request will only work if the TlsInspectionPolicy has no rules.)
         */
        force?: boolean;
        /**
         * Required. A name of the TlsInspectionPolicy to delete. Must be in the format `projects/{project\}/locations/{location\}/tlsInspectionPolicies/{tls_inspection_policy\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Tlsinspectionpolicies$Get extends StandardParameters {
        /**
         * Required. A name of the TlsInspectionPolicy to get. Must be in the format `projects/{project\}/locations/{location\}/tlsInspectionPolicies/{tls_inspection_policy\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Tlsinspectionpolicies$List extends StandardParameters {
        /**
         * Maximum number of TlsInspectionPolicies to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last 'ListTlsInspectionPoliciesResponse' Indicates that this is a continuation of a prior 'ListTlsInspectionPolicies' call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the TlsInspectionPolicies should be listed, specified in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Tlsinspectionpolicies$Patch extends StandardParameters {
        /**
         * Required. Name of the resource. Name is of the form projects/{project\}/locations/{location\}/tlsInspectionPolicies/{tls_inspection_policy\} tls_inspection_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the TlsInspectionPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TlsInspectionPolicy;
    }
    export class Resource$Projects$Locations$Urllists {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new UrlList in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Urllists$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Urllists$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Urllists$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Urllists$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Urllists$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single UrlList.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Urllists$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Urllists$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Urllists$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Urllists$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Urllists$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single UrlList.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Urllists$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Urllists$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$UrlList>>;
        get(params: Params$Resource$Projects$Locations$Urllists$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Urllists$Get, options: MethodOptions | BodyResponseCallback<Schema$UrlList>, callback: BodyResponseCallback<Schema$UrlList>): void;
        get(params: Params$Resource$Projects$Locations$Urllists$Get, callback: BodyResponseCallback<Schema$UrlList>): void;
        get(callback: BodyResponseCallback<Schema$UrlList>): void;
        /**
         * Lists UrlLists in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Urllists$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Urllists$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListUrlListsResponse>>;
        list(params: Params$Resource$Projects$Locations$Urllists$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Urllists$List, options: MethodOptions | BodyResponseCallback<Schema$ListUrlListsResponse>, callback: BodyResponseCallback<Schema$ListUrlListsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Urllists$List, callback: BodyResponseCallback<Schema$ListUrlListsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListUrlListsResponse>): void;
        /**
         * Updates the parameters of a single UrlList.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Urllists$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Urllists$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Urllists$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Urllists$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Urllists$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Urllists$Create extends StandardParameters {
        /**
         * Required. The parent resource of the UrlList. Must be in the format `projects/x/locations/{location\}`.
         */
        parent?: string;
        /**
         * Required. Short name of the UrlList resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. "url_list".
         */
        urlListId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UrlList;
    }
    export interface Params$Resource$Projects$Locations$Urllists$Delete extends StandardParameters {
        /**
         * Required. A name of the UrlList to delete. Must be in the format `projects/x/locations/{location\}/urlLists/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Urllists$Get extends StandardParameters {
        /**
         * Required. A name of the UrlList to get. Must be in the format `projects/x/locations/{location\}/urlLists/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Urllists$List extends StandardParameters {
        /**
         * Maximum number of UrlLists to return per call.
         */
        pageSize?: number;
        /**
         * The value returned by the last `ListUrlListsResponse` Indicates that this is a continuation of a prior `ListUrlLists` call, and that the system should return the next page of data.
         */
        pageToken?: string;
        /**
         * Required. The project and location from which the UrlLists should be listed, specified in the format `projects/{project\}/locations/{location\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Urllists$Patch extends StandardParameters {
        /**
         * Required. Name of the resource provided by the user. Name is of the form projects/{project\}/locations/{location\}/urlLists/{url_list\} url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61\}[a-z0-9])?$).
         */
        name?: string;
        /**
         * Optional. Field mask is used to specify the fields to be overwritten in the UrlList resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UrlList;
    }
    export {};
}
