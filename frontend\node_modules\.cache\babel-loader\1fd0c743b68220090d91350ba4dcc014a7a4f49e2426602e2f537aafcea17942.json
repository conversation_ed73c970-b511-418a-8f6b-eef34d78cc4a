{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return {\n        success: true,\n        user: response.data.data.user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Password change failed';\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "logout", "login", "email", "password", "post", "setItem", "success", "_error$response", "_error$response$data", "message", "register", "userData", "_error$response2", "_error$response2$data", "removeItem", "updateProfile", "profileData", "put", "_error$response3", "_error$response3$data", "changePassword", "currentPassword", "newPassword", "_error$response4", "_error$response4$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n\n      const { user, token } = response.data.data;\n      \n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      \n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      return { success: false, message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', userData);\n      \n      const { user, token } = response.data.data;\n      \n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      \n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      return { success: false, message };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/auth/profile', profileData);\n      setUser(response.data.data.user);\n      return { success: true, user: response.data.data.user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      return { success: false, message };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('http://localhost:5000/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      return { success: false, message };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACF,MAAMQ,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,mCAAmC,CAAC;UACrEZ,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;QAClC,CAAC,CAAC,OAAOe,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CE,MAAM,CAAC,CAAC;QACV;MACF;MACAd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EAEX,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMvB,KAAK,CAACgC,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEpB,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1Cb,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAElB,KAAK,CAAC;MAEpC,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEvB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAT,KAAK,CAACH,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc;MAC/D,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMvB,KAAK,CAACgC,IAAI,CAAC,yCAAyC,EAAEO,QAAQ,CAAC;MAEtF,MAAM;QAAE5B,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1Cb,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAElB,KAAK,CAAC;MAEpC,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEvB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,OAAO,GAAG,EAAAG,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MACtE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMT,MAAM,GAAGA,CAAA,KAAM;IACnBhB,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO1C,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAMsB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMvB,KAAK,CAAC6C,GAAG,CAAC,wCAAwC,EAAED,WAAW,CAAC;MACvFhC,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;MAChC,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEY,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd;MAAK,CAAC;IACzD,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACd,MAAMV,OAAO,GAAG,EAAAS,gBAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,uBAAuB;MACxE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMW,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMlD,KAAK,CAAC6C,GAAG,CAAC,gDAAgD,EAAE;QAChEI,eAAe;QACfC;MACF,CAAC,CAAC;MACF,OAAO;QAAEhB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAyB,gBAAA,EAAAC,qBAAA;MACd,MAAMf,OAAO,GAAG,EAAAc,gBAAA,GAAAzB,KAAK,CAACH,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,wBAAwB;MACzE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMgB,KAAK,GAAG;IACZ1C,IAAI;IACJI,KAAK;IACLF,OAAO;IACPgB,KAAK;IACLS,QAAQ;IACRV,MAAM;IACNe,aAAa;IACbK,cAAc;IACdM,eAAe,EAAE,CAAC,CAAC3C;EACrB,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACoD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA5C,QAAA,EAChCA;EAAQ;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACjD,GAAA,CArHWF,YAAY;AAAAoD,EAAA,GAAZpD,YAAY;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}