import React, { useState, useEffect, useRef } from 'react';
import { useHealthData } from '../contexts/HealthDataContext';

const Chatbot = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const messagesEndRef = useRef(null);
  const { chatWithBot } = useHealthData();

  useEffect(() => {
    // Initial welcome message
    setMessages([
      {
        id: 1,
        text: "Hello! I'm your health assistant. I can help you with questions about fitness, nutrition, sleep, and understanding your health data. How can I assist you today?",
        sender: 'bot',
        timestamp: new Date()
      }
    ]);

    // Load initial suggestions
    setSuggestions([
      'How can I improve my sleep quality?',
      'What\'s a healthy heart rate?',
      'How many steps should I take daily?',
      'Tips for staying motivated to exercise',
      'How does stress affect my health?',
      'What should I know about my BMI?'
    ]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: messageText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate typing delay
    setTimeout(async () => {
      const result = await chatWithBot(messageText);
      
      const botMessage = {
        id: Date.now() + 1,
        text: result.success ? result.data.botResponse : "I'm sorry, I'm having trouble right now. Please try again.",
        sender: 'bot',
        timestamp: new Date(),
        suggestions: result.success ? result.data.suggestions : []
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);

      // Update suggestions if provided
      if (result.success && result.data.suggestions) {
        setSuggestions(result.data.suggestions);
      }
    }, 1000);
  };

  const handleSuggestionClick = (suggestion) => {
    handleSendMessage(suggestion);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="chatbot-page">
      <div className="chat-container">
        <div className="chat-header">
          <div className="bot-info">
            <div className="bot-avatar">🤖</div>
            <div className="bot-details">
              <h2>Health Assistant</h2>
              <p className="bot-status">
                <span className="status-dot"></span>
                Online - Ready to help
              </p>
            </div>
          </div>
        </div>

        <div className="chat-messages">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`message ${message.sender === 'user' ? 'user-message' : 'bot-message'}`}
            >
              <div className="message-content">
                <div className="message-text">{message.text}</div>
                <div className="message-time">{formatTime(message.timestamp)}</div>
              </div>
            </div>
          ))}

          {isTyping && (
            <div className="message bot-message">
              <div className="message-content">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        <div className="chat-input-section">
          {suggestions.length > 0 && (
            <div className="suggestions">
              <p className="suggestions-title">Suggested questions:</p>
              <div className="suggestions-list">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    className="suggestion-chip"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="chat-input">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me about your health, fitness, or wellness..."
              className="message-input"
              rows="1"
            />
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputMessage.trim() || isTyping}
              className="send-button"
            >
              <span className="send-icon">📤</span>
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .chatbot-page {
          height: calc(100vh - 140px);
          display: flex;
          justify-content: center;
          padding: 0;
        }

        .chat-container {
          width: 100%;
          max-width: 800px;
          background: rgba(255, 255, 255, 0.95);
          border-radius: 15px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          display: flex;
          flex-direction: column;
          height: 100%;
        }

        .chat-header {
          padding: 20px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 15px 15px 0 0;
          color: white;
        }

        .bot-info {
          display: flex;
          align-items: center;
        }

        .bot-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          margin-right: 15px;
        }

        .bot-details h2 {
          margin: 0 0 5px 0;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .bot-status {
          margin: 0;
          font-size: 0.9rem;
          opacity: 0.9;
          display: flex;
          align-items: center;
        }

        .status-dot {
          width: 8px;
          height: 8px;
          background: #27ae60;
          border-radius: 50%;
          margin-right: 8px;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        .chat-messages {
          flex: 1;
          padding: 20px;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .message {
          display: flex;
          max-width: 80%;
        }

        .user-message {
          align-self: flex-end;
        }

        .bot-message {
          align-self: flex-start;
        }

        .message-content {
          background: #f8f9fa;
          padding: 12px 16px;
          border-radius: 18px;
          position: relative;
        }

        .user-message .message-content {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-bottom-right-radius: 4px;
        }

        .bot-message .message-content {
          background: #f8f9fa;
          color: #2c3e50;
          border-bottom-left-radius: 4px;
        }

        .message-text {
          font-size: 0.95rem;
          line-height: 1.4;
          margin-bottom: 5px;
        }

        .message-time {
          font-size: 0.75rem;
          opacity: 0.7;
          text-align: right;
        }

        .typing-indicator {
          display: flex;
          gap: 4px;
          padding: 8px 0;
        }

        .typing-indicator span {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #667eea;
          animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
          0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
          40% { transform: scale(1); opacity: 1; }
        }

        .chat-input-section {
          padding: 20px;
          border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .suggestions {
          margin-bottom: 15px;
        }

        .suggestions-title {
          margin: 0 0 10px 0;
          font-size: 0.9rem;
          color: #7f8c8d;
          font-weight: 500;
        }

        .suggestions-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .suggestion-chip {
          background: rgba(102, 126, 234, 0.1);
          border: 1px solid rgba(102, 126, 234, 0.2);
          border-radius: 20px;
          padding: 6px 12px;
          font-size: 0.85rem;
          color: #667eea;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .suggestion-chip:hover {
          background: rgba(102, 126, 234, 0.2);
          transform: translateY(-1px);
        }

        .chat-input {
          display: flex;
          gap: 10px;
          align-items: flex-end;
        }

        .message-input {
          flex: 1;
          border: 2px solid #ecf0f1;
          border-radius: 20px;
          padding: 12px 16px;
          font-size: 0.95rem;
          resize: none;
          max-height: 100px;
          background: rgba(255, 255, 255, 0.9);
          transition: border-color 0.3s ease;
        }

        .message-input:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-button {
          width: 45px;
          height: 45px;
          border: none;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
        }

        .send-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .send-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .send-icon {
          font-size: 1.1rem;
        }

        @media (max-width: 768px) {
          .chatbot-page {
            height: calc(100vh - 120px);
            padding: 0;
          }

          .chat-container {
            border-radius: 0;
            height: 100%;
          }

          .chat-header {
            border-radius: 0;
            padding: 15px;
          }

          .bot-avatar {
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
          }

          .bot-details h2 {
            font-size: 1.1rem;
          }

          .message {
            max-width: 90%;
          }

          .suggestions-list {
            flex-direction: column;
          }

          .suggestion-chip {
            text-align: left;
          }
        }
      `}</style>
    </div>
  );
};

export default Chatbot;
