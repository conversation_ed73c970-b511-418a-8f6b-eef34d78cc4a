import React from 'react';

const HealthInsights = ({ insights = [], prediction = null }) => {
  const getInsightIcon = (type) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'suggestion':
        return '💡';
      default:
        return '📊';
    }
  };

  const getInsightColor = (type) => {
    switch (type) {
      case 'success':
        return '#27ae60';
      case 'warning':
        return '#f39c12';
      case 'info':
        return '#667eea';
      case 'suggestion':
        return '#9b59b6';
      default:
        return '#2c3e50';
    }
  };

  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'very_low':
      case 'low':
        return '#27ae60';
      case 'moderate':
        return '#f39c12';
      case 'high':
      case 'very_high':
        return '#e74c3c';
      default:
        return '#667eea';
    }
  };

  return (
    <div className="health-insights">
      {/* AI Prediction Summary */}
      {prediction && (
        <div className="prediction-summary">
          <div className="prediction-header">
            <h4>🔮 AI Health Assessment</h4>
            <span 
              className="risk-badge"
              style={{ 
                backgroundColor: getRiskColor(prediction.overallRisk),
                color: 'white'
              }}
            >
              {prediction.overallRisk?.replace('_', ' ').toUpperCase() || 'UNKNOWN'} RISK
            </span>
          </div>
          
          {prediction.recommendations && prediction.recommendations.length > 0 && (
            <div className="prediction-recommendations">
              <h5>Priority Recommendations:</h5>
              <ul>
                {prediction.recommendations.slice(0, 3).map((rec, index) => (
                  <li key={index}>
                    <strong>{rec.title}:</strong> {rec.description}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="prediction-date">
            Last updated: {new Date(prediction.createdAt).toLocaleDateString()}
          </div>
        </div>
      )}

      {/* Daily Insights */}
      <div className="insights-list">
        <h4>📈 Today's Insights</h4>
        
        {insights.length === 0 ? (
          <div className="no-insights">
            <p>Great job! No health concerns detected today.</p>
            <p>Keep up the good work with your healthy habits! 🌟</p>
          </div>
        ) : (
          insights.map((insight, index) => (
            <div 
              key={index}
              className="insight-item"
              style={{ borderLeftColor: getInsightColor(insight.type) }}
            >
              <div className="insight-header">
                <span className="insight-icon">
                  {getInsightIcon(insight.type)}
                </span>
                <h5 className="insight-title">{insight.title}</h5>
              </div>
              
              <p className="insight-message">{insight.message}</p>
              
              {insight.action && (
                <div className="insight-action">
                  <strong>Recommended Action:</strong> {insight.action}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Health Tips */}
      <div className="health-tips">
        <h4>💡 Health Tips</h4>
        <div className="tips-grid">
          <div className="tip-item">
            <span className="tip-icon">💧</span>
            <div className="tip-content">
              <h6>Stay Hydrated</h6>
              <p>Aim for 8 glasses of water daily</p>
            </div>
          </div>
          
          <div className="tip-item">
            <span className="tip-icon">🚶</span>
            <div className="tip-content">
              <h6>Move Regularly</h6>
              <p>Take breaks every hour to walk</p>
            </div>
          </div>
          
          <div className="tip-item">
            <span className="tip-icon">😴</span>
            <div className="tip-content">
              <h6>Quality Sleep</h6>
              <p>Maintain consistent sleep schedule</p>
            </div>
          </div>
          
          <div className="tip-item">
            <span className="tip-icon">🧘</span>
            <div className="tip-content">
              <h6>Manage Stress</h6>
              <p>Practice mindfulness daily</p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .health-insights {
          display: flex;
          flex-direction: column;
          gap: 25px;
        }

        .prediction-summary {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
          border: 1px solid rgba(102, 126, 234, 0.2);
          border-radius: 12px;
          padding: 20px;
        }

        .prediction-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .prediction-header h4 {
          margin: 0;
          color: #2c3e50;
          font-size: 1.1rem;
        }

        .risk-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 700;
          letter-spacing: 0.5px;
        }

        .prediction-recommendations {
          margin-bottom: 15px;
        }

        .prediction-recommendations h5 {
          margin: 0 0 10px 0;
          color: #2c3e50;
          font-size: 0.95rem;
        }

        .prediction-recommendations ul {
          margin: 0;
          padding-left: 20px;
        }

        .prediction-recommendations li {
          margin-bottom: 8px;
          color: #2c3e50;
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .prediction-date {
          font-size: 0.8rem;
          color: #7f8c8d;
          font-style: italic;
        }

        .insights-list h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 1.1rem;
        }

        .no-insights {
          text-align: center;
          padding: 20px;
          background: rgba(39, 174, 96, 0.1);
          border: 1px solid rgba(39, 174, 96, 0.2);
          border-radius: 8px;
          color: #27ae60;
        }

        .no-insights p {
          margin: 5px 0;
        }

        .insight-item {
          background: rgba(255, 255, 255, 0.8);
          border-left: 4px solid;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 12px;
          transition: all 0.3s ease;
        }

        .insight-item:hover {
          background: rgba(255, 255, 255, 0.95);
          transform: translateX(2px);
        }

        .insight-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
        }

        .insight-icon {
          font-size: 1.2rem;
          margin-right: 10px;
        }

        .insight-title {
          margin: 0;
          color: #2c3e50;
          font-size: 1rem;
          font-weight: 600;
        }

        .insight-message {
          margin: 0 0 8px 0;
          color: #2c3e50;
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .insight-action {
          font-size: 0.85rem;
          color: #667eea;
          background: rgba(102, 126, 234, 0.1);
          padding: 8px 12px;
          border-radius: 6px;
          margin-top: 8px;
        }

        .health-tips h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 1.1rem;
        }

        .tips-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .tip-item {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.8);
          border: 1px solid rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          padding: 12px;
          transition: all 0.3s ease;
        }

        .tip-item:hover {
          background: rgba(255, 255, 255, 0.95);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .tip-icon {
          font-size: 1.5rem;
          margin-right: 12px;
        }

        .tip-content h6 {
          margin: 0 0 4px 0;
          color: #2c3e50;
          font-size: 0.9rem;
          font-weight: 600;
        }

        .tip-content p {
          margin: 0;
          color: #7f8c8d;
          font-size: 0.8rem;
        }

        @media (max-width: 768px) {
          .prediction-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
          }

          .tips-grid {
            grid-template-columns: 1fr;
          }

          .tip-item {
            flex-direction: column;
            text-align: center;
          }

          .tip-icon {
            margin-right: 0;
            margin-bottom: 8px;
          }
        }
      `}</style>
    </div>
  );
};

export default HealthInsights;
