{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\mini_project\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { HealthDataProvider } from './contexts/HealthDataContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Navbar from './components/Navbar';\nimport ApiTest from './components/ApiTest';\nimport Dashboard from './pages/Dashboard';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Profile from './pages/Profile';\nimport HealthData from './pages/HealthData';\nimport Goals from './pages/Goals';\nimport Predictions from './pages/Predictions';\nimport Chatbot from './pages/Chatbot';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(HealthDataProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: \"main-content\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/register\",\n                element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/api-test\",\n                element: /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/profile\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 37,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/health-data\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(HealthData, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/goals\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Goals, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/predictions\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Predictions, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/chatbot\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Chatbot, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "HealthDataProvider", "ProtectedRoute", "<PERSON><PERSON><PERSON>", "ApiTest", "Dashboard", "<PERSON><PERSON>", "Register", "Profile", "HealthData", "Goals", "Predictions", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/mini_project/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { HealthDataProvider } from './contexts/HealthDataContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Navbar from './components/Navbar';\nimport ApiTest from './components/ApiTest';\nimport Dashboard from './pages/Dashboard';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Profile from './pages/Profile';\nimport HealthData from './pages/HealthData';\nimport Goals from './pages/Goals';\nimport Predictions from './pages/Predictions';\nimport Chatbot from './pages/Chatbot';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <HealthDataProvider>\n        <Router>\n          <div className=\"App\">\n            <Navbar />\n            <main className=\"main-content\">\n              <Routes>\n                <Route path=\"/login\" element={<Login />} />\n                <Route path=\"/register\" element={<Register />} />\n                <Route path=\"/api-test\" element={<ApiTest />} />\n                <Route path=\"/\" element={\n                  <ProtectedRoute>\n                    <Dashboard />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/profile\" element={\n                  <ProtectedRoute>\n                    <Profile />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/health-data\" element={\n                  <ProtectedRoute>\n                    <HealthData />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/goals\" element={\n                  <ProtectedRoute>\n                    <Goals />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/predictions\" element={\n                  <ProtectedRoute>\n                    <Predictions />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"/chatbot\" element={\n                  <ProtectedRoute>\n                    <Chatbot />\n                  </ProtectedRoute>\n                } />\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            </main>\n          </div>\n        </Router>\n      </HealthDataProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACd,YAAY;IAAAgB,QAAA,eACXF,OAAA,CAACb,kBAAkB;MAAAe,QAAA,eACjBF,OAAA,CAAClB,MAAM;QAAAoB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBF,OAAA,CAACX,MAAM;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA;YAAMG,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC5BF,OAAA,CAACjB,MAAM;cAAAmB,QAAA,gBACLF,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACR,KAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACP,QAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACV,OAAO;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,GAAG;gBAACC,OAAO,eACrBT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACT,SAAS;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAC5BT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACN,OAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,cAAc;gBAACC,OAAO,eAChCT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACL,UAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAC1BT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACJ,KAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,cAAc;gBAACC,OAAO,eAChCT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACH,WAAW;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAC5BT,OAAA,CAACZ,cAAc;kBAAAc,QAAA,eACbF,OAAA,CAACF,OAAO;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAChB,KAAK;gBAACwB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACf,QAAQ;kBAACyB,EAAE,EAAC,GAAG;kBAACC,OAAO;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEnB;AAACK,EAAA,GAlDQX,GAAG;AAoDZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}