/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { clouddeploy_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof clouddeploy_v1.Clouddeploy;
};
export declare function clouddeploy(version: 'v1'): clouddeploy_v1.Clouddeploy;
export declare function clouddeploy(options: clouddeploy_v1.Options): clouddeploy_v1.Clouddeploy;
declare const auth: AuthPlus;
export { auth };
export { clouddeploy_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
