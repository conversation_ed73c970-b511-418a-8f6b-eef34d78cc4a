const express = require('express');
const mongoose = require('mongoose');
const { PythonShell } = require('python-shell');
const path = require('path');
const { protect } = require('../middleware/auth');
const Prediction = require('../models/Prediction');
const HealthData = require('../models/HealthData');
const User = require('../models/User');
const router = express.Router();

// @desc    Generate health predictions
// @route   POST /api/predictions/generate
// @access  Private
router.post('/generate', protect, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { predictionType = 'comprehensive', timeframe = 7 } = req.body;
    const userId = req.user.id;

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get recent health data
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframe);

    const healthData = await HealthData.find({
      userId: userId,
      date: { $gte: startDate, $lte: endDate }
    }).sort({ date: -1 }).lean();

    if (healthData.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient health data for prediction. Please sync your device or add health data manually.'
      });
    }

    // Prepare data for ML model
    const inputData = {
      healthData: healthData,
      userData: {
        age: user.age,
        gender: user.gender,
        bmi: user.bmi,
        height: user.height,
        weight: user.weight,
        activityLevel: user.activityLevel,
        medicalConditions: user.medicalConditions || []
      },
      predictionType: predictionType
    };

    // Create prediction record
    const prediction = new Prediction({
      userId: userId,
      predictionType: predictionType,
      inputData: {
        heartRate: {
          resting: healthData[0]?.heartRate?.resting,
          average: healthData[0]?.heartRate?.average,
          variability: calculateHRV(healthData)
        },
        activity: {
          steps: calculateAverage(healthData, 'steps'),
          activeMinutes: calculateAverage(healthData, 'activeMinutes'),
          caloriesBurned: calculateAverage(healthData, 'caloriesBurned')
        },
        sleep: {
          duration: calculateAverage(healthData, 'sleep.totalSleep'),
          efficiency: calculateAverage(healthData, 'sleep.sleepEfficiency'),
          deepSleepPercentage: calculateDeepSleepPercentage(healthData)
        },
        vitals: {
          bloodPressure: healthData[0]?.vitals?.bloodPressure,
          bloodOxygen: healthData[0]?.vitals?.bloodOxygen,
          weight: user.weight,
          bmi: user.bmi
        },
        lifestyle: {
          stressLevel: calculateAverage(healthData, 'stress.level'),
          mood: calculateAverage(healthData, 'mood'),
          energyLevel: calculateAverage(healthData, 'energyLevel'),
          waterIntake: calculateAverage(healthData, 'waterIntake')
        },
        demographics: {
          age: user.age,
          gender: user.gender,
          activityLevel: user.activityLevel
        },
        timeframe: {
          startDate: startDate,
          endDate: endDate,
          dataPoints: healthData.length
        }
      },
      status: 'processing'
    });

    await prediction.save();

    // Run ML prediction
    try {
      const options = {
        mode: 'text',
        pythonPath: 'python',
        pythonOptions: ['-u'],
        scriptPath: path.join(__dirname, '../../ml-model'),
        args: [JSON.stringify(inputData)]
      };

      const results = await new Promise((resolve, reject) => {
        PythonShell.run('health_predictor.py', options, (err, results) => {
          if (err) {
            console.error('Python script error:', err);
            reject(err);
          } else {
            try {
              const result = JSON.parse(results[0]);
              resolve(result);
            } catch (parseErr) {
              console.error('JSON parse error:', parseErr);
              reject(parseErr);
            }
          }
        });
      });

      if (results.error) {
        throw new Error(results.error);
      }

      // Update prediction with results
      prediction.predictions = results.predictions || [];
      prediction.recommendations = results.recommendations || [];
      prediction.modelInfo = results.model_info || {};
      prediction.status = 'completed';
      prediction.processingTime = Date.now() - startTime;

      await prediction.save();

      // Emit real-time update
      const io = req.app.get('io');
      io.to(`user-${userId}`).emit('predictionUpdate', {
        type: 'completed',
        data: prediction
      });

      res.status(201).json({
        success: true,
        message: 'Health prediction generated successfully',
        data: { prediction }
      });

    } catch (mlError) {
      console.error('ML prediction error:', mlError);
      
      // Update prediction with error
      prediction.status = 'failed';
      prediction.error = {
        message: mlError.message,
        code: 'ML_PREDICTION_FAILED',
        timestamp: new Date()
      };
      prediction.processingTime = Date.now() - startTime;
      
      await prediction.save();

      res.status(500).json({
        success: false,
        message: 'Failed to generate prediction',
        error: process.env.NODE_ENV === 'development' ? mlError.message : 'Prediction service unavailable'
      });
    }

  } catch (error) {
    console.error('Generate prediction error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during prediction generation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get user's predictions
// @route   GET /api/predictions
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const { limit = 10, page = 1, type, status } = req.query;
    const userId = req.user.id;

    let query = { userId, isArchived: false };
    if (type) query.predictionType = type;
    if (status) query.status = status;

    const skip = (page - 1) * limit;
    
    const predictions = await Prediction.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip)
      .populate('userId', 'name email');

    const total = await Prediction.countDocuments(query);

    res.json({
      success: true,
      data: {
        predictions,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          hasNext: skip + predictions.length < total,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get latest predictions
// @route   GET /api/predictions/latest
// @access  Private
router.get('/latest', protect, async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    const predictions = await Prediction.getLatestPredictions(req.user.id, parseInt(limit));

    res.json({
      success: true,
      data: { predictions }
    });
  } catch (error) {
    console.error('Get latest predictions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get prediction statistics
// @route   GET /api/predictions/stats
// @access  Private
router.get('/stats', protect, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const stats = await Prediction.getPredictionStats(req.user.id, parseInt(days));

    res.json({
      success: true,
      data: { stats }
    });
  } catch (error) {
    console.error('Get prediction stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Add feedback to prediction
// @route   POST /api/predictions/:id/feedback
// @access  Private
router.post('/:id/feedback', protect, async (req, res) => {
  try {
    const { accuracy, usefulness, comments } = req.body;

    if (!accuracy || !usefulness) {
      return res.status(400).json({
        success: false,
        message: 'Please provide accuracy and usefulness ratings'
      });
    }

    const prediction = await Prediction.findById(req.params.id);

    if (!prediction) {
      return res.status(404).json({
        success: false,
        message: 'Prediction not found'
      });
    }

    // Check ownership
    if (prediction.userId.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    await prediction.addFeedback(accuracy, usefulness, comments);

    res.json({
      success: true,
      message: 'Feedback added successfully',
      data: { prediction }
    });
  } catch (error) {
    console.error('Add feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Helper functions
function calculateAverage(data, field) {
  const values = data.map(item => {
    const keys = field.split('.');
    let value = item;
    for (const key of keys) {
      value = value?.[key];
    }
    return value;
  }).filter(val => val != null && !isNaN(val));
  
  return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
}

function calculateHRV(data) {
  // Simple HRV calculation based on resting heart rate variability
  const restingHRs = data.map(item => item.heartRate?.resting).filter(hr => hr);
  if (restingHRs.length < 2) return 0;
  
  const mean = restingHRs.reduce((sum, hr) => sum + hr, 0) / restingHRs.length;
  const variance = restingHRs.reduce((sum, hr) => sum + Math.pow(hr - mean, 2), 0) / restingHRs.length;
  return Math.sqrt(variance);
}

function calculateDeepSleepPercentage(data) {
  const sleepData = data.map(item => item.sleep).filter(sleep => sleep?.totalSleep && sleep?.deepSleep);
  if (sleepData.length === 0) return 0;
  
  const totalDeepSleep = sleepData.reduce((sum, sleep) => sum + sleep.deepSleep, 0);
  const totalSleep = sleepData.reduce((sum, sleep) => sum + sleep.totalSleep, 0);
  
  return totalSleep > 0 ? (totalDeepSleep / totalSleep) * 100 : 0;
}

module.exports = router;
