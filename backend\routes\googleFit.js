const express = require('express');
const { protect } = require('../middleware/auth');
const GoogleFitService = require('../services/googleFitService');
const User = require('../models/User');
const HealthData = require('../models/HealthData');
const router = express.Router();

const googleFitService = new GoogleFitService();

// @desc    Get Google Fit authorization URL
// @route   GET /api/google-fit/auth-url
// @access  Private
router.get('/auth-url', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const authUrl = googleFitService.getAuthUrl(userId);
    
    res.json({
      success: true,
      data: {
        authUrl,
        message: 'Visit this URL to authorize Google Fit access'
      }
    });
  } catch (error) {
    console.error('Google Fit auth URL error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate authorization URL',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Handle Google Fit OAuth callback
// @route   POST /api/google-fit/callback
// @access  Private
router.post('/callback', protect, async (req, res) => {
  try {
    const { code } = req.body;
    const userId = req.user.id;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'Authorization code is required'
      });
    }
    
    // Exchange code for tokens
    const tokens = await googleFitService.getTokens(code);
    
    // Save tokens to user profile
    await User.findByIdAndUpdate(userId, {
      'integrations.googleFit': {
        connected: true,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiryDate: tokens.expiry_date,
        connectedAt: new Date(),
        lastSync: null
      }
    });
    
    res.json({
      success: true,
      message: 'Google Fit connected successfully',
      data: {
        connected: true,
        connectedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Google Fit callback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to connect Google Fit',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Sync health data from Google Fit
// @route   POST /api/google-fit/sync
// @access  Private
router.post('/sync', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.body;
    
    // Get user's Google Fit tokens
    const user = await User.findById(userId);
    
    if (!user.integrations?.googleFit?.connected) {
      return res.status(400).json({
        success: false,
        message: 'Google Fit is not connected. Please connect first.'
      });
    }
    
    const tokens = {
      access_token: user.integrations.googleFit.accessToken,
      refresh_token: user.integrations.googleFit.refreshToken,
      expiry_date: user.integrations.googleFit.expiryDate
    };
    
    // Set date range (default to last 7 days)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    // Fetch health data from Google Fit
    const healthDataArray = await googleFitService.getHealthData(tokens, start, end);
    
    // Save health data to database
    const savedData = [];
    for (const dayData of healthDataArray) {
      const existingData = await HealthData.findOne({
        userId,
        date: {
          $gte: new Date(dayData.date).setHours(0, 0, 0, 0),
          $lt: new Date(dayData.date).setHours(23, 59, 59, 999)
        }
      });
      
      if (existingData) {
        // Update existing data
        Object.assign(existingData, dayData);
        existingData.lastModified = new Date();
        const updated = await existingData.save();
        savedData.push(updated);
      } else {
        // Create new data
        const newData = await HealthData.create({
          ...dayData,
          userId
        });
        savedData.push(newData);
      }
    }
    
    // Update last sync time
    await User.findByIdAndUpdate(userId, {
      'integrations.googleFit.lastSync': new Date()
    });
    
    res.json({
      success: true,
      message: `Successfully synced ${savedData.length} days of health data`,
      data: {
        syncedDays: savedData.length,
        dateRange: { start, end },
        lastSync: new Date(),
        healthData: savedData
      }
    });
    
  } catch (error) {
    console.error('Google Fit sync error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync Google Fit data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get Google Fit connection status
// @route   GET /api/google-fit/status
// @access  Private
router.get('/status', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findById(userId);
    
    const googleFitStatus = user.integrations?.googleFit || {
      connected: false,
      connectedAt: null,
      lastSync: null
    };
    
    res.json({
      success: true,
      data: {
        connected: googleFitStatus.connected,
        connectedAt: googleFitStatus.connectedAt,
        lastSync: googleFitStatus.lastSync,
        hasValidTokens: !!(googleFitStatus.accessToken && googleFitStatus.refreshToken)
      }
    });
  } catch (error) {
    console.error('Google Fit status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get Google Fit status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Disconnect Google Fit
// @route   DELETE /api/google-fit/disconnect
// @access  Private
router.delete('/disconnect', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Remove Google Fit integration from user
    await User.findByIdAndUpdate(userId, {
      $unset: {
        'integrations.googleFit': 1
      }
    });
    
    res.json({
      success: true,
      message: 'Google Fit disconnected successfully'
    });
  } catch (error) {
    console.error('Google Fit disconnect error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disconnect Google Fit',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get available data types from Google Fit
// @route   GET /api/google-fit/data-types
// @access  Private
router.get('/data-types', protect, async (req, res) => {
  try {
    const dataTypes = [
      {
        name: 'Steps',
        type: 'steps',
        description: 'Daily step count from your device',
        unit: 'steps',
        category: 'activity'
      },
      {
        name: 'Heart Rate',
        type: 'heart_rate',
        description: 'Heart rate measurements throughout the day',
        unit: 'bpm',
        category: 'vitals'
      },
      {
        name: 'Calories',
        type: 'calories',
        description: 'Calories burned during activities',
        unit: 'kcal',
        category: 'activity'
      },
      {
        name: 'Distance',
        type: 'distance',
        description: 'Distance traveled during activities',
        unit: 'km',
        category: 'activity'
      },
      {
        name: 'Active Minutes',
        type: 'active_minutes',
        description: 'Minutes spent in moderate to vigorous activity',
        unit: 'minutes',
        category: 'activity'
      },
      {
        name: 'Sleep',
        type: 'sleep',
        description: 'Sleep duration and quality metrics',
        unit: 'minutes',
        category: 'recovery'
      },
      {
        name: 'Weight',
        type: 'weight',
        description: 'Body weight measurements',
        unit: 'kg',
        category: 'body'
      },
      {
        name: 'Blood Pressure',
        type: 'blood_pressure',
        description: 'Systolic and diastolic blood pressure',
        unit: 'mmHg',
        category: 'vitals'
      },
      {
        name: 'Blood Oxygen',
        type: 'oxygen_saturation',
        description: 'Blood oxygen saturation levels',
        unit: '%',
        category: 'vitals'
      }
    ];
    
    res.json({
      success: true,
      data: {
        dataTypes,
        totalTypes: dataTypes.length,
        categories: ['activity', 'vitals', 'recovery', 'body']
      }
    });
  } catch (error) {
    console.error('Google Fit data types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get data types',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
