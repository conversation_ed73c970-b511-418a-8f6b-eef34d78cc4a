import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { useHealthData } from '../contexts/HealthDataContext';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const ActivityChart = ({ period = '7d' }) => {
  const { getActivitySummary } = useHealthData();
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('steps');

  useEffect(() => {
    fetchChartData();
  }, [period]);

  const fetchChartData = async () => {
    setLoading(true);
    const result = await getActivitySummary(period);
    
    if (result.success) {
      const data = result.data;
      prepareChartData(data.dailyData || []);
    }
    setLoading(false);
  };

  const prepareChartData = (dailyData) => {
    const labels = dailyData.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    });

    const datasets = [];

    if (selectedMetric === 'steps') {
      datasets.push({
        label: 'Steps',
        data: dailyData.map(item => item.steps || 0),
        borderColor: '#667eea',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#667eea',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      });
    } else if (selectedMetric === 'calories') {
      datasets.push({
        label: 'Calories',
        data: dailyData.map(item => item.calories || 0),
        borderColor: '#e74c3c',
        backgroundColor: 'rgba(231, 76, 60, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#e74c3c',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      });
    } else if (selectedMetric === 'activeMinutes') {
      datasets.push({
        label: 'Active Minutes',
        data: dailyData.map(item => item.activeMinutes || 0),
        borderColor: '#f39c12',
        backgroundColor: 'rgba(243, 156, 18, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#f39c12',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      });
    } else if (selectedMetric === 'all') {
      datasets.push(
        {
          label: 'Steps',
          data: dailyData.map(item => item.steps || 0),
          borderColor: '#667eea',
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          yAxisID: 'y',
          tension: 0.4
        },
        {
          label: 'Calories',
          data: dailyData.map(item => item.calories || 0),
          borderColor: '#e74c3c',
          backgroundColor: 'rgba(231, 76, 60, 0.1)',
          yAxisID: 'y1',
          tension: 0.4
        }
      );
    }

    setChartData({
      labels,
      datasets
    });
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            weight: '500'
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#2c3e50',
        bodyColor: '#2c3e50',
        borderColor: 'rgba(102, 126, 234, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            label += context.parsed.y.toLocaleString();
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#7f8c8d',
          font: {
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          color: '#7f8c8d',
          font: {
            size: 11
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      },
      ...(selectedMetric === 'all' && {
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          beginAtZero: true,
          grid: {
            drawOnChartArea: false,
          },
          ticks: {
            color: '#7f8c8d',
            font: {
              size: 11
            }
          }
        }
      })
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    elements: {
      point: {
        hoverRadius: 8
      }
    }
  };

  if (loading) {
    return (
      <div className="chart-loading">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  return (
    <div className="activity-chart">
      <div className="chart-controls">
        <select
          value={selectedMetric}
          onChange={(e) => setSelectedMetric(e.target.value)}
          className="form-select"
          style={{ width: 'auto', fontSize: '0.9rem' }}
        >
          <option value="steps">Steps</option>
          <option value="calories">Calories</option>
          <option value="activeMinutes">Active Minutes</option>
          <option value="all">All Metrics</option>
        </select>
      </div>

      <div className="chart-container">
        {chartData ? (
          <Line data={chartData} options={options} />
        ) : (
          <div className="no-data">
            <p>No activity data available</p>
          </div>
        )}
      </div>

      <style jsx>{`
        .activity-chart {
          height: 100%;
        }

        .chart-controls {
          margin-bottom: 20px;
          display: flex;
          justify-content: flex-end;
        }

        .chart-container {
          height: 300px;
          position: relative;
        }

        .chart-loading {
          height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .no-data {
          height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #7f8c8d;
          font-style: italic;
        }

        @media (max-width: 768px) {
          .chart-container {
            height: 250px;
          }
        }
      `}</style>
    </div>
  );
};

export default ActivityChart;
