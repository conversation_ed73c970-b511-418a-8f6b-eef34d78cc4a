/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { clouddebugger_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof clouddebugger_v2.Clouddebugger;
};
export declare function clouddebugger(version: 'v2'): clouddebugger_v2.Clouddebugger;
export declare function clouddebugger(options: clouddebugger_v2.Options): clouddebugger_v2.Clouddebugger;
declare const auth: AuthPlus;
export { auth };
export { clouddebugger_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
