const mongoose = require('mongoose');

const goalSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: [true, 'Goal title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    enum: ['steps', 'distance', 'calories', 'exercise', 'sleep', 'weight', 'heart_rate', 'custom'],
    required: true
  },
  type: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'yearly', 'one_time'],
    required: true
  },
  target: {
    value: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      required: true
    }
  },
  current: {
    value: {
      type: Number,
      default: 0,
      min: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'completed', 'paused', 'cancelled'],
    default: 'active'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  reminders: {
    enabled: {
      type: Boolean,
      default: true
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'custom'],
      default: 'daily'
    },
    time: {
      type: String, // HH:MM format
      default: '09:00'
    }
  },
  milestones: [{
    percentage: {
      type: Number,
      min: 0,
      max: 100
    },
    achieved: {
      type: Boolean,
      default: false
    },
    achievedDate: Date,
    reward: String
  }],
  achievements: [{
    date: {
      type: Date,
      default: Date.now
    },
    value: Number,
    note: String
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  tags: [String]
}, {
  timestamps: true
});

// Indexes
goalSchema.index({ userId: 1, status: 1 });
goalSchema.index({ userId: 1, category: 1 });
goalSchema.index({ endDate: 1, status: 1 });

// Virtual for progress percentage
goalSchema.virtual('progressPercentage').get(function() {
  if (!this.target.value || this.target.value === 0) return 0;
  return Math.min(Math.round((this.current.value / this.target.value) * 100), 100);
});

// Virtual for days remaining
goalSchema.virtual('daysRemaining').get(function() {
  if (!this.endDate) return null;
  const today = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

// Virtual for is overdue
goalSchema.virtual('isOverdue').get(function() {
  if (this.status === 'completed') return false;
  return new Date() > new Date(this.endDate);
});

// Method to update progress
goalSchema.methods.updateProgress = function(value, note = '') {
  this.current.value = value;
  this.current.lastUpdated = new Date();
  
  // Add achievement record
  this.achievements.push({
    date: new Date(),
    value: value,
    note: note
  });
  
  // Check if goal is completed
  if (value >= this.target.value && this.status === 'active') {
    this.status = 'completed';
  }
  
  // Check milestones
  const progressPercentage = (value / this.target.value) * 100;
  this.milestones.forEach(milestone => {
    if (!milestone.achieved && progressPercentage >= milestone.percentage) {
      milestone.achieved = true;
      milestone.achievedDate = new Date();
    }
  });
  
  return this.save();
};

// Static method to get user's active goals
goalSchema.statics.getActiveGoals = function(userId) {
  return this.find({
    userId: userId,
    status: 'active',
    endDate: { $gte: new Date() }
  }).sort({ priority: -1, createdAt: -1 });
};

// Static method to get goals summary
goalSchema.statics.getGoalsSummary = async function(userId) {
  const summary = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        categories: { $addToSet: '$category' }
      }
    }
  ]);
  
  const result = {
    total: 0,
    active: 0,
    completed: 0,
    paused: 0,
    cancelled: 0
  };
  
  summary.forEach(item => {
    result[item._id] = item.count;
    result.total += item.count;
  });
  
  return result;
};

// Pre-save middleware to set default milestones
goalSchema.pre('save', function(next) {
  if (this.isNew && this.milestones.length === 0) {
    // Add default milestones
    this.milestones = [
      { percentage: 25, reward: '25% Complete!' },
      { percentage: 50, reward: 'Halfway There!' },
      { percentage: 75, reward: '75% Complete!' },
      { percentage: 100, reward: 'Goal Achieved!' }
    ];
  }
  next();
});

module.exports = mongoose.model('Goal', goalSchema);
