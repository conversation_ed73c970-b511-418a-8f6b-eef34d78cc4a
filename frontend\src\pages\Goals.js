import React, { useState, useEffect } from 'react';
import { useHealthData } from '../contexts/HealthDataContext';

const Goals = () => {
  const { goals, fetchGoals, addGoal, updateGoalProgress } = useHealthData();
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'steps',
    type: 'daily',
    target: { value: '', unit: 'steps' },
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    priority: 'medium'
  });

  useEffect(() => {
    fetchGoals();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const result = await addGoal({
      ...formData,
      target: {
        value: parseInt(formData.target.value),
        unit: formData.target.unit
      }
    });
    if (result.success) {
      setShowAddForm(false);
      setFormData({
        title: '', description: '', category: 'steps', type: 'daily',
        target: { value: '', unit: 'steps' },
        startDate: new Date().toISOString().split('T')[0],
        endDate: '', priority: 'medium'
      });
    }
  };

  return (
    <div className="goals-page">
      <div className="page-header">
        <h1>Fitness Goals</h1>
        <button className="btn btn-primary" onClick={() => setShowAddForm(true)}>
          Create New Goal
        </button>
      </div>

      {showAddForm && (
        <div className="modal-overlay" onClick={() => setShowAddForm(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <h2>Create New Goal</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label className="form-label">Goal Title</label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  required
                  placeholder="e.g., Walk 10,000 steps daily"
                />
              </div>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">Category</label>
                  <select
                    className="form-select"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                  >
                    <option value="steps">Steps</option>
                    <option value="distance">Distance</option>
                    <option value="calories">Calories</option>
                    <option value="exercise">Exercise</option>
                    <option value="sleep">Sleep</option>
                    <option value="weight">Weight</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">Type</label>
                  <select
                    className="form-select"
                    value={formData.type}
                    onChange={(e) => setFormData({...formData, type: e.target.value})}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">Target Value</label>
                  <input
                    type="number"
                    className="form-input"
                    value={formData.target.value}
                    onChange={(e) => setFormData({
                      ...formData, 
                      target: {...formData.target, value: e.target.value}
                    })}
                    required
                    placeholder="e.g., 10000"
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Unit</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.target.unit}
                    onChange={(e) => setFormData({
                      ...formData, 
                      target: {...formData.target, unit: e.target.value}
                    })}
                    placeholder="e.g., steps, km, minutes"
                  />
                </div>
              </div>
              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Create Goal
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="goals-grid">
        {goals.map((goal) => (
          <div key={goal._id} className="goal-card">
            <div className="goal-header">
              <h3>{goal.title}</h3>
              <span className={`priority-badge priority-${goal.priority}`}>
                {goal.priority}
              </span>
            </div>
            <p className="goal-description">{goal.description}</p>
            <div className="goal-progress">
              <div className="progress-info">
                <span>{goal.current?.value || 0} / {goal.target?.value} {goal.target?.unit}</span>
                <span>{Math.round((goal.current?.value / goal.target?.value) * 100 || 0)}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${Math.min((goal.current?.value / goal.target?.value) * 100 || 0, 100)}%` }}
                />
              </div>
            </div>
            <div className="goal-meta">
              <span>📅 {goal.type}</span>
              <span>🏷️ {goal.category}</span>
              <span>⏰ {goal.daysRemaining || 0} days left</span>
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        .goals-page { padding: 0; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
        .page-header h1 { margin: 0; color: #2c3e50; }
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center; z-index: 1000; }
        .modal-content { background: white; border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; }
        .form-actions { display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px; }
        .goals-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .goal-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
        .goal-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }
        .goal-header h3 { margin: 0; color: #2c3e50; }
        .priority-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; }
        .priority-high { background: #e74c3c; color: white; }
        .priority-medium { background: #f39c12; color: white; }
        .priority-low { background: #27ae60; color: white; }
        .goal-description { color: #7f8c8d; margin-bottom: 20px; }
        .progress-info { display: flex; justify-content: space-between; margin-bottom: 8px; font-weight: 600; }
        .progress-bar { width: 100%; height: 8px; background: rgba(0, 0, 0, 0.1); border-radius: 4px; overflow: hidden; margin-bottom: 15px; }
        .progress-fill { height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px; transition: width 0.6s ease; }
        .goal-meta { display: flex; gap: 15px; font-size: 0.85rem; color: #7f8c8d; }
      `}</style>
    </div>
  );
};

export default Goals;
