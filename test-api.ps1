# Test HealthConnect API
Write-Host "Testing HealthConnect API..." -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Testing Health Endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method GET
    Write-Host "✅ Health Check: SUCCESS" -ForegroundColor Green
    Write-Host "   Status: $($healthResponse.status)" -ForegroundColor Cyan
    Write-Host "   Message: $($healthResponse.message)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Health Check: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Login
Write-Host "`n2. Testing Login Endpoint..." -ForegroundColor Yellow
$loginBody = @{
    email = "<EMAIL>"
    password = "demo123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginBody
    Write-Host "✅ Login: SUCCESS" -ForegroundColor Green
    Write-Host "   User: $($loginResponse.data.user.name)" -ForegroundColor Cyan
    Write-Host "   Email: $($loginResponse.data.user.email)" -ForegroundColor Cyan
    Write-Host "   Token: $($loginResponse.data.token.Substring(0, 20))..." -ForegroundColor Cyan
    
    $token = $loginResponse.data.token
} catch {
    Write-Host "❌ Login: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "   Response: $responseBody" -ForegroundColor Red
    }
    exit 1
}

# Test 3: Dashboard
Write-Host "`n3. Testing Dashboard Endpoint..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $dashboardResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/users/dashboard" -Method GET -Headers $headers
    Write-Host "✅ Dashboard: SUCCESS" -ForegroundColor Green
    Write-Host "   Today's Steps: $($dashboardResponse.data.today.steps)" -ForegroundColor Cyan
    Write-Host "   Today's Calories: $($dashboardResponse.data.today.calories)" -ForegroundColor Cyan
    Write-Host "   Active Goals: $($dashboardResponse.data.goals.active.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Dashboard: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 All API tests passed! Backend is working correctly." -ForegroundColor Green
Write-Host "`n📝 Summary:" -ForegroundColor Yellow
Write-Host "   ✅ Backend server is running on http://localhost:5000" -ForegroundColor Green
Write-Host "   ✅ Authentication is working" -ForegroundColor Green
Write-Host "   ✅ Protected routes are accessible" -ForegroundColor Green
Write-Host "   ✅ Demo data is available" -ForegroundColor Green

Write-Host "`n🔧 If the React app still doesn't work, the issue is likely:" -ForegroundColor Yellow
Write-Host "   1. CORS configuration" -ForegroundColor Cyan
Write-Host "   2. Frontend axios configuration" -ForegroundColor Cyan
Write-Host "   3. React Router navigation" -ForegroundColor Cyan
