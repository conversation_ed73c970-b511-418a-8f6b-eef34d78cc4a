/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { fcmdata_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof fcmdata_v1beta1.Fcmdata;
};
export declare function fcmdata(version: 'v1beta1'): fcmdata_v1beta1.Fcmdata;
export declare function fcmdata(options: fcmdata_v1beta1.Options): fcmdata_v1beta1.Fcmdata;
declare const auth: AuthPlus;
export { auth };
export { fcmdata_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
